# AI-Observable CodeCanvas Documentation

## Overview

The AI-Observable CodeCanvas is a comprehensive system that allows AI agents to monitor, analyze, and interact with students' coding activities in real-time. This system provides complete visibility into the learning process and enables intelligent assistance.

## Key Features

### 🔍 **Complete State Observation**
- **Code Content**: Real-time access to HTML, CSS, JavaScript, and TypeScript code
- **UI State**: Active tab, TypeScript/JavaScript mode, Tailwind CSS usage
- **Execution State**: Code running status, compilation results
- **Error Tracking**: Syntax errors, runtime errors, TypeScript compilation errors
- **Console Output**: All console messages with timestamps and context

### 📊 **User Behavior Analytics**
- **Keystroke Tracking**: Total keystrokes and typing patterns
- **Tab Switching**: Navigation patterns between different code sections
- **Code Execution**: Frequency and timing of code runs
- **Error Patterns**: Types and frequency of errors encountered
- **Time Tracking**: Time spent on different activities

### 📸 **Visual Analysis**
- **Screenshot Capture**: Full CodeCanvas screenshots for visual analysis
- **Preview Capture**: Specific preview iframe screenshots
- **Automatic Capture**: Periodic screenshots for progress tracking
- **Manual Capture**: On-demand screenshot generation

### 🤖 **AI Interaction System**
- **Real-time Suggestions**: Context-aware help and tips
- **Error Assistance**: Automatic debugging help when errors occur
- **Learning Guidance**: Personalized learning recommendations
- **Template Suggestions**: Smart code template recommendations

## Architecture

### Core Components

1. **AICodeCanvasContext**: Central state management for AI observation
2. **useAICodeCanvasObserver**: Hook for AI behavior analysis and suggestions
3. **AIAssistant**: UI component for AI interaction
4. **Enhanced CodeCanvas**: Main editor with AI observation capabilities

### Data Flow

```
CodeCanvas → AI Context → AI Observer → AI Assistant
     ↓           ↓            ↓           ↓
  User Actions → State Sync → Analysis → Suggestions
```

## Usage Examples

### Basic Setup

```tsx
import { AICodeCanvasProvider } from '../contexts/AICodeCanvasContext';
import CodeCanvas from './CodeCanvas';
import AIAssistant from './AIAssistant';

function MyApp() {
  return (
    <AICodeCanvasProvider>
      <CodeCanvas enableAIObservation={true} />
      <AIAssistant />
    </AICodeCanvasProvider>
  );
}
```

### Advanced AI Observer Configuration

```tsx
import { useAICodeCanvasObserver } from '../hooks/useAICodeCanvasObserver';

function CustomAIComponent() {
  const observer = useAICodeCanvasObserver({
    enableAutoSuggestions: true,
    suggestionThreshold: {
      errorCount: 2,
      timeWithoutProgress: 20000,
      keystrokesWithoutExecution: 50
    },
    analysisInterval: 3000,
    screenshotInterval: 15000
  });

  return (
    <div>
      <h3>AI Analysis</h3>
      <p>Complexity: {observer.currentAnalysis.complexity}%</p>
      <p>Error Rate: {observer.currentAnalysis.errorRate}%</p>
      <button onClick={observer.captureScreenshot}>
        Capture Screenshot
      </button>
    </div>
  );
}
```

### Custom AI Observer

```tsx
import { AIObserver } from '../components/CodeCanvas';

const customAIObserver: AIObserver = {
  onStateChange: (state) => {
    console.log('Code state changed:', state);
    // Send to your AI service
    sendToAIService(state);
  },
  
  onUserAction: (action) => {
    console.log('User action:', action);
    // Analyze user behavior
    analyzeUserBehavior(action);
  },
  
  onScreenshotReady: (screenshot) => {
    console.log('Screenshot captured');
    // Send screenshot to AI for visual analysis
    analyzeScreenshot(screenshot);
  },
  
  getCurrentState: () => ({}), // Will be set by CodeCanvas
  captureScreenshot: async () => '', // Will be set by CodeCanvas
  
  suggestAction: (suggestion) => {
    // Handle AI suggestions
    displaySuggestion(suggestion);
  }
};

<CodeCanvas 
  aiObserver={customAIObserver}
  enableAIObservation={true}
/>
```

## AI Observable Data Structure

### AICodeCanvasState
```typescript
interface AICodeCanvasState {
  // Code content
  html: string;
  css: string;
  js: string;
  ts: string;
  compiledJs: string;
  
  // UI state
  activeTab: Tab;
  useTypeScript: boolean;
  useTailwind: boolean;
  isExecuting: boolean;
  
  // Errors and console
  errors: CodeError[];
  tsErrors: CodeError[];
  consoleLogs: ConsoleMessage[];
  
  // User interaction tracking
  lastModified: Date;
  totalKeystrokes: number;
  tabSwitches: number;
  codeExecutions: number;
  errorsEncountered: number;
}
```

### AIUserAction
```typescript
interface AIUserAction {
  type: 'code_change' | 'tab_switch' | 'code_execution' | 'template_load' | 'error_occurred' | 'console_output';
  timestamp: Date;
  details: any;
  context: Partial<AICodeCanvasState>;
}
```

## AI Analysis Capabilities

### Code Analysis
- **Complexity Scoring**: Based on code length and structure
- **Error Rate Calculation**: Percentage of executions with errors
- **Productivity Metrics**: Keystroke efficiency and progress rate
- **Struggling Area Detection**: Identifies problem areas (syntax, logic, focus)

### Behavioral Analysis
- **Learning Patterns**: How students approach problems
- **Error Recovery**: How quickly students fix errors
- **Help-Seeking Behavior**: When students need assistance
- **Code Quality Trends**: Improvement over time

### Visual Analysis
- **UI State Recognition**: Understanding current interface state
- **Preview Analysis**: Analyzing rendered output
- **Progress Visualization**: Visual representation of coding progress

## Integration with External AI Services

### OpenAI Integration Example
```typescript
async function analyzeWithOpenAI(state: AICodeCanvasState, screenshot: string) {
  const prompt = `
    Analyze this student's coding session:
    - Code: ${JSON.stringify({html: state.html, css: state.css, js: state.js})}
    - Errors: ${state.errors.length}
    - Keystrokes: ${state.totalKeystrokes}
    - Screenshot: [base64 image data]
    
    Provide helpful suggestions for improvement.
  `;
  
  const response = await openai.chat.completions.create({
    model: "gpt-4-vision-preview",
    messages: [{
      role: "user",
      content: [
        { type: "text", text: prompt },
        { type: "image_url", image_url: { url: screenshot } }
      ]
    }]
  });
  
  return response.choices[0].message.content;
}
```

## Best Practices

### Performance Optimization
- Use debounced updates for frequent state changes
- Limit screenshot frequency to avoid performance issues
- Batch user actions for efficient processing
- Clean up old data periodically

### Privacy Considerations
- Always inform users about AI observation
- Provide opt-out mechanisms
- Anonymize data when possible
- Secure data transmission and storage

### Educational Effectiveness
- Provide contextual, not intrusive suggestions
- Focus on learning outcomes, not just error correction
- Encourage exploration and experimentation
- Adapt to individual learning styles

## Troubleshooting

### Common Issues
1. **Screenshots not capturing**: Ensure html2canvas is installed
2. **Context not updating**: Check if AICodeCanvasProvider wraps components
3. **Suggestions not appearing**: Verify observer configuration
4. **Performance issues**: Reduce analysis frequency

### Debug Mode
```tsx
<CodeCanvas 
  enableAIObservation={true}
  aiObserver={{
    ...customObserver,
    onStateChange: (state) => {
      console.log('Debug - State:', state);
      customObserver.onStateChange(state);
    }
  }}
/>
```

This AI-Observable CodeCanvas system provides a comprehensive foundation for building intelligent coding education tools that can understand, analyze, and assist students in their learning journey.
