# CodeCanvas Usage Guide

## Overview

The CodeCanvas component can be used in two modes:
1. **Standalone Mode**: Basic code editor without AI observation
2. **AI-Observable Mode**: Full AI monitoring and assistance capabilities

## 🔧 Standalone Mode (Current Default)

Use this when you just need a code editor without AI features:

```tsx
import CodeCanvasStandalone from '../components/CodeCanvasStandalone';

function MyPage() {
  return (
    <div>
      <h1>Code Editor</h1>
      <CodeCanvasStandalone />
    </div>
  );
}
```

**Features Available:**
- ✅ HTML, CSS, JavaScript, TypeScript editing
- ✅ Live preview with iframe
- ✅ Syntax highlighting
- ✅ Error detection and display
- ✅ Console output capture
- ✅ Code templates and snippets
- ✅ Save/load functionality
- ✅ Tailwind CSS support
- ✅ TypeScript compilation

**What's NOT included:**
- ❌ AI behavior tracking
- ❌ AI suggestions and assistance
- ❌ Screenshot capture for AI analysis
- ❌ Learning analytics

## 🤖 AI-Observable Mode

Use this when you want full AI monitoring and assistance:

```tsx
import { AICodeCanvasProvider } from '../contexts/AICodeCanvasContext';
import CodeCanvas from '../components/CodeCanvas';
import AIAssistant from '../components/AIAssistant';

function AIEnabledPage() {
  return (
    <AICodeCanvasProvider>
      <div>
        <h1>AI-Powered Code Editor</h1>
        <CodeCanvas enableAIObservation={true} />
        <AIAssistant />
      </div>
    </AICodeCanvasProvider>
  );
}
```

**Additional Features:**
- ✅ All standalone features PLUS:
- ✅ Real-time AI behavior monitoring
- ✅ Intelligent suggestions and help
- ✅ Screenshot capture for visual analysis
- ✅ Learning analytics and progress tracking
- ✅ Context-aware assistance
- ✅ Error pattern recognition

## 🚀 Quick Start Examples

### Example 1: Simple Landing Page (Current Usage)
```tsx
// This is what's currently used in LandingPage.tsx
import CodeCanvasStandalone from '../components/CodeCanvasStandalone';

function LandingPage() {
  return (
    <section>
      <h2>Try Our Code Editor</h2>
      <CodeCanvasStandalone />
    </section>
  );
}
```

### Example 2: Full AI-Powered Learning Environment
```tsx
import { useState } from 'react';
import { AICodeCanvasProvider } from '../contexts/AICodeCanvasContext';
import CodeCanvas from '../components/CodeCanvas';
import AIAssistant from '../components/AIAssistant';

function LearningEnvironment() {
  const [showAI, setShowAI] = useState(false);

  return (
    <AICodeCanvasProvider>
      <div className="learning-environment">
        <header>
          <h1>Interactive Learning Platform</h1>
          <button onClick={() => setShowAI(!showAI)}>
            {showAI ? 'Hide' : 'Show'} AI Assistant
          </button>
        </header>
        
        <main>
          <CodeCanvas enableAIObservation={true} />
        </main>
        
        {showAI && (
          <AIAssistant 
            isVisible={showAI}
            onToggle={() => setShowAI(!showAI)}
          />
        )}
      </div>
    </AICodeCanvasProvider>
  );
}
```

### Example 3: Custom AI Observer
```tsx
import { AIObserver } from '../components/CodeCanvas';

const customAIObserver: AIObserver = {
  onStateChange: (state) => {
    // Send to your AI backend
    fetch('/api/ai/analyze', {
      method: 'POST',
      body: JSON.stringify(state)
    });
  },
  
  onUserAction: (action) => {
    console.log('User did:', action.type, action.details);
  },
  
  onScreenshotReady: (screenshot) => {
    // Send screenshot to AI vision service
    analyzeScreenshot(screenshot);
  },
  
  getCurrentState: () => ({}), // Set by CodeCanvas
  captureScreenshot: async () => '', // Set by CodeCanvas
  
  suggestAction: (suggestion) => {
    // Show custom suggestion UI
    showCustomSuggestion(suggestion);
  }
};

function CustomAIPage() {
  return (
    <CodeCanvas 
      aiObserver={customAIObserver}
      enableAIObservation={true}
    />
  );
}
```

## 🔄 Migration Guide

### From Current Usage to AI-Enabled

**Step 1**: Wrap with Provider
```tsx
// Before
<CodeCanvasStandalone />

// After
<AICodeCanvasProvider>
  <CodeCanvas enableAIObservation={true} />
</AICodeCanvasProvider>
```

**Step 2**: Add AI Assistant (Optional)
```tsx
<AICodeCanvasProvider>
  <CodeCanvas enableAIObservation={true} />
  <AIAssistant />
</AICodeCanvasProvider>
```

**Step 3**: Use AI Observer Hook (Optional)
```tsx
import { useAICodeCanvasObserver } from '../hooks/useAICodeCanvasObserver';

function MyComponent() {
  const observer = useAICodeCanvasObserver({
    enableAutoSuggestions: true,
    analysisInterval: 5000
  });
  
  return (
    <div>
      <p>Complexity: {observer.currentAnalysis.complexity}%</p>
      <button onClick={observer.captureScreenshot}>
        Take Screenshot
      </button>
    </div>
  );
}
```

## 📊 AI Features Deep Dive

### What AI Can Observe:
- **Code Content**: All HTML, CSS, JS, TS code in real-time
- **User Behavior**: Keystrokes, tab switches, execution frequency
- **Errors**: All syntax and runtime errors with context
- **Console Output**: All console messages with timestamps
- **Visual State**: Screenshots of the interface and preview

### What AI Can Do:
- **Provide Suggestions**: Context-aware help and tips
- **Debug Assistance**: Help fix errors and improve code
- **Learning Guidance**: Personalized learning recommendations
- **Progress Tracking**: Monitor learning progress over time
- **Visual Analysis**: Understand what the student sees

### AI Analysis Metrics:
- **Complexity Score**: Based on code structure and length
- **Error Rate**: Percentage of executions with errors
- **Productivity Score**: Efficiency of coding process
- **Struggling Areas**: Identified problem areas
- **Learning Patterns**: How the student approaches problems

## 🛠️ Troubleshooting

### Error: "useAICodeCanvas must be used within an AICodeCanvasProvider"
**Solution**: Either:
1. Use `CodeCanvasStandalone` for basic functionality
2. Wrap your component with `AICodeCanvasProvider` for AI features

### AI Features Not Working
**Check**:
1. Is `enableAIObservation={true}` set?
2. Is the component wrapped with `AICodeCanvasProvider`?
3. Are there any console errors?

### Performance Issues
**Solutions**:
1. Reduce `analysisInterval` in observer config
2. Limit screenshot frequency
3. Use `enableAIObservation={false}` if AI features aren't needed

## 📝 Best Practices

1. **Use Standalone Mode** for simple demos and landing pages
2. **Use AI Mode** for educational platforms and learning environments
3. **Wrap with Provider** only when AI features are needed
4. **Configure Observer** based on your specific use case
5. **Handle Errors Gracefully** with try-catch blocks
6. **Optimize Performance** by adjusting analysis intervals

This guide should help you choose the right approach for your specific use case!
