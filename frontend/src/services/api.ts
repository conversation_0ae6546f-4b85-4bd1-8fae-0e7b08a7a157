// API Configuration and Service
const API_BASE_URL = 'http://localhost:3001/api/v1';

// Types
interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

interface User {
  id: string;
  email: string;
  name: string;
  [key: string]: any;
}

interface Subject {
  id: string;
  title: string;
  description?: string;
  [key: string]: any;
}

interface UniversityCourse {
  id: string;
  title: string;
  description?: string;
  modules?: any[];
  [key: string]: any;
}

interface RequestOptions extends RequestInit {
  headers?: Record<string, string>;
}

class ApiService {
  private baseURL: string;
  private token: string | null;

  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('authToken');
  }

  // Set authentication token
  setToken(token: string | null): void {
    this.token = token;
    if (token) {
      localStorage.setItem('authToken', token);
    } else {
      localStorage.removeItem('authToken');
    }
  }

  // Get authentication token
  getToken(): string | null {
    return this.token || localStorage.getItem('authToken');
  }

  // Clear authentication
  clearAuth(): void {
    this.token = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
  }

  // Make HTTP request with authentication
  async request<T = any>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const token = this.getToken();

    const config: RequestOptions = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include', // Include cookies for session management
      ...options,
    };

    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, config);

      // Handle different response types
      let data: any;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = { message: await response.text() };
      }

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error: any) {
      console.error('API request failed:', error);

      // Handle authentication errors
      if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
        this.clearAuth();
        window.location.href = '/login';
      }

      throw error;
    }
  }

  // GET request
  async get<T = any>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  // POST request
  async post<T = any>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT request
  async put<T = any>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE request
  async delete<T = any>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // Authentication methods
  async register(userData: any): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await this.post<ApiResponse<{ user: User; token: string }>>('/auth/register', userData);
    if (response.success && response.data.token) {
      this.setToken(response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    return response;
  }

  async login(credentials: any): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await this.post<ApiResponse<{ user: User; token: string }>>('/auth/login', credentials);
    if (response.success && response.data.token) {
      this.setToken(response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    return response;
  }

  async logout(): Promise<void> {
    try {
      await this.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearAuth();
    }
  }

  async getProfile(): Promise<ApiResponse<User>> {
    return this.get<ApiResponse<User>>('/users/profile');
  }

  async updateProfile(profileData: any): Promise<ApiResponse<User>> {
    return this.put<ApiResponse<User>>('/users/profile', profileData);
  }

  // Subject methods
  async getSubjects(params: Record<string, string> = {}): Promise<ApiResponse<Subject[]>> {
    const queryString = new URLSearchParams(params).toString();
    return this.get<ApiResponse<Subject[]>>(`/subjects${queryString ? `?${queryString}` : ''}`);
  }

  async getSubject(id: string): Promise<ApiResponse<Subject>> {
    return this.get<ApiResponse<Subject>>(`/subjects/${id}`);
  }

  async createSubject(subjectData: any): Promise<ApiResponse<Subject>> {
    return this.post<ApiResponse<Subject>>('/subjects', subjectData);
  }

  async updateSubject(id: string, subjectData: any): Promise<ApiResponse<Subject>> {
    return this.put<ApiResponse<Subject>>(`/subjects/${id}`, subjectData);
  }

  async deleteSubject(id: string): Promise<ApiResponse<void>> {
    return this.delete<ApiResponse<void>>(`/subjects/${id}`);
  }

  async generateCurriculum(subjectId: string, data: any): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>(`/subjects/${subjectId}/generate-curriculum`, data);
  }

  async updateSubjectProgress(subjectId: string, progressData: any): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>(`/subjects/${subjectId}/progress`, progressData);
  }

  async getSubjectProgress(subjectId: string): Promise<ApiResponse<any>> {
    return this.get<ApiResponse<any>>(`/subjects/${subjectId}/progress`);
  }

  async getSubjectStats(subjectId: string): Promise<ApiResponse<any>> {
    return this.get<ApiResponse<any>>(`/subjects/${subjectId}/stats`);
  }

  // University Course methods
  async generateUniversityCourse(data: any): Promise<ApiResponse<UniversityCourse>> {
    return this.post<ApiResponse<UniversityCourse>>('/university-courses/generate', data);
  }

  async createUniversityCourse(courseData: any): Promise<ApiResponse<UniversityCourse>> {
    return this.post<ApiResponse<UniversityCourse>>('/university-courses', courseData);
  }

  async getUniversityCourses(params: Record<string, string> = {}): Promise<ApiResponse<UniversityCourse[]>> {
    const queryString = new URLSearchParams(params).toString();
    return this.get<ApiResponse<UniversityCourse[]>>(`/university-courses${queryString ? `?${queryString}` : ''}`);
  }

  async getUniversityCourse(id: string): Promise<ApiResponse<UniversityCourse>> {
    return this.get<ApiResponse<UniversityCourse>>(`/university-courses/${id}`);
  }

  async updateUniversityCourse(id: string, courseData: any): Promise<ApiResponse<UniversityCourse>> {
    return this.put<ApiResponse<UniversityCourse>>(`/university-courses/${id}`, courseData);
  }

  async deleteUniversityCourse(id: string): Promise<ApiResponse<void>> {
    return this.delete<ApiResponse<void>>(`/university-courses/${id}`);
  }

  async updateUniversityCourseProgress(courseId: string, moduleId: string, progressData: any): Promise<ApiResponse<any>> {
    return this.post<ApiResponse<any>>(`/university-courses/${courseId}/progress/${moduleId}`, progressData);
  }

  async getUniversityCourseStats(courseId: string): Promise<ApiResponse<any>> {
    return this.get<ApiResponse<any>>(`/university-courses/${courseId}/stats`);
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService;
