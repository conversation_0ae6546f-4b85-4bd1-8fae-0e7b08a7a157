// Shared TypeScript type definitions for QtMaster frontend

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  [key: string]: any;
}

// Subject types
export interface Subject {
  id: string;
  title: string;
  description?: string;
  has_curriculum?: boolean;
  created_at?: string;
  [key: string]: any;
}

// Topic and Curriculum types
export interface Topic {
  id: string;
  title: string;
  [key: string]: any;
}

export interface Category {
  id: string;
  title: string;
  topics: Topic[];
  [key: string]: any;
}

export type Curriculum = Category[];

// University Course types
export interface UniversityCourse {
  id: string;
  title: string;
  description?: string;
  modules?: any[];
  [key: string]: any;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

// Auth types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  [key: string]: any;
}

export interface AuthResponse {
  success: boolean;
  error?: string;
}

// Language types
export interface Language {
  code: string;
  name: string;
  nativeName: string;
}

// Form types
export interface FormErrors {
  [key: string]: string;
}

// Component prop types
export interface DashboardProps {
  onSubjectSelect: (subject: Subject) => void;
  onCreateSubject: () => void;
  onCreateUniversityCourse?: () => void;
}

export interface SubjectCreationProps {
  onBack: () => void;
  onSubjectCreated: (subject: Subject) => void;
  apiKey: string;
}

export interface SubjectLearningProps {
  subject: Subject | null;
  onBack: () => void;
  apiKey: string;
}

export interface TopicTreeProps {
  curriculum: Curriculum | null;
  onTopicSelect: (topic: Topic) => void;
  selectedTopic: Topic | null;
  subjectTitle?: string;
}

export interface ContentDisplayProps {
  content: string;
  isLoading: boolean;
  selectedTopic: Topic | null;
}

export interface ProtectedRouteProps {
  children: React.ReactNode;
}

// Context types
export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (credentials: LoginCredentials) => Promise<AuthResponse>;
  register: (userData: RegisterData) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  updateProfile: (profileData: any) => Promise<AuthResponse>;
}

export interface LanguageContextType {
  selectedLanguage: Language;
  changeLanguage: (languageCode: string) => void;
  supportedLanguages: Language[];
  getLanguageByCode: (code: string) => Language | undefined;
  isLanguageSupported: (code: string) => boolean;
}

export interface AuthProviderProps {
  children: React.ReactNode;
}

export interface LanguageProviderProps {
  children: React.ReactNode;
}

// Status types
export type Status = 'processing' | 'success' | 'error';
export type ViewType = 'dashboard' | 'create-subject' | 'learning';
