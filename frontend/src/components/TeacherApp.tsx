import React, { useState, useEffect } from 'react';
import Header from './Header';
import Dashboard from './Dashboard';
import SubjectCreation from './SubjectCreation';
import SubjectLearning from './SubjectLearning';
import SettingsModal from './SettingsModal';
import LanguageSettings from './LanguageSettings';
import UniversityCourseCreation from './UniversityCourseCreation';
import { LanguageProvider } from '../contexts/LanguageContext';

// Types
interface Subject {
  id: string;
  title: string;
  description?: string;
  has_curriculum?: boolean;
  [key: string]: any;
}

function App() {
  const [currentView, setCurrentView] = useState('dashboard'); // 'dashboard', 'create-subject', 'learning', 'create-university-course'
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [apiKey, setApiKey] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [showLanguageSettings, setShowLanguageSettings] = useState(false);

  useEffect(() => {
    // Load API key from localStorage on component mount
    const savedApiKey = localStorage.getItem('qtmaster_api_key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
    }
  }, []);

  const handleSubjectSelect = (subject: Subject) => {
    setSelectedSubject(subject);
    setCurrentView('learning');
  };

  const handleCreateSubject = () => {
    setCurrentView('create-subject');
  };

  const handleCreateUniversityCourse = () => {
    setCurrentView('create-university-course');
  };

  const handleSubjectCreated = (newSubject: Subject) => {
    setSelectedSubject(newSubject);
    setCurrentView('learning');
  };

  const handleBackToDashboard = () => {
    setSelectedSubject(null);
    setCurrentView('dashboard');
  };

  const handleSaveApiKey = (key: string) => {
    setApiKey(key);
    localStorage.setItem('qtmaster_api_key', key);
    setShowSettings(false);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return (
          <Dashboard
            onSubjectSelect={handleSubjectSelect}
            onCreateSubject={handleCreateSubject}
            onCreateUniversityCourse={handleCreateUniversityCourse}
          />
        );
      case 'create-subject':
        return (
          <SubjectCreation
            onBack={handleBackToDashboard}
            onSubjectCreated={handleSubjectCreated}
            apiKey={apiKey}
          />
        );
      case 'create-university-course':
        return (
          <UniversityCourseCreation
            onBack={handleBackToDashboard}
            onCourseCreated={handleBackToDashboard}
          />
        );
      case 'learning':
        return (
          <SubjectLearning
            subject={selectedSubject}
            onBack={handleBackToDashboard}
            apiKey={apiKey}
          />
        );
      default:
        return <Dashboard onSubjectSelect={handleSubjectSelect} onCreateSubject={handleCreateSubject} onCreateUniversityCourse={handleCreateUniversityCourse} />;
    }
  };

  return (
    <LanguageProvider>
      <div className="min-h-screen bg-gray-100">
        {currentView === 'dashboard' && (
          <Header
            onSettingsClick={() => setShowSettings(true)}
            onLanguageClick={() => setShowLanguageSettings(true)}
          />
        )}

        <main>
          {renderCurrentView()}
        </main>

        {showSettings && (
          <SettingsModal
            onClose={() => setShowSettings(false)}
            onSave={handleSaveApiKey}
            initialApiKey={apiKey}
          />
        )}

        {showLanguageSettings && (
          <LanguageSettings
            isOpen={showLanguageSettings}
            onClose={() => setShowLanguageSettings(false)}
          />
        )}
      </div>
    </LanguageProvider>
  );
}

export default App;