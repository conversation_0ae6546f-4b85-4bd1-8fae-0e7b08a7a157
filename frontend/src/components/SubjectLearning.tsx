import React, { useState, useEffect } from 'react';
import axios from 'axios';
import TopicTree from './TopicTree';
import ContentDisplay from './ContentDisplay';
import { useLanguage } from '../contexts/LanguageContext';
import { Category, Topic } from '../types';

// Types
interface Subject {
  id: string;
  title: string;
  description?: string;
  [key: string]: any;
}

interface SubjectLearningProps {
  subject: Subject | null;
  onBack: () => void;
  apiKey: string;
}

const SubjectLearning: React.FC<SubjectLearningProps> = ({ subject, onBack, apiKey }) => {
  const { selectedLanguage } = useLanguage();
  const [curriculum, setCurriculum] = useState<Category[] | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [courseContent, setCourseContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingCurriculum, setIsLoadingCurriculum] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (subject?.id) {
      fetchCurriculum();
    }
  }, [subject?.id]);

  const fetchCurriculum = async (): Promise<void> => {
    if (!subject?.id) return;

    try {
      setIsLoadingCurriculum(true);
      const response = await axios.get(`http://localhost:3001/api/curricula/${subject.id}`);
      setCurriculum(response.data.structure.curriculum);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching curriculum:', error);
      setError('Failed to load curriculum. Please try again.');
    } finally {
      setIsLoadingCurriculum(false);
    }
  };

  const handleTopicSelect = (topic: Topic): void => {
    setSelectedTopic(topic);
    setCourseContent(''); // Clear previous content when selecting a new topic
  };

  const handleGenerateCourse = async (): Promise<void> => {
    if (!selectedTopic) {
      alert('Please select a topic first');
      return;
    }

    if (!apiKey) {
      alert('Please set your API key in the settings');
      return;
    }

    if (!subject?.id) {
      alert('Subject not found');
      return;
    }

    setIsLoading(true);
    try {
      const response = await axios.post('http://localhost:3001/api/generate', {
        topic: selectedTopic.title,
        subjectId: subject.id,
        topicId: selectedTopic.id,
        apiKey: apiKey,
        language: selectedLanguage.name
      });

      setCourseContent(response.data.content);
    } catch (error: any) {
      console.error('Error generating course:', error);
      if (error.response) {
        alert(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else if (error.request) {
        alert('Error: No response from server. Make sure the backend is running.');
      } else {
        alert(`Error: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!subject) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <h2 className="text-2xl font-bold mb-4">Error</h2>
        <p>Subject not found</p>
        <button onClick={onBack} className="mt-4 px-4 py-2 rounded-md text-white bg-blue-600 hover:bg-blue-700">
          Back to Dashboard
        </button>
      </div>
    );
  }

  if (isLoadingCurriculum) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500"></div>
        <p className="mt-4 text-lg">Loading curriculum...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <h2 className="text-2xl font-bold mb-4 text-red-600">Error</h2>
        <p>{error}</p>
        <div className="mt-4 space-x-4">
          <button onClick={fetchCurriculum} className="px-4 py-2 rounded-md text-white bg-blue-600 hover:bg-blue-700">
            Try Again
          </button>
          <button onClick={onBack} className="px-4 py-2 rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300">
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="bg-white shadow-md p-4 flex items-center">
        <button onClick={onBack} className="text-blue-600 hover:underline">
          ← Back to Dashboard
        </button>
        <div className="ml-4">
          <h1 className="text-2xl font-bold">{subject.title}</h1>
          {subject.description && <p className="text-gray-600">{subject.description}</p>}
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden">
        <aside className="w-1/4 bg-gray-50 p-4 overflow-y-auto">
          {curriculum && <TopicTree 
            curriculum={curriculum} 
            onTopicSelect={handleTopicSelect}
            selectedTopic={selectedTopic}
            subjectTitle={subject.title}
          />}
        </aside>
        
        <main className="flex-1 p-6 overflow-y-auto">
          {selectedTopic && (
            <div className="mb-4">
              <button 
                className="w-full px-4 py-2 rounded-md text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                onClick={handleGenerateCourse}
                disabled={isLoading}
              >
                {isLoading ? 'Generating...' : 'Generate New Course'}
              </button>
            </div>
          )}
          <ContentDisplay 
            content={courseContent} 
            isLoading={isLoading}
            selectedTopic={selectedTopic}
          />
        </main>
      </div>
    </div>
  );
};

export default SubjectLearning;