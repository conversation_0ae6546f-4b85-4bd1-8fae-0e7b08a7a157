import React, { useState, useEffect } from 'react';

const GENERATION_STAGES = [
  {
    id: 'analyzing',
    title: 'Analyzing Your Learning Sketch',
    description: 'Our AI is reading and understanding your learning goals, identifying key topics and skill areas.',
    icon: '🔍',
    duration: 3000,
    progress: 15
  },
  {
    id: 'personas',
    title: 'Selecting AI Specialists',
    description: 'Choosing the best AI personas for each topic area - from programming experts to domain specialists.',
    icon: '🤖',
    duration: 4000,
    progress: 30
  },
  {
    id: 'structure',
    title: 'Designing Course Architecture',
    description: 'Creating the optimal learning path with progressive modules and clear prerequisites.',
    icon: '🏗️',
    duration: 5000,
    progress: 50
  },
  {
    id: 'curricula',
    title: 'Generating Detailed Curricula',
    description: 'Each AI specialist is creating comprehensive learning materials for their expertise area.',
    icon: '📚',
    duration: 20000,
    progress: 85
  },
  {
    id: 'finalizing',
    title: 'Finalizing Your Course',
    description: 'Integrating all modules, setting learning objectives, and preparing your personalized curriculum.',
    icon: '✨',
    duration: 3000,
    progress: 100
  }
];

interface CourseGenerationLoaderProps {
  isVisible: boolean;
  onComplete: () => void;
}

function CourseGenerationLoader({ isVisible, onComplete }: CourseGenerationLoaderProps) {
  const [currentStageIndex, setCurrentStageIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    if (isVisible && !startTime) {
      setStartTime(Date.now());
      setCurrentStageIndex(0);
      setProgress(0);
    }
  }, [isVisible, startTime]);

  useEffect(() => {
    if (!isVisible || !startTime) return;

    const timer = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [isVisible, startTime]);

  useEffect(() => {
    if (!isVisible) return;

    let timeoutId: number;
    
    const advanceStage = () => {
      if (currentStageIndex < GENERATION_STAGES.length - 1) {
        const currentStage = GENERATION_STAGES[currentStageIndex];
        setProgress(currentStage.progress);
        
        timeoutId = setTimeout(() => {
          setCurrentStageIndex(prev => prev + 1);
        }, currentStage.duration);
      } else {
        setProgress(100);
        if (onComplete) {
          setTimeout(onComplete, 1000);
        }
      }
    };

    advanceStage();

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [currentStageIndex, isVisible, onComplete]);

  if (!isVisible) return null;

  const currentStage = GENERATION_STAGES[currentStageIndex];
  const estimatedTotal = GENERATION_STAGES.reduce((sum, stage) => sum + stage.duration, 0) / 1000;

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-2xl p-8 max-w-2xl w-full mx-4">
        <div className="text-center mb-6">
          <div className="text-5xl mb-4">🎓</div>
          <h2 className="text-2xl font-bold text-gray-800">Creating Your University Course</h2>
          <p className="text-gray-600 mt-2">Our AI specialists are working together to build your personalized curriculum</p>
        </div>

        <div className="mb-6">
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="flex justify-between text-sm text-gray-600 mt-2">
            <span>{progress}%</span>
            <span>
              {elapsedTime}s / ~{estimatedTotal}s
            </span>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 flex items-center mb-6">
          <div className="text-3xl mr-4">{currentStage.icon}</div>
          <div>
            <h3 className="font-bold text-gray-800">{currentStage.title}</h3>
            <p className="text-gray-600 text-sm">{currentStage.description}</p>
          </div>
        </div>

        <div className="space-y-3 mb-6">
          {GENERATION_STAGES.map((stage, index) => (
            <div 
              key={stage.id}
              className={`flex items-center p-2 rounded-md transition-all duration-300 ${
                index < currentStageIndex ? 'bg-green-100 text-green-800' :
                index === currentStageIndex ? 'bg-blue-100 text-blue-800 scale-105' :
                'bg-gray-100 text-gray-500'
              }`}
            >
              <div className="text-xl mr-3">{stage.icon}</div>
              <span className="font-medium text-sm">{stage.title}</span>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center text-xs text-gray-600 mb-6">
          <div className="bg-gray-100 p-2 rounded-lg">
            <span className="text-lg">🧠</span>
            <p>Multiple AI specialists working in parallel</p>
          </div>
          <div className="bg-gray-100 p-2 rounded-lg">
            <span className="text-lg">📖</span>
            <p>University-level curriculum quality</p>
          </div>
          <div className="bg-gray-100 p-2 rounded-lg">
            <span className="text-lg">🌍</span>
            <p>Content tailored to your language</p>
          </div>
        </div>

        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4">
          <p className="font-bold">Tip:</p>
          <p>The more detailed your learning sketch, the more personalized your course will be!</p>
        </div>
      </div>
    </div>
  );
}

export default CourseGenerationLoader;