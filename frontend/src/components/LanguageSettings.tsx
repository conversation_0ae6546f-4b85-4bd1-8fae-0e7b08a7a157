import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

interface LanguageSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

function LanguageSettings({ isOpen, onClose }: LanguageSettingsProps) {
  const { selectedLanguage, changeLanguage, supportedLanguages } = useLanguage();
  const [tempSelectedLanguage, setTempSelectedLanguage] = useState(selectedLanguage.code);

  if (!isOpen) return null;

  const handleSave = () => {
    changeLanguage(tempSelectedLanguage);
    onClose();
  };

  const handleCancel = () => {
    setTempSelectedLanguage(selectedLanguage.code);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-xl font-semibold">Language Settings</h2>
          <button className="text-gray-500 hover:text-gray-800" onClick={handleCancel}>
            ×
          </button>
        </div>
        
        <div className="p-6">
          <p className="text-gray-600 mb-4">
            Select your preferred language for AI-generated content. This will affect curriculum generation and course content.
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {supportedLanguages.map((language) => (
              <label key={language.code} className="p-4 border rounded-lg cursor-pointer hover:bg-gray-100 has-[:checked]:bg-blue-100 has-[:checked]:border-blue-500">
                <input
                  type="radio"
                  name="language"
                  value={language.code}
                  checked={tempSelectedLanguage === language.code}
                  onChange={(e) => setTempSelectedLanguage(e.target.value)}
                  className="sr-only"
                />
                <div className="text-center">
                  <span className="block font-semibold">{language.name}</span>
                  <span className="block text-sm text-gray-500">{language.nativeName}</span>
                </div>
              </label>
            ))}
          </div>
        </div>
        
        <div className="p-4 bg-gray-50 border-t flex justify-end space-x-2">
          <button className="px-4 py-2 rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300" onClick={handleCancel}>
            Cancel
          </button>
          <button className="px-4 py-2 rounded-md text-white bg-blue-600 hover:bg-blue-700" onClick={handleSave}>
            Save Language
          </button>
        </div>
      </div>
    </div>
  );
}

export default LanguageSettings;