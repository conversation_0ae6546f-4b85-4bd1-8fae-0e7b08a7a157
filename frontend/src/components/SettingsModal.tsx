import React, { useState } from 'react';

interface SettingsModalProps {
  onClose: () => void;
  onSave: (apiKey: string) => void;
  initialApiKey: string;
}

function SettingsModal({ onClose, onSave, initialApiKey }: SettingsModalProps) {
  const [apiKey, setApiKey] = useState(initialApiKey || '');

  const handleSave = () => {
    onSave(apiKey);
  };

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={handleBackdropClick}>
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <h2 className="text-2xl font-bold mb-4">Settings</h2>
        
        <div className="mb-4">
          <label htmlFor="apiKey" className="block text-sm font-medium text-gray-700">
            LLM API Key
          </label>
          <input
            id="apiKey"
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter your API key"
          />
          <p className="mt-2 text-sm text-gray-500">
            Your API key is stored locally in your browser and is never sent to any server except the LLM API.
          </p>
        </div>
        
        <div className="flex justify-end space-x-2">
          <button onClick={onClose} className="px-4 py-2 rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300">
            Cancel
          </button>
          <button onClick={handleSave} className="px-4 py-2 rounded-md text-white bg-blue-600 hover:bg-blue-700">
            Save
          </button>
        </div>
      </div>
    </div>
  );
}

export default SettingsModal;