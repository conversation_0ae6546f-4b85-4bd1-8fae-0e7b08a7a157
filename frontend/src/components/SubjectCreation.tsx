import React, { useState } from 'react';
import axios from 'axios';
import CurriculumEditor from './CurriculumEditor';
import { useLanguage } from '../contexts/LanguageContext';
import { Category } from '../types';

// Types
interface Subject {
  id: string;
  title: string;
  description: string;
  has_curriculum: boolean;
}

interface SubjectCreationProps {
  onBack: () => void;
  onSubjectCreated: (subject: Subject) => void;
  apiKey: string;
}

interface CurriculumData {
  curriculum: Category[];
  [key: string]: any;
}

const SubjectCreation: React.FC<SubjectCreationProps> = ({ onBack, onSubjectCreated, apiKey }) => {
  const { selectedLanguage } = useLanguage();
  const [step, setStep] = useState<number>(1); // 1: topic input, 2: curriculum review, 3: curriculum editing
  const [subjectTitle, setSubjectTitle] = useState<string>('');
  const [subjectDescription, setSubjectDescription] = useState<string>('');
  const [generatedCurriculum, setGeneratedCurriculum] = useState<CurriculumData | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateCurriculum = async (): Promise<void> => {
    if (!subjectTitle.trim()) {
      setError('Please enter a subject title');
      return;
    }

    if (!apiKey) {
      setError('Please set your API key in the settings');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await axios.post('http://localhost:3001/api/generate-curriculum', {
        subject: subjectTitle.trim(),
        language: selectedLanguage.name
      });

      setGeneratedCurriculum(response.data);
      setStep(2);
    } catch (error: any) {
      console.error('Error generating curriculum:', error);
      if (error.response) {
        setError(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else if (error.request) {
        setError('Error: No response from server. Make sure the backend is running.');
      } else {
        setError(`Error: ${error.message}`);
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCreateSubject = async (): Promise<void> => {
    if (!generatedCurriculum) return;
    try {
      // Create the subject
      const subjectResponse = await axios.post('http://localhost:3001/api/subjects', {
        title: subjectTitle.trim(),
        description: subjectDescription.trim()
      });

      const subjectId = subjectResponse.data.id;

      // Save the curriculum
      await axios.post('http://localhost:3001/api/curricula', {
        subjectId: subjectId,
        structure: generatedCurriculum
      });

      onSubjectCreated({
        id: subjectId,
        title: subjectTitle.trim(),
        description: subjectDescription.trim(),
        has_curriculum: true
      });
    } catch (error: any) {
      console.error('Error creating subject:', error);
      setError('Failed to create subject. Please try again.');
    }
  };

  const handleCurriculumEdit = (updatedCurriculum: CurriculumData): void => {
    setGeneratedCurriculum(updatedCurriculum);
  };

  if (step === 1) {
    return (
      <div className="p-4 sm:p-6 md:p-8">
        <div className="mb-6">
          <button onClick={onBack} className="text-blue-600 hover:underline">
            ← Back to Dashboard
          </button>
          <h1 className="text-3xl font-bold mt-2">Create New Learning Subject</h1>
        </div>

        <div className="max-w-lg mx-auto bg-white p-6 rounded-lg shadow-md">
          <div className="mb-4">
            <label htmlFor="subjectTitle" className="block text-sm font-medium text-gray-700">
              What do you want to learn?
            </label>
            <input
              id="subjectTitle"
              type="text"
              value={subjectTitle}
              onChange={(e) => setSubjectTitle(e.target.value)}
              className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="e.g., React.js, Machine Learning, Photography..."
              maxLength={100}
            />
          </div>

          <div className="mb-4">
            <label htmlFor="subjectDescription" className="block text-sm font-medium text-gray-700">
              Description (optional)
            </label>
            <textarea
              id="subjectDescription"
              value={subjectDescription}
              onChange={(e) => setSubjectDescription(e.target.value)}
              className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Add any specific details about what you want to focus on..."
              maxLength={500}
              rows={3}
            />
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
              {error}
            </div>
          )}

          <button
            onClick={handleGenerateCurriculum}
            disabled={isGenerating || !subjectTitle.trim()}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400"
          >
            {isGenerating ? 'Generating Curriculum...' : 'Generate Curriculum'}
          </button>
        </div>
      </div>
    );
  }

  if (step === 2) {
    return (
      <div className="p-4 sm:p-6 md:p-8">
        <div className="mb-6">
          <button onClick={() => setStep(1)} className="text-blue-600 hover:underline">
            ← Back to Subject Details
          </button>
          <h1 className="text-3xl font-bold mt-2">Review Your Curriculum</h1>
        </div>

        <div className="max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-md">
          <div className="mb-6 pb-4 border-b">
            <h2 className="text-2xl font-semibold">{subjectTitle}</h2>
            {subjectDescription && <p className="text-gray-600 mt-2">{subjectDescription}</p>}
          </div>

          {generatedCurriculum && (
            <div className="space-y-6">
              {generatedCurriculum.curriculum.map((category) => (
                <div key={category.id}>
                  <h3 className="text-xl font-semibold text-gray-800">{category.title}</h3>
                  {category.description && (
                    <p className="text-gray-600 mt-1 mb-2">{category.description}</p>
                  )}
                  <ul className="list-disc list-inside space-y-1 pl-4">
                    {category.topics.map((topic) => (
                      <li key={topic.id}>
                        <span className="font-medium">{topic.title}</span>
                        {topic.description && (
                          <span className="text-gray-500 ml-2">- {topic.description}</span>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          )}

          <div className="mt-8 flex justify-end space-x-4">
            <button
              onClick={() => setStep(1)}
              className="px-4 py-2 rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300"
            >
              Regenerate Curriculum
            </button>
            <button
              onClick={() => setStep(3)}
              className="px-4 py-2 rounded-md text-white bg-yellow-500 hover:bg-yellow-600"
            >
              Edit Curriculum
            </button>
            <button
              onClick={handleCreateSubject}
              className="px-4 py-2 rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              Create Subject
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (step === 3) {
    return (
      <div className="p-4 sm:p-6 md:p-8">
        <div className="mb-6">
          <button onClick={() => setStep(2)} className="text-blue-600 hover:underline">
            ← Back to Review
          </button>
          <h1 className="text-3xl font-bold mt-2">Edit Curriculum</h1>
        </div>

        <div className="max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-md">
          <div className="mb-6 pb-4 border-b">
            <h2 className="text-2xl font-semibold">{subjectTitle}</h2>
            {subjectDescription && <p className="text-gray-600 mt-2">{subjectDescription}</p>}
          </div>

          {generatedCurriculum && <CurriculumEditor
            curriculum={generatedCurriculum}
            onCurriculumChange={handleCurriculumEdit}
          />}

          <div className="mt-8 flex justify-end space-x-4">
            <button
              onClick={() => setStep(2)}
              className="px-4 py-2 rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300"
            >
              Back to Review
            </button>
            <button
              onClick={handleCreateSubject}
              className="px-4 py-2 rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              Create Subject
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default SubjectCreation;