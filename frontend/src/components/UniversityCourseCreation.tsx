import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import CourseGenerationLoader from './CourseGenerationLoader';
import CourseEditor from './CourseEditor';
import { Category, Topic } from '../types';

interface Module {
  id: string;
  title: string;
  description: string;
  duration: string;
  order: number;
  prerequisites: string[];
  learningObjectives: string[];
  curriculum: Category[];
}

interface Course {
  title: string;
  description: string;
  duration: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  learningOutcomes: string[];
  prerequisites?: string[];
}

export interface GeneratedCourse {
  course: Course;
  modules: Module[];
}

interface UniversityCourseCreationProps {
  onBack: () => void;
  onCourseCreated: (course: any) => void;
}

function UniversityCourseCreation({ onBack, onCourseCreated }: UniversityCourseCreationProps) {
  const { selectedLanguage } = useLanguage();
  const [userSketch, setUserSketch] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCourse, setGeneratedCourse] = useState<GeneratedCourse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [demoMode, setDemoMode] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const handleDemoGeneration = () => {
    setIsGenerating(true);
    setError(null);
    setDemoMode(true);

    setTimeout(() => {
      setGeneratedCourse({
        course: {
          title: "Demo: Full-Stack Web Development Mastery",
          description: "A comprehensive course designed to take you from beginner to advanced full-stack developer.",
          duration: "12 weeks",
          difficulty: "intermediate",
          learningOutcomes: [
            "Build complete web applications from scratch",
            "Master both frontend and backend technologies",
            "Deploy applications to production environments"
          ]
        },
        modules: [
          {
            id: "demo-module-1",
            title: "Frontend Fundamentals",
            description: "Learn HTML, CSS, and JavaScript basics",
            duration: "3 weeks",
            order: 1,
            prerequisites: [],
            learningObjectives: ["Master HTML5 semantic elements", "Create responsive designs with CSS3"],
            curriculum: [
              { id: "cat-1", title: "HTML & CSS Basics", description: "", topics: [{ id: "t1", title: "HTML Structure", description: "" }] },
              { id: "cat-2", title: "JavaScript Fundamentals", description: "", topics: [{ id: "t2", title: "Variables & Functions", description: "" }] }
            ]
          }
        ]
      });
      setIsGenerating(false);
      setDemoMode(false);
    }, 35000); // Match the total animation time
  };

  const handleGenerateCourse = async () => {
    if (!userSketch.trim()) {
      setError('Please provide a learning sketch');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:3001/api/university-courses/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userSketch: userSketch.trim(),
          language: selectedLanguage.name
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setGeneratedCourse(data.course);
      console.log('Generated course:', data.course);
    } catch (err: any) {
      console.error('Error generating course:', err);
      setError(err.message || 'Failed to generate course. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCreateCourse = async () => {
    if (!generatedCourse) return;

    try {
      const response = await fetch('http://localhost:3001/api/university-courses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: generatedCourse.course.title,
          description: generatedCourse.course.description,
          duration: generatedCourse.course.duration,
          difficulty: generatedCourse.course.difficulty,
          prerequisites: generatedCourse.course.prerequisites || [],
          learningOutcomes: generatedCourse.course.learningOutcomes,
          userSketch: userSketch,
          modules: generatedCourse.modules.map(module => ({
            title: module.title,
            description: module.description,
            duration: module.duration,
            order: module.order,
            prerequisites: module.prerequisites || [],
            learningObjectives: module.learningObjectives,
            curriculum: { curriculum: module.curriculum }
          }))
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Course created:', data);

      if (onCourseCreated) {
        onCourseCreated(data);
      }
    } catch (err: any) {
      console.error('Error creating course:', err);
      setError(err.message || 'Failed to create course. Please try again.');
    }
  };

  const handleEditCourse = () => {
    setIsEditing(true);
  };

  const handleSaveCustomizations = (editedCourse: GeneratedCourse) => {
    setGeneratedCourse(editedCourse);
    setIsEditing(false);
  };

  const handleCancelEditing = () => {
    setIsEditing(false);
  };

  const handleBackToGeneration = () => {
    setGeneratedCourse(null);
    setIsEditing(false);
    setError(null);
  };

  if (isEditing && generatedCourse) {
    return (
      <CourseEditor
        course={generatedCourse}
        onSave={handleSaveCustomizations}
        onCancel={handleCancelEditing}
        onBackToGeneration={handleBackToGeneration}
      />
    );
  }

  return (
    <div className="container mx-auto p-4">
      <CourseGenerationLoader
        isVisible={isGenerating}
        onComplete={() => {}}
      />

      <div className="flex justify-between items-center mb-4">
        <button className="text-blue-500 hover:underline" onClick={onBack}>
          ← Back
        </button>
        <h1 className="text-2xl font-bold">Create University-Style Course</h1>
        <div className="text-sm text-gray-600">
          🌐 {selectedLanguage.nativeName}
        </div>
      </div>

      {!generatedCourse ? (
        <div className="max-w-2xl mx-auto">
          <div className="bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-6">
            <h2 className="font-bold">📝 Describe Your Learning Goals</h2>
            <p>
              Provide a detailed sketch of what you want to learn. The AI will analyze your content 
              and create a comprehensive university-style course with multiple specialized modules.
            </p>
            <div className="mt-2">
              <strong>Examples:</strong>
              <ul className="list-disc list-inside ml-4">
                <li>Technical skills you want to master</li>
                <li>Knowledge areas you want to explore</li>
                <li>Professional competencies you want to develop</li>
                <li>Academic subjects you want to study</li>
              </ul>
            </div>
          </div>

          <textarea
            className="w-full p-2 border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            value={userSketch}
            onChange={(e) => setUserSketch(e.target.value)}
            placeholder="Describe what you want to learn in detail. For example:\n\n### Technical Interview Preparation\n- Master data structures and algorithms\n- Learn system design principles\n- Practice coding interview questions\n- Understand database optimization\n- Learn cloud architecture patterns\n\nBe as detailed as possible about your learning goals..."
            rows={15}
          />

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative my-4" role="alert">
              ❌ {error}
            </div>
          )}

          <div className="flex justify-center space-x-4 mt-4">
            <button
              className="px-6 py-3 rounded-md text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400 font-bold"
              onClick={handleGenerateCourse}
              disabled={isGenerating || !userSketch.trim()}
            >
              {isGenerating ? '🔄 Generating Course...' : '🚀 Generate University Course'}
            </button>

            <button
              className="px-6 py-3 rounded-md text-white bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 font-bold"
              onClick={handleDemoGeneration}
              disabled={isGenerating}
            >
              🎬 Demo Animation
            </button>
          </div>
        </div>
      ) : (
        <div className="max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-lg">
          <div className="text-center mb-6">
            <h2 className="text-3xl font-bold">📚 {generatedCourse.course.title}</h2>
            <div className="flex justify-center space-x-4 mt-2 text-gray-600">
              <span>⏱️ {generatedCourse.course.duration}</span>
              <span className="capitalize">📈 {generatedCourse.course.difficulty}</span>
            </div>
          </div>

          <div className="mb-6">
            <p>{generatedCourse.course.description}</p>
          </div>

          <div className="mb-6">
            <h3 className="text-xl font-semibold mb-2">🎯 Learning Outcomes</h3>
            <ul className="list-disc list-inside space-y-1">
              {generatedCourse.course.learningOutcomes.map((outcome, index) => (
                <li key={index}>{outcome}</li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-4">📦 Course Modules ({generatedCourse.modules.length})</h3>
            <div className="space-y-4">
              {generatedCourse.modules.map((module, index) => (
                <div key={module.id} className="border p-4 rounded-md">
                  <div className="flex justify-between items-start">
                    <h4 className="text-lg font-bold">{index + 1}. {module.title}</h4>
                    <span className="text-sm text-gray-500 whitespace-nowrap">{module.duration}</span>
                  </div>
                  <p className="text-gray-700 mt-1">{module.description}</p>
                  
                  <div className="mt-2">
                    <strong>Learning Objectives:</strong>
                    <ul className="list-disc list-inside ml-4 text-sm">
                      {module.learningObjectives.map((objective, objIndex) => (
                        <li key={objIndex}>{objective}</li>
                      ))}
                    </ul>
                  </div>

                  <div className="mt-2 text-sm">
                    <strong>Curriculum ({module.curriculum.length} categories):</strong>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {module.curriculum.map((category) => (
                        <div key={category.id} className="bg-gray-200 rounded-full px-3 py-1 text-xs">
                          <span>{category.title}</span>
                          <span className="ml-1">({category.topics.length} topics)</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-center space-x-4 mt-8">
            <button
              className="px-6 py-2 rounded-md text-white bg-yellow-500 hover:bg-yellow-600 font-semibold"
              onClick={handleEditCourse}
            >
              ✏️ Customize Course
            </button>
            <button
              className="px-6 py-2 rounded-md text-white bg-gray-500 hover:bg-gray-600 font-semibold"
              onClick={handleBackToGeneration}
            >
              🔄 Generate New Course
            </button>
            <button
              className="px-6 py-2 rounded-md text-white bg-blue-600 hover:bg-blue-700 font-semibold"
              onClick={handleCreateCourse}
            >
              ✅ Create Course
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default UniversityCourseCreation;