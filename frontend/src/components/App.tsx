import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from '../contexts/AuthContext';
import { LanguageProvider } from '../contexts/LanguageContext';
import ProtectedRoute from './ProtectedRoute';

// Import pages
import LandingPage from '../pages/LandingPage';
import LoginPage from '../pages/LoginPage';
import RegisterPage from '../pages/RegisterPage';
import PricingPage from '../pages/PricingPage';
import DocumentationPage from '../pages/DocumentationPage';
import OAuthCallback from '../pages/OAuthCallback';

// Import dashboard components
import Dashboard from './Dashboard';
import SubjectCreation from './SubjectCreation';
import SubjectLearning from './SubjectLearning';

// Types
interface Subject {
  id: string;
  title: string;
  description?: string;
  [key: string]: any;
}

type ViewType = 'dashboard' | 'create-subject' | 'learning';

// Dashboard wrapper component to maintain the old functionality
const DashboardWrapper: React.FC = () => {
  const [currentView, setCurrentView] = useState<ViewType>('dashboard');
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [apiKey, setApiKey] = useState<string>('');
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [showLanguageSettings, setShowLanguageSettings] = useState<boolean>(false);

  useEffect(() => {
    const savedApiKey = localStorage.getItem('qtmaster_api_key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
    }
  }, []);

  const handleSubjectSelect = (subject: Subject) => {
    setSelectedSubject(subject);
    setCurrentView('learning');
  };

  const handleCreateSubject = () => {
    setCurrentView('create-subject');
  };

  const handleSubjectCreated = (newSubject: Subject) => {
    setSelectedSubject(newSubject);
    setCurrentView('learning');
  };

  const handleBackToDashboard = () => {
    setSelectedSubject(null);
    setCurrentView('dashboard');
  };

  const handleSaveApiKey = (key: string) => {
    setApiKey(key);
    localStorage.setItem('qtmaster_api_key', key);
    setShowSettings(false);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return (
          <Dashboard
            onSubjectSelect={handleSubjectSelect}
            onCreateSubject={handleCreateSubject}
          />
        );
      case 'create-subject':
        return (
          <SubjectCreation
            onBack={handleBackToDashboard}
            onSubjectCreated={handleSubjectCreated}
            apiKey={apiKey}
          />
        );
      case 'learning':
        return (
          <SubjectLearning
            subject={selectedSubject}
            onBack={handleBackToDashboard}
            apiKey={apiKey}
          />
        );
      default:
        return <Dashboard onSubjectSelect={handleSubjectSelect} onCreateSubject={handleCreateSubject} />;
    }
  };

  return (
    <div className="min-h-screen bg-slate-950">
      {renderCurrentView()}
    </div>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <LanguageProvider>
        <Router>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<LandingPage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="/pricing" element={<PricingPage />} />
            <Route path="/documentation" element={<DocumentationPage />} />
            <Route path="/oauth/callback" element={<OAuthCallback />} />

            {/* Protected routes */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <DashboardWrapper />
                </ProtectedRoute>
              }
            />

            {/* Redirect unknown routes to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </LanguageProvider>
    </AuthProvider>
  );
};

export default App;
