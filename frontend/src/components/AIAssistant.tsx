import React, { useState, useEffect } from 'react';
import { MessageCircle, X, Camera, BarChart3, Lightbulb, AlertTriangle } from 'lucide-react';
import { useAICodeCanvas } from '../contexts/AICodeCanvasContext';
import { useAICodeCanvasObserver } from '../hooks/useAICodeCanvasObserver';

interface AIAssistantProps {
  isVisible: boolean;
  onToggle: () => void;
}

const AIAssistant: React.FC<AIAssistantProps> = ({ isVisible, onToggle }) => {
  const {
    currentState,
    suggestions,
    removeSuggestion,
    clearSuggestions,
    lastScreenshot
  } = useAICodeCanvas();

  const {
    currentAnalysis,
    manualAnalysis,
    requestHelp,
    suggestTemplate,
    captureScreenshot
  } = useAICodeCanvasObserver();

  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [helpTopic, setHelpTopic] = useState('');

  const handleManualAnalysis = async () => {
    setIsAnalyzing(true);
    try {
      await manualAnalysis();
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleRequestHelp = () => {
    if (helpTopic.trim()) {
      requestHelp(helpTopic);
      setHelpTopic('');
    }
  };

  const handleCaptureScreenshot = async () => {
    try {
      await captureScreenshot();
    } catch (error) {
      console.error('Failed to capture screenshot:', error);
    }
  };

  const getPriorityColor = (priority: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high': return 'border-red-500 bg-red-50';
      case 'medium': return 'border-yellow-500 bg-yellow-50';
      case 'low': return 'border-blue-500 bg-blue-50';
      default: return 'border-gray-500 bg-gray-50';
    }
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'debug_help': return <AlertTriangle size={16} className="text-red-500" />;
      case 'learning_tip': return <Lightbulb size={16} className="text-yellow-500" />;
      case 'template_suggestion': return <MessageCircle size={16} className="text-blue-500" />;
      default: return <MessageCircle size={16} className="text-gray-500" />;
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-50"
        title="Open AI Assistant"
      >
        <MessageCircle size={24} />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-80 bg-white border border-gray-200 rounded-lg shadow-xl z-50 max-h-96 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <MessageCircle size={20} className="text-blue-600" />
          <h3 className="font-semibold text-gray-800">AI Assistant</h3>
        </div>
        <button
          onClick={onToggle}
          className="text-gray-500 hover:text-gray-700 transition-colors"
        >
          <X size={20} />
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Current Analysis */}
        {currentState && (
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-gray-700">Code Analysis</h4>
              <button
                onClick={handleManualAnalysis}
                disabled={isAnalyzing}
                className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200 disabled:opacity-50"
              >
                {isAnalyzing ? 'Analyzing...' : 'Refresh'}
              </button>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="bg-gray-50 p-2 rounded">
                <div className="text-gray-600">Complexity</div>
                <div className="font-semibold">{currentAnalysis.complexity}%</div>
              </div>
              <div className="bg-gray-50 p-2 rounded">
                <div className="text-gray-600">Error Rate</div>
                <div className="font-semibold">{currentAnalysis.errorRate}%</div>
              </div>
              <div className="bg-gray-50 p-2 rounded">
                <div className="text-gray-600">Productivity</div>
                <div className="font-semibold">{currentAnalysis.productivityScore}%</div>
              </div>
              <div className="bg-gray-50 p-2 rounded">
                <div className="text-gray-600">Keystrokes</div>
                <div className="font-semibold">{currentState.totalKeystrokes}</div>
              </div>
            </div>
          </div>
        )}

        {/* Suggestions */}
        <div className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-700">Suggestions</h4>
            {suggestions.length > 0 && (
              <button
                onClick={clearSuggestions}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                Clear All
              </button>
            )}
          </div>
          
          {suggestions.length === 0 ? (
            <p className="text-sm text-gray-500">No suggestions at the moment. Keep coding!</p>
          ) : (
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {suggestions.map((suggestion) => (
                <div
                  key={suggestion.id}
                  className={`p-3 rounded-lg border-l-4 ${getPriorityColor(suggestion.priority)}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-2 flex-1">
                      {getSuggestionIcon(suggestion.type)}
                      <div className="flex-1">
                        <p className="text-sm text-gray-800">{suggestion.message}</p>
                        {suggestion.code && (
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-x-auto">
                            <code>{suggestion.code}</code>
                          </pre>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => removeSuggestion(suggestion.id)}
                      className="text-gray-400 hover:text-gray-600 ml-2"
                    >
                      <X size={14} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="p-4 border-t border-gray-100">
          <h4 className="font-medium text-gray-700 mb-2">Quick Actions</h4>
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => suggestTemplate('html')}
              className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded hover:bg-orange-200"
            >
              HTML Template
            </button>
            <button
              onClick={() => suggestTemplate('css')}
              className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200"
            >
              CSS Template
            </button>
            <button
              onClick={() => suggestTemplate('js')}
              className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded hover:bg-yellow-200"
            >
              JS Template
            </button>
            <button
              onClick={handleCaptureScreenshot}
              className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded hover:bg-green-200"
            >
              <Camera size={12} className="inline mr-1" />
              Screenshot
            </button>
          </div>
        </div>

        {/* Help Request */}
        <div className="p-4 border-t border-gray-100">
          <h4 className="font-medium text-gray-700 mb-2">Need Help?</h4>
          <div className="flex gap-2">
            <input
              type="text"
              value={helpTopic}
              onChange={(e) => setHelpTopic(e.target.value)}
              placeholder="What do you need help with?"
              className="flex-1 text-sm px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              onKeyDown={(e) => e.key === 'Enter' && handleRequestHelp()}
            />
            <button
              onClick={handleRequestHelp}
              disabled={!helpTopic.trim()}
              className="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Ask
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIAssistant;
