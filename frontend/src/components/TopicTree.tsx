import React from 'react';
import { Category, Topic } from '../types';

interface TopicTreeProps {
  curriculum: Category[] | null;
  onTopicSelect: (topic: Topic) => void;
  selectedTopic: Topic | null;
  subjectTitle?: string;
}

const TopicTree: React.FC<TopicTreeProps> = ({ curriculum, onTopicSelect, selectedTopic, subjectTitle }) => {
  const handleTopicClick = (topic: Topic): void => {
    onTopicSelect(topic);
  };

  if (!curriculum) {
    return (
      <div className="p-4">
        <h2 className="text-xl font-bold mb-4">Loading...</h2>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">{subjectTitle || 'Curriculum'}</h2>
      <ul className="space-y-4">
        {curriculum.map((category) => (
          <li key={category.id}>
            <h3 className="text-lg font-semibold text-gray-700">{category.title}</h3>
            <ul className="mt-2 space-y-1 pl-4">
              {category.topics.map((topic) => (
                <li 
                  key={topic.id} 
                  className={`cursor-pointer p-2 rounded-md ${selectedTopic?.id === topic.id ? 'bg-blue-500 text-white' : 'hover:bg-gray-200'}`}
                  onClick={() => handleTopicClick(topic)}
                >
                  {topic.title}
                </li>
              ))}
            </ul>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default TopicTree;