import { CodeCanvasState, CodeCanvasAction } from '../types';

export const initialState: CodeCanvasState = {
  // Code content
  html: "<div>\n  <h1>Hello World!</h1>\n  <p>Edit this code to see changes</p>\n</div>",
  css: "body {\n  font-family: sans-serif;\n  padding: 20px;\n}\n\nh1 {\n  color: #4a6cf7;\n}",
  js: '// JavaScript code will go here\nconsole.log("Hello from JavaScript!");\nconsole.error("This is an error example");\nconsole.warn("This is a warning example");',
  ts: '// TypeScript code will go here\nconst message: string = "Hello from TypeScript!";\nconsole.log(message);\n\ninterface User {\n  name: string;\n  age: number;\n}\n\nconst user: User = {\n  name: "<PERSON>",\n  age: 30\n};\n\nconsole.log(`User: ${user.name}, Age: ${user.age}`);',
  compiledJs: '',
  
  // UI state
  activeTab: 'preview',
  useTypeScript: false,
  useTailwind: true,
  isExecuting: false,
  isFullscreen: false,
  showConsoleInFullscreen: false,
  
  // Errors and console
  errors: [],
  tsErrors: [],
  consoleLogs: [],
  
  // Templates and projects
  showTemplateDropdown: false,
  showSaveDialog: false,
  showLoadDialog: false,
  projectName: '',
  savedProjects: [],
  
  // AI tracking
  totalKeystrokes: 0,
  tabSwitches: 0,
  codeExecutions: 0,
  errorsEncountered: 0,
  lastModified: new Date(),
};

export function codeCanvasReducer(state: CodeCanvasState, action: CodeCanvasAction): CodeCanvasState {
  switch (action.type) {
    case 'SET_CODE':
      return {
        ...state,
        [action.payload.tab]: action.payload.code,
        lastModified: new Date(),
      };
      
    case 'SET_ACTIVE_TAB':
      return {
        ...state,
        activeTab: action.payload,
      };
      
    case 'SET_TYPESCRIPT_MODE':
      return {
        ...state,
        useTypeScript: action.payload,
        // Switch to appropriate tab when toggling
        activeTab: action.payload && state.activeTab === 'js' ? 'ts' : 
                  !action.payload && state.activeTab === 'ts' ? 'js' : 
                  state.activeTab,
      };
      
    case 'SET_TAILWIND_MODE':
      return {
        ...state,
        useTailwind: action.payload,
      };
      
    case 'SET_EXECUTING':
      return {
        ...state,
        isExecuting: action.payload,
      };
      
    case 'SET_COMPILED_JS':
      return {
        ...state,
        compiledJs: action.payload,
      };
      
    case 'SET_ERRORS':
      return {
        ...state,
        [action.payload.type === 'js' ? 'errors' : 'tsErrors']: action.payload.errors,
      };
      
    case 'ADD_CONSOLE_LOG':
      return {
        ...state,
        consoleLogs: [...state.consoleLogs, action.payload],
      };
      
    case 'CLEAR_CONSOLE':
      return {
        ...state,
        consoleLogs: [],
      };
      
    case 'INCREMENT_KEYSTROKES':
      return {
        ...state,
        totalKeystrokes: state.totalKeystrokes + action.payload,
      };
      
    case 'INCREMENT_TAB_SWITCHES':
      return {
        ...state,
        tabSwitches: state.tabSwitches + 1,
      };
      
    case 'INCREMENT_CODE_EXECUTIONS':
      return {
        ...state,
        codeExecutions: state.codeExecutions + 1,
      };
      
    case 'INCREMENT_ERRORS_ENCOUNTERED':
      return {
        ...state,
        errorsEncountered: state.errorsEncountered + action.payload,
      };
      
    case 'SET_TEMPLATE_DROPDOWN':
      return {
        ...state,
        showTemplateDropdown: action.payload,
      };
      
    case 'SET_SAVE_DIALOG':
      return {
        ...state,
        showSaveDialog: action.payload,
      };
      
    case 'SET_LOAD_DIALOG':
      return {
        ...state,
        showLoadDialog: action.payload,
      };
      
    case 'SET_PROJECT_NAME':
      return {
        ...state,
        projectName: action.payload,
      };
      
    case 'SET_SAVED_PROJECTS':
      return {
        ...state,
        savedProjects: action.payload,
      };

    case 'SET_FULLSCREEN':
      return {
        ...state,
        isFullscreen: action.payload,
      };

    case 'SET_CONSOLE_IN_FULLSCREEN':
      return {
        ...state,
        showConsoleInFullscreen: action.payload,
      };

    case 'RESET_CODE':
      return {
        ...initialState,
        savedProjects: state.savedProjects, // Keep saved projects
        useTailwind: state.useTailwind, // Keep preferences
        useTypeScript: state.useTypeScript,
        isFullscreen: state.isFullscreen, // Keep fullscreen state
        showConsoleInFullscreen: state.showConsoleInFullscreen,
      };

    default:
      return state;
  }
}
