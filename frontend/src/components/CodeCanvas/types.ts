// Core types for the CodeCanvas module

export type Tab = "html" | "css" | "js" | "ts" | "preview" | "console";

export interface CodeError {
  message: string;
  line?: number;
  column?: number;
  type: 'syntax' | 'runtime';
}

export interface ConsoleMessage {
  type: 'log' | 'error' | 'warn' | 'info';
  message: string;
  timestamp: Date;
  line?: number;
  column?: number;
}

export interface CodeTemplate {
  name: string;
  description: string;
  code: string;
}

export interface SavedProject {
  id: string;
  name: string;
  html: string;
  css: string;
  js: string;
  ts: string;
  timestamp: Date;
}

// AI Observer Types
export interface AICodeCanvasState {
  html: string;
  css: string;
  js: string;
  ts: string;
  compiledJs: string;
  activeTab: Tab;
  useTypeScript: boolean;
  useTailwind: boolean;
  isExecuting: boolean;
  errors: CodeError[];
  tsErrors: CodeError[];
  consoleLogs: ConsoleMessage[];
  lastModified: Date;
  totalKeystrokes: number;
  tabSwitches: number;
  codeExecutions: number;
  errorsEncountered: number;
}

export interface AIUserAction {
  type: 'code_change' | 'tab_switch' | 'code_execution' | 'template_load' | 'error_occurred' | 'console_output';
  timestamp: Date;
  details: any;
  context: Partial<AICodeCanvasState>;
}

export interface AISuggestion {
  type: 'code_fix' | 'template_suggestion' | 'learning_tip' | 'debug_help';
  message: string;
  code?: string;
  targetTab?: Tab;
  priority: 'low' | 'medium' | 'high';
}

export interface AIObserver {
  onStateChange: (state: AICodeCanvasState) => void;
  onUserAction: (action: AIUserAction) => void;
  onScreenshotReady: (screenshot: string) => void;
  getCurrentState: () => AICodeCanvasState;
  captureScreenshot: () => Promise<string>;
  suggestAction: (suggestion: AISuggestion) => void;
}

// Component Props
export interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language: string;
  placeholder?: string;
  showLineNumbers?: boolean;
  enableTailwindSuggestions?: boolean;
}

export interface CodeCanvasProps {
  aiObserver?: AIObserver;
  enableAIObservation?: boolean;
}

// State Management Types
export interface CodeCanvasState {
  // Code content
  html: string;
  css: string;
  js: string;
  ts: string;
  compiledJs: string;
  
  // UI state
  activeTab: Tab;
  useTypeScript: boolean;
  useTailwind: boolean;
  isExecuting: boolean;
  isFullscreen: boolean;
  showConsoleInFullscreen: boolean;
  
  // Errors and console
  errors: CodeError[];
  tsErrors: CodeError[];
  consoleLogs: ConsoleMessage[];
  
  // Templates and projects
  showTemplateDropdown: boolean;
  showSaveDialog: boolean;
  showLoadDialog: boolean;
  projectName: string;
  savedProjects: SavedProject[];
  
  // AI tracking
  totalKeystrokes: number;
  tabSwitches: number;
  codeExecutions: number;
  errorsEncountered: number;
  lastModified: Date;
}

export type CodeCanvasAction =
  | { type: 'SET_CODE'; payload: { tab: Tab; code: string } }
  | { type: 'SET_ACTIVE_TAB'; payload: Tab }
  | { type: 'SET_TYPESCRIPT_MODE'; payload: boolean }
  | { type: 'SET_TAILWIND_MODE'; payload: boolean }
  | { type: 'SET_EXECUTING'; payload: boolean }
  | { type: 'SET_COMPILED_JS'; payload: string }
  | { type: 'SET_ERRORS'; payload: { type: 'js' | 'ts'; errors: CodeError[] } }
  | { type: 'ADD_CONSOLE_LOG'; payload: ConsoleMessage }
  | { type: 'CLEAR_CONSOLE' }
  | { type: 'INCREMENT_KEYSTROKES'; payload: number }
  | { type: 'INCREMENT_TAB_SWITCHES' }
  | { type: 'INCREMENT_CODE_EXECUTIONS' }
  | { type: 'INCREMENT_ERRORS_ENCOUNTERED'; payload: number }
  | { type: 'SET_TEMPLATE_DROPDOWN'; payload: boolean }
  | { type: 'SET_SAVE_DIALOG'; payload: boolean }
  | { type: 'SET_LOAD_DIALOG'; payload: boolean }
  | { type: 'SET_PROJECT_NAME'; payload: string }
  | { type: 'SET_SAVED_PROJECTS'; payload: SavedProject[] }
  | { type: 'SET_FULLSCREEN'; payload: boolean }
  | { type: 'SET_CONSOLE_IN_FULLSCREEN'; payload: boolean }
  | { type: 'RESET_CODE' };

// Service Types
export interface CompilerOptions {
  target?: string;
  module?: string;
  strict?: boolean;
  esModuleInterop?: boolean;
  skipLibCheck?: boolean;
}

export interface CompilationResult {
  js: string;
  errors: CodeError[];
}

export interface ScreenshotOptions {
  width?: number;
  height?: number;
  scale?: number;
  backgroundColor?: string;
}

export interface TemplateCategory {
  [key: string]: CodeTemplate[];
}
