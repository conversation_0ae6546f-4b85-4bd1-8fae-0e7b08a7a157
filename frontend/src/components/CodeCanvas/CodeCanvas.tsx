import React, { useEffect } from 'react';
import { useAICodeCanvasSafe } from '../../contexts/AICodeCanvasContext';
import { useCodeCanvasState } from './hooks/useCodeCanvasState';
import { useCodeExecution } from './hooks/useCodeExecution';
import { useTemplateManager } from './hooks/useTemplateManager';
import { CompilerService } from './services/CompilerService';
import { ScreenshotService } from './services/ScreenshotService';

// Components
import Toolbar from './components/Toolbar';
import TabBar from './components/TabBar';
import ErrorDisplay from './components/ErrorDisplay';
import CodeEditor from './components/CodeEditor';
import ConsolePanel from './components/ConsolePanel';
// import TemplateDropdown from './components/TemplateDropdown';
// import SaveDialog from './components/SaveDialog';
// import LoadDialog from './components/LoadDialog';

import { CodeCanvasProps, AICodeCanvasState, AIUserAction } from './types';

const CodeCanvas: React.FC<CodeCanvasProps> = ({ 
  aiObserver, 
  enableAIObservation = false 
}) => {
  // AI Context integration (optional)
  const aiContext = enableAIObservation ? useAICodeCanvasSafe() : null;

  // State management
  const {
    state,
    setCode,
    setActiveTab,
    setTypeScriptMode,
    setTailwindMode,
    setExecuting,
    setCompiledJs,
    setErrors,
    addConsoleLog,
    clearConsole,
    incrementKeystrokes,
    incrementTabSwitches,
    incrementCodeExecutions,
    incrementErrorsEncountered,
    setTemplateDropdown,
    setSaveDialog,
    setLoadDialog,
    setProjectName,
    setSavedProjects,
    setFullscreen,
    setConsoleInFullscreen,
    resetCode,
    getCurrentCode,
    setCurrentCode,
    getLanguageForTab
  } = useCodeCanvasState();

  // Code execution
  const { iframeRef, runCode, srcDoc } = useCodeExecution({
    html: state.html,
    css: state.css,
    js: state.js,
    ts: state.ts,
    compiledJs: state.compiledJs,
    useTypeScript: state.useTypeScript,
    useTailwind: state.useTailwind,
    onSetExecuting: setExecuting,
    onSetCompiledJs: setCompiledJs,
    onSetTsErrors: (errors) => setErrors('ts', errors),
    onAddConsoleLog: addConsoleLog,
    onClearConsole: clearConsole,
    onIncrementCodeExecutions: incrementCodeExecutions,
    onIncrementErrorsEncountered: incrementErrorsEncountered
  });

  // Template management
  const {
    getTemplatesForCurrentTab,
    loadTemplate,
    saveProject,
    loadProject,
    deleteProject
  } = useTemplateManager({
    activeTab: state.activeTab,
    useTypeScript: state.useTypeScript,
    savedProjects: state.savedProjects,
    onSetSavedProjects: setSavedProjects,
    onSetCode: setCode,
    onSetProjectName: setProjectName
  });

  // Services
  const compilerService = CompilerService.getInstance();
  const screenshotService = ScreenshotService.getInstance();

  // JavaScript syntax checking
  useEffect(() => {
    const jsErrors = compilerService.checkJavaScriptSyntax(state.js);
    setErrors('js', jsErrors);

    if (jsErrors.length > 0) {
      incrementErrorsEncountered(jsErrors.length);
    }
  }, [state.js]); // Remove function dependencies

  // AI Observer integration
  const getCurrentAIState = (): AICodeCanvasState => ({
    html: state.html,
    css: state.css,
    js: state.js,
    ts: state.ts,
    compiledJs: state.compiledJs,
    activeTab: state.activeTab,
    useTypeScript: state.useTypeScript,
    useTailwind: state.useTailwind,
    isExecuting: state.isExecuting,
    errors: state.errors,
    tsErrors: state.tsErrors,
    consoleLogs: state.consoleLogs,
    lastModified: state.lastModified,
    totalKeystrokes: state.totalKeystrokes,
    tabSwitches: state.tabSwitches,
    codeExecutions: state.codeExecutions,
    errorsEncountered: state.errorsEncountered
  });

  const notifyAIObserver = (action: Omit<AIUserAction, 'context'>) => {
    if (enableAIObservation) {
      const fullAction: AIUserAction = {
        ...action,
        context: getCurrentAIState()
      };
      
      // Notify external AI observer
      if (aiObserver) {
        aiObserver.onUserAction(fullAction);
        aiObserver.onStateChange(getCurrentAIState());
      }
      
      // Sync with AI context
      if (aiContext) {
        aiContext.addUserAction(fullAction);
        aiContext.updateState(getCurrentAIState());
      }
    }
  };

  // Enhanced handlers with AI tracking
  const handleCodeChange = (code: string) => {
    const oldCode = getCurrentCode();
    setCurrentCode(code);
    
    if (enableAIObservation && code !== oldCode) {
      incrementKeystrokes(Math.abs(code.length - oldCode.length));
      notifyAIObserver({
        type: 'code_change',
        timestamp: new Date(),
        details: {
          tab: state.activeTab,
          oldLength: oldCode.length,
          newLength: code.length,
          changeType: code.length > oldCode.length ? 'addition' : 'deletion'
        }
      });
    }
  };

  const handleTabChange = (tab: typeof state.activeTab) => {
    const oldTab = state.activeTab;
    setActiveTab(tab);
    
    if (enableAIObservation && tab !== oldTab) {
      incrementTabSwitches();
      notifyAIObserver({
        type: 'tab_switch',
        timestamp: new Date(),
        details: { fromTab: oldTab, toTab: tab }
      });
    }
  };

  const handleRunCode = () => {
    runCode();
    
    if (enableAIObservation) {
      notifyAIObserver({
        type: 'code_execution',
        timestamp: new Date(),
        details: {
          codeLength: {
            html: state.html.length,
            css: state.css.length,
            js: state.js.length,
            ts: state.ts.length
          },
          useTypeScript: state.useTypeScript,
          useTailwind: state.useTailwind
        }
      });
    }
  };

  const handleTemplateLoad = (template: any) => {
    loadTemplate(template);
    setTemplateDropdown(false);

    if (enableAIObservation) {
      notifyAIObserver({
        type: 'template_load',
        timestamp: new Date(),
        details: { templateName: template.name, tab: state.activeTab }
      });
    }
  };

  const handleToggleFullscreen = () => {
    setFullscreen(!state.isFullscreen);
  };

  const handleToggleConsoleInFullscreen = () => {
    setConsoleInFullscreen(!state.showConsoleInFullscreen);
  };

  // Expose AI observer methods - run only once
  useEffect(() => {
    if (enableAIObservation && aiObserver) {
      aiObserver.getCurrentState = getCurrentAIState;
      aiObserver.captureScreenshot = () => screenshotService.captureCodeCanvas();
    }
  }, [enableAIObservation, aiObserver]);

  // Sync state with AI context - separate effect to prevent loops
  useEffect(() => {
    if (aiContext && enableAIObservation) {
      aiContext.updateState(getCurrentAIState());
    }
  }, [aiContext, enableAIObservation, state]); // Depend on state instead of getCurrentAIState

  return (
    <div className={`code-canvas-container flex flex-col font-sans ${
      state.isFullscreen
        ? 'fixed inset-0 z-50 bg-gray-50'
        : 'w-full h-[70vh] border border-gray-200 rounded-lg overflow-hidden bg-gray-50'
    }`}>
      {/* Toolbar */}
      <Toolbar
        useTypeScript={state.useTypeScript}
        useTailwind={state.useTailwind}
        isExecuting={state.isExecuting}
        showTemplateDropdown={state.showTemplateDropdown}
        showSaveDialog={state.showSaveDialog}
        showLoadDialog={state.showLoadDialog}
        isFullscreen={state.isFullscreen}
        showConsoleInFullscreen={state.showConsoleInFullscreen}
        onTypeScriptToggle={setTypeScriptMode}
        onTailwindToggle={setTailwindMode}
        onRunCode={handleRunCode}
        onResetCode={resetCode}
        onToggleTemplateDropdown={() => setTemplateDropdown(!state.showTemplateDropdown)}
        onToggleSaveDialog={() => setSaveDialog(!state.showSaveDialog)}
        onToggleLoadDialog={() => setLoadDialog(!state.showLoadDialog)}
        onToggleFullscreen={handleToggleFullscreen}
        onToggleConsoleInFullscreen={handleToggleConsoleInFullscreen}
      />

      {/* Tab Bar - Hidden in fullscreen mode */}
      {!state.isFullscreen && (
        <TabBar
          activeTab={state.activeTab}
          useTypeScript={state.useTypeScript}
          errors={state.errors}
          tsErrors={state.tsErrors}
          consoleLogs={state.consoleLogs}
          onTabChange={handleTabChange}
        />
      )}

      {/* Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Error Display */}
        <ErrorDisplay
          activeTab={state.activeTab}
          useTypeScript={state.useTypeScript}
          errors={state.errors}
          tsErrors={state.tsErrors}
        />

        {/* Main Content - Different layout for fullscreen vs normal */}
        {state.isFullscreen ? (
          /* Fullscreen Layout - Code editor with optional console below */
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Code Editor Area */}
            <div className={`${state.showConsoleInFullscreen ? 'flex-1' : 'h-full'} overflow-hidden`}>
              {/* Code Editor */}
              {(state.activeTab === "html" || state.activeTab === "css" ||
                (state.activeTab === "js" && !state.useTypeScript) ||
                (state.activeTab === "ts" && state.useTypeScript)) && (
                <CodeEditor
                  value={getCurrentCode()}
                  onChange={handleCodeChange}
                  language={getLanguageForTab(state.activeTab)}
                  placeholder={`Enter your ${(state.activeTab === 'ts' && state.useTypeScript) ? 'TYPESCRIPT' : state.activeTab.toUpperCase()} code here...`}
                  enableTailwindSuggestions={state.useTailwind && state.activeTab === 'html'}
                />
              )}

              {/* Preview */}
              {state.activeTab === "preview" && (
                <iframe
                  ref={iframeRef}
                  srcDoc={srcDoc}
                  className="w-full h-full border-0 bg-white"
                  title="Code Preview"
                  sandbox="allow-scripts allow-same-origin"
                />
              )}
            </div>

            {/* Console Panel (below code editor in fullscreen) */}
            {state.showConsoleInFullscreen && (
              <div className="h-64 border-t border-gray-200 bg-gray-900">
                <ConsolePanel
                  consoleLogs={state.consoleLogs}
                  onClearConsole={clearConsole}
                />
              </div>
            )}
          </div>
        ) : (
          /* Normal Layout - Tab-based content */
          <div className="flex-1 overflow-hidden">
            {/* Code Editor */}
            {(state.activeTab === "html" || state.activeTab === "css" ||
              (state.activeTab === "js" && !state.useTypeScript) ||
              (state.activeTab === "ts" && state.useTypeScript)) && (
              <CodeEditor
                value={getCurrentCode()}
                onChange={handleCodeChange}
                language={getLanguageForTab(state.activeTab)}
                placeholder={`Enter your ${(state.activeTab === 'ts' && state.useTypeScript) ? 'TYPESCRIPT' : state.activeTab.toUpperCase()} code here...`}
                enableTailwindSuggestions={state.useTailwind && state.activeTab === 'html'}
              />
            )}

            {/* Preview */}
            {state.activeTab === "preview" && (
              <iframe
                ref={iframeRef}
                srcDoc={srcDoc}
                className="w-full h-full border-0 bg-white"
                title="Code Preview"
                sandbox="allow-scripts allow-same-origin"
              />
            )}

            {/* Console */}
            {state.activeTab === "console" && (
              <ConsolePanel
                consoleLogs={state.consoleLogs}
                onClearConsole={clearConsole}
              />
            )}
          </div>
        )}
      </div>

      {/* Dialogs and Dropdowns - TODO: Implement these components */}
      {state.showTemplateDropdown && (
        <div className="absolute right-4 top-16 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-64 overflow-y-auto">
          {getTemplatesForCurrentTab().map((template, index) => (
            <button
              key={index}
              onClick={() => handleTemplateLoad(template)}
              className="w-full text-left px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
            >
              <div className="font-medium text-sm text-gray-800">{template.name}</div>
              <div className="text-xs text-gray-500 mt-1">{template.description}</div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default CodeCanvas;
