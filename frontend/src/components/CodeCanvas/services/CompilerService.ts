import { CodeError, CompilerOptions, CompilationResult } from '../types';

export class CompilerService {
  private static instance: CompilerService;
  private tsLoaded = false;

  static getInstance(): CompilerService {
    if (!CompilerService.instance) {
      CompilerService.instance = new CompilerService();
    }
    return CompilerService.instance;
  }

  private async loadTypeScript(): Promise<any> {
    if (this.tsLoaded && (window as any).ts) {
      return (window as any).ts;
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/typescript@latest/lib/typescript.js';
      script.onload = () => {
        this.tsLoaded = true;
        resolve((window as any).ts);
      };
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  async compileTypeScript(code: string, options: CompilerOptions = {}): Promise<CompilationResult> {
    try {
      const ts = await this.loadTypeScript();
      
      const compilerOptions = {
        target: ts.ScriptTarget.ES2020,
        module: ts.ModuleKind.None,
        strict: false,
        esModuleInterop: true,
        skipLibCheck: true,
        noEmitOnError: false,
        lib: ['ES2020', 'DOM'],
        ...options
      };

      let result: string;
      let errors: CodeError[] = [];

      try {
        result = ts.transpile(code, compilerOptions);
        
        // Basic syntax error checking
        const sourceFile = ts.createSourceFile('temp.ts', code, ts.ScriptTarget.ES2020, true);
        
        function visit(node: any) {
          if (node.kind === ts.SyntaxKind.SyntaxError) {
            errors.push({
              message: 'Syntax error detected',
              type: 'syntax' as const
            });
          }
          ts.forEachChild(node, visit);
        }
        
        visit(sourceFile);
        
      } catch (transpileError: any) {
        result = `// TypeScript compilation failed\nconsole.error("TypeScript Error: ${transpileError.message}");`;
        
        const lineMatch = transpileError.message?.match(/\((\d+),(\d+)\)/);
        errors.push({
          message: transpileError.message || 'TypeScript compilation failed',
          line: lineMatch ? parseInt(lineMatch[1]) : undefined,
          column: lineMatch ? parseInt(lineMatch[2]) : undefined,
          type: 'syntax' as const
        });
      }

      return { js: result, errors };
      
    } catch (error) {
      console.error('TypeScript compilation error:', error);
      return { 
        js: `// TypeScript compilation failed\nconsole.error("TypeScript Error: ${error}");`,
        errors: [{
          message: `TypeScript compilation failed: ${error}`,
          type: 'syntax' as const
        }]
      };
    }
  }

  checkJavaScriptSyntax(code: string): CodeError[] {
    const errors: CodeError[] = [];
    try {
      new Function(code);
    } catch (error: any) {
      const errorMessage = error.message;
      let line: number | undefined;
      let column: number | undefined;
      
      const lineMatch = errorMessage.match(/line (\d+)/i);
      const columnMatch = errorMessage.match(/column (\d+)/i);
      
      if (lineMatch) line = parseInt(lineMatch[1]);
      if (columnMatch) column = parseInt(columnMatch[1]);
      
      errors.push({
        message: errorMessage,
        line,
        column,
        type: 'syntax'
      });
    }
    return errors;
  }

  generateSourceDoc(html: string, css: string, js: string, useTailwind: boolean): string {
    const tailwindCDN = useTailwind 
      ? '<script src="https://cdn.tailwindcss.com"></script>' 
      : '';
    
    const consoleScript = `
      <script>
        // Override console methods to send messages to parent
        const originalConsole = {
          log: console.log,
          error: console.error,
          warn: console.warn,
          info: console.info
        };
        
        function sendConsoleMessage(type, args) {
          const message = args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          ).join(' ');
          
          window.parent.postMessage({
            type: 'console',
            level: type,
            message: message,
            timestamp: new Date().toISOString()
          }, '*');
          
          originalConsole[type].apply(console, args);
        }
        
        console.log = (...args) => sendConsoleMessage('log', args);
        console.error = (...args) => sendConsoleMessage('error', args);
        console.warn = (...args) => sendConsoleMessage('warn', args);
        console.info = (...args) => sendConsoleMessage('info', args);
        
        // Catch unhandled errors with detailed information
        window.addEventListener('error', (event) => {
          const errorMsg = \`\${event.message}\${event.lineno ? \` (line \${event.lineno}\${event.colno ? \`, column \${event.colno}\` : ''})\` : ''}\`;
          sendConsoleMessage('error', [errorMsg]);
          
          window.parent.postMessage({
            type: 'runtime-error',
            message: event.message,
            line: event.lineno,
            column: event.colno,
            filename: event.filename,
            timestamp: new Date().toISOString()
          }, '*');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
          const reason = typeof event.reason === 'object' ? JSON.stringify(event.reason) : String(event.reason);
          sendConsoleMessage('error', [\`Unhandled Promise Rejection: \${reason}\`]);
        });
        
        // Execution timeout protection
        let executionStartTime = Date.now();
        const EXECUTION_TIMEOUT = 5000;
        
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;
        
        window.setTimeout = function(callback, delay, ...args) {
          if (Date.now() - executionStartTime > EXECUTION_TIMEOUT) {
            sendConsoleMessage('error', ['Code execution timed out. Possible infinite loop detected.']);
            return -1;
          }
          return originalSetTimeout.call(this, callback, delay, ...args);
        };
        
        window.setInterval = function(callback, delay, ...args) {
          if (Date.now() - executionStartTime > EXECUTION_TIMEOUT) {
            sendConsoleMessage('error', ['Code execution timed out. Possible infinite loop detected.']);
            return -1;
          }
          return originalSetInterval.call(this, callback, delay, ...args);
        };
        
        const checkExecutionTime = () => {
          if (Date.now() - executionStartTime > EXECUTION_TIMEOUT) {
            sendConsoleMessage('error', ['Code execution timed out after 5 seconds. Check for infinite loops.']);
            window.parent.postMessage({
              type: 'execution-timeout',
              timestamp: new Date().toISOString()
            }, '*');
            return;
          }
          setTimeout(checkExecutionTime, 100);
        };
        
        setTimeout(checkExecutionTime, 100);
      </script>
    `;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        ${tailwindCDN}
        <style>${css}</style>
      </head>
      <body>
        ${html}
        ${consoleScript}
        <script>
          try {
            ${js}
          } catch (error) {
            console.error('JavaScript Error:', error.message);
          }
        </script>
      </body>
      </html>
    `;
  }
}
