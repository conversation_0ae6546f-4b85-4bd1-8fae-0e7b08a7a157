import { CodeTemplate, TemplateCategory, SavedProject } from '../types';

export class TemplateService {
  private static instance: TemplateService;
  private readonly STORAGE_KEY = 'codecanvas-saved-projects';

  static getInstance(): TemplateService {
    if (!TemplateService.instance) {
      TemplateService.instance = new TemplateService();
    }
    return TemplateService.instance;
  }

  getDefaultTemplates(): TemplateCategory {
    return {
      html: [
        {
          name: "Basic HTML Structure",
          description: "A simple HTML document structure",
          code: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>My Page</title>
</head>
<body>
  <h1>Welcome to My Page</h1>
  <p>This is a basic HTML structure.</p>
</body>
</html>`
        },
        {
          name: "HTML Form",
          description: "A basic form with input fields",
          code: `<form>
  <div>
    <label for="name">Name:</label>
    <input type="text" id="name" name="name" required>
  </div>
  <div>
    <label for="email">Email:</label>
    <input type="email" id="email" name="email" required>
  </div>
  <div>
    <label for="message">Message:</label>
    <textarea id="message" name="message" rows="4"></textarea>
  </div>
  <button type="submit">Submit</button>
</form>`
        },
        {
          name: "Card Layout",
          description: "A responsive card component",
          code: `<div class="card">
  <img src="https://via.placeholder.com/300x200" alt="Card image">
  <div class="card-content">
    <h3>Card Title</h3>
    <p>This is a sample card with an image, title, and description.</p>
    <button class="btn">Learn More</button>
  </div>
</div>`
        }
      ],
      css: [
        {
          name: "CSS Reset",
          description: "Basic CSS reset for consistent styling",
          code: `* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}`
        },
        {
          name: "Flexbox Layout",
          description: "Flexible box layout system",
          code: `.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.flex-item {
  flex: 1;
  margin: 10px;
  padding: 20px;
  background: #f4f4f4;
  border-radius: 8px;
}`
        },
        {
          name: "Card Styling",
          description: "Modern card component styles",
          code: `.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-2px);
}

.card-content {
  padding: 20px;
}

.btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
}`
        }
      ],
      js: [
        {
          name: "DOM Manipulation",
          description: "Basic DOM manipulation examples",
          code: `// Select elements
const button = document.querySelector('button');
const output = document.querySelector('#output');

// Add event listener
button.addEventListener('click', function() {
  output.textContent = 'Button clicked!';
  output.style.color = 'green';
});

// Create new elements
const newDiv = document.createElement('div');
newDiv.textContent = 'Dynamically created element';
document.body.appendChild(newDiv);`
        },
        {
          name: "Fetch API",
          description: "Making HTTP requests with fetch",
          code: `// Fetch data from an API
async function fetchData() {
  try {
    const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');
    const data = await response.json();
    console.log('Fetched data:', data);
    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
}

// Call the function
fetchData();`
        },
        {
          name: "Local Storage",
          description: "Working with browser local storage",
          code: `// Save data to localStorage
function saveData(key, value) {
  localStorage.setItem(key, JSON.stringify(value));
  console.log('Data saved:', key, value);
}

// Load data from localStorage
function loadData(key) {
  const data = localStorage.getItem(key);
  return data ? JSON.parse(data) : null;
}

// Example usage
saveData('user', { name: 'John', age: 30 });
const user = loadData('user');
console.log('Loaded user:', user);`
        }
      ],
      ts: [
        {
          name: "TypeScript Basics",
          description: "Basic TypeScript syntax and types",
          code: `// Basic types
const message: string = "Hello TypeScript!";
const count: number = 42;
const isActive: boolean = true;

// Array types
const numbers: number[] = [1, 2, 3, 4, 5];
const names: Array<string> = ["Alice", "Bob", "Charlie"];

// Object type
interface User {
  id: number;
  name: string;
  email: string;
}

const user: User = {
  id: 1,
  name: "John Doe",
  email: "<EMAIL>"
};

console.log(user);`
        },
        {
          name: "Classes and Interfaces",
          description: "Object-oriented programming in TypeScript",
          code: `interface Animal {
  name: string;
  makeSound(): void;
}

class Dog implements Animal {
  constructor(public name: string) {}

  makeSound(): void {
    console.log(\`\${this.name} says Woof!\`);
  }

  fetch(): void {
    console.log(\`\${this.name} is fetching the ball!\`);
  }
}

class Cat implements Animal {
  constructor(public name: string) {}

  makeSound(): void {
    console.log(\`\${this.name} says Meow!\`);
  }
}

const dog = new Dog("Buddy");
const cat = new Cat("Whiskers");

dog.makeSound();
cat.makeSound();`
        },
        {
          name: "Generic Functions",
          description: "Using generics for reusable code",
          code: `// Generic function
function identity<T>(arg: T): T {
  return arg;
}

// Generic array function
function getFirstElement<T>(arr: T[]): T | undefined {
  return arr.length > 0 ? arr[0] : undefined;
}

// Generic interface
interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

// Usage examples
const stringResult = identity("Hello");
const numberResult = identity(42);

const firstNumber = getFirstElement([1, 2, 3]);
const firstName = getFirstElement(["Alice", "Bob"]);

const userResponse: ApiResponse<User> = {
  data: { id: 1, name: "John", email: "<EMAIL>" },
  status: 200,
  message: "Success"
};

console.log(userResponse);`
        }
      ]
    };
  }

  // Project management
  saveProject(project: Omit<SavedProject, 'id' | 'timestamp'>): SavedProject {
    const savedProject: SavedProject = {
      ...project,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date()
    };

    const projects = this.getSavedProjects();
    projects.push(savedProject);
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(projects));
    return savedProject;
  }

  getSavedProjects(): SavedProject[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];
      
      const projects = JSON.parse(stored);
      return projects.map((p: any) => ({
        ...p,
        timestamp: new Date(p.timestamp)
      }));
    } catch (error) {
      console.error('Error loading saved projects:', error);
      return [];
    }
  }

  deleteProject(id: string): boolean {
    try {
      const projects = this.getSavedProjects();
      const filteredProjects = projects.filter(p => p.id !== id);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredProjects));
      return true;
    } catch (error) {
      console.error('Error deleting project:', error);
      return false;
    }
  }

  loadProject(id: string): SavedProject | null {
    const projects = this.getSavedProjects();
    return projects.find(p => p.id === id) || null;
  }

  exportProject(project: SavedProject): string {
    return JSON.stringify(project, null, 2);
  }

  importProject(jsonString: string): SavedProject | null {
    try {
      const project = JSON.parse(jsonString);
      
      // Validate project structure
      if (!project.name || !project.html || !project.css || !project.js) {
        throw new Error('Invalid project format');
      }

      return this.saveProject({
        name: project.name + ' (Imported)',
        html: project.html,
        css: project.css,
        js: project.js,
        ts: project.ts || ''
      });
    } catch (error) {
      console.error('Error importing project:', error);
      return null;
    }
  }

  // Template management
  getTemplatesForTab(tab: string): CodeTemplate[] {
    const templates = this.getDefaultTemplates();
    return templates[tab] || [];
  }

  searchTemplates(query: string): CodeTemplate[] {
    const allTemplates = this.getDefaultTemplates();
    const results: CodeTemplate[] = [];
    
    Object.values(allTemplates).forEach(templates => {
      templates.forEach(template => {
        if (
          template.name.toLowerCase().includes(query.toLowerCase()) ||
          template.description.toLowerCase().includes(query.toLowerCase())
        ) {
          results.push(template);
        }
      });
    });
    
    return results;
  }
}
