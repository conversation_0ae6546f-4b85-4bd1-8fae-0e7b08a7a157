import { ScreenshotOptions } from '../types';

export class ScreenshotService {
  private static instance: ScreenshotService;
  private html2canvasLoaded = false;

  static getInstance(): ScreenshotService {
    if (!ScreenshotService.instance) {
      ScreenshotService.instance = new ScreenshotService();
    }
    return ScreenshotService.instance;
  }

  private async loadHtml2Canvas(): Promise<any> {
    if (this.html2canvasLoaded && (window as any).html2canvas) {
      return (window as any).html2canvas;
    }

    try {
      const html2canvas = await import('html2canvas');
      this.html2canvasLoaded = true;
      return html2canvas.default;
    } catch (error) {
      console.warn('html2canvas not available, using fallback');
      return null;
    }
  }

  async captureElement(element: HTMLElement, options: ScreenshotOptions = {}): Promise<string> {
    const html2canvas = await this.loadHtml2Canvas();
    
    if (!html2canvas) {
      return this.createFallbackScreenshot(element, options);
    }

    try {
      const canvas = await html2canvas(element, {
        allowTaint: true,
        useCORS: true,
        scale: options.scale || 0.8,
        width: options.width,
        height: options.height,
        backgroundColor: options.backgroundColor || '#ffffff'
      });
      
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.warn('html2canvas failed, using fallback:', error);
      return this.createFallbackScreenshot(element, options);
    }
  }

  async captureIframe(iframe: HTMLIFrameElement, options: ScreenshotOptions = {}): Promise<string> {
    const html2canvas = await this.loadHtml2Canvas();
    
    if (!html2canvas) {
      return this.createFallbackIframeScreenshot(iframe, options);
    }

    try {
      const rect = iframe.getBoundingClientRect();
      const canvas = await html2canvas(iframe, {
        allowTaint: true,
        useCORS: true,
        scale: options.scale || 1,
        width: options.width || rect.width,
        height: options.height || rect.height,
        backgroundColor: options.backgroundColor || '#ffffff'
      });
      
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.warn('iframe capture failed, using fallback:', error);
      return this.createFallbackIframeScreenshot(iframe, options);
    }
  }

  async captureCodeCanvas(): Promise<string> {
    const codeCanvasElement = document.querySelector('.code-canvas-container') as HTMLElement;
    
    if (!codeCanvasElement) {
      throw new Error('CodeCanvas container not found');
    }

    return this.captureElement(codeCanvasElement, {
      scale: 0.8,
      backgroundColor: '#ffffff'
    });
  }

  private createFallbackScreenshot(element: HTMLElement, options: ScreenshotOptions): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Canvas context not available');
    }

    const rect = element.getBoundingClientRect();
    canvas.width = options.width || rect.width || 800;
    canvas.height = options.height || rect.height || 600;
    
    // Fill background
    ctx.fillStyle = options.backgroundColor || '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add text information
    ctx.fillStyle = '#000000';
    ctx.font = '16px Arial';
    ctx.fillText('CodeCanvas Screenshot', 20, 50);
    ctx.fillText('(Visual capture not available)', 20, 80);
    ctx.fillText(`Element: ${element.className || element.tagName}`, 20, 110);
    ctx.fillText(`Size: ${rect.width}x${rect.height}`, 20, 140);
    ctx.fillText(`Timestamp: ${new Date().toLocaleString()}`, 20, 170);
    
    return canvas.toDataURL('image/png');
  }

  private createFallbackIframeScreenshot(iframe: HTMLIFrameElement, options: ScreenshotOptions): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Canvas context not available');
    }

    const rect = iframe.getBoundingClientRect();
    canvas.width = options.width || rect.width || 800;
    canvas.height = options.height || rect.height || 600;
    
    // Fill background
    ctx.fillStyle = options.backgroundColor || '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add border
    ctx.strokeStyle = '#cccccc';
    ctx.lineWidth = 2;
    ctx.strokeRect(0, 0, canvas.width, canvas.height);
    
    // Add text information
    ctx.fillStyle = '#666666';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Preview Screenshot', canvas.width / 2, canvas.height / 2 - 20);
    ctx.fillText('(iframe content cannot be captured)', canvas.width / 2, canvas.height / 2 + 10);
    ctx.fillText('due to security restrictions', canvas.width / 2, canvas.height / 2 + 40);
    
    return canvas.toDataURL('image/png');
  }

  // Utility method to convert data URL to blob
  dataURLToBlob(dataURL: string): Blob {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)![1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    
    return new Blob([u8arr], { type: mime });
  }

  // Utility method to download screenshot
  downloadScreenshot(dataURL: string, filename: string = 'codecanvas-screenshot.png'): void {
    const link = document.createElement('a');
    link.download = filename;
    link.href = dataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
