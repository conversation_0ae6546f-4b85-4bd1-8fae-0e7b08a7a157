# CodeCanvas - Modular Architecture

## 🏗️ Architecture Overview

The CodeCanvas has been refactored into a clean, modular architecture following best practices for separation of concerns, maintainability, and scalability.

## 📁 Folder Structure

```
CodeCanvas/
├── index.ts                    # Main exports
├── types.ts                    # TypeScript type definitions
├── CodeCanvas.tsx              # Main component
├── CodeCanvasStandalone.tsx    # Standalone version
├── AICodeCanvasDemo.tsx        # AI-enabled demo
├── README.md                   # This documentation
│
├── components/                 # UI Components
│   ├── TabBar.tsx             # Tab navigation
│   ├── Toolbar.tsx            # Action toolbar
│   ├── CodeEditor.tsx         # Code editor with syntax highlighting
│   ├── ConsolePanel.tsx       # Console output display
│   └── ErrorDisplay.tsx       # Error visualization
│
├── hooks/                      # Custom React hooks
│   ├── useCodeCanvasState.ts  # State management hook
│   ├── useCodeExecution.ts    # Code execution logic
│   └── useTemplateManager.ts  # Template operations
│
├── services/                   # Business logic services
│   ├── CompilerService.ts     # TypeScript/JS compilation
│   ├── ScreenshotService.ts   # Screenshot capture
│   └── TemplateService.ts     # Template & project management
│
└── state/                      # State management
    └── reducer.ts             # State reducer with actions
```

## 🎯 Design Principles

### 1. **Separation of Concerns**
- **UI Components**: Pure presentation components
- **Hooks**: Business logic and state management
- **Services**: External integrations and utilities
- **State**: Centralized state management with reducer pattern

### 2. **Single Responsibility**
- Each component has one clear purpose
- Services handle specific domains (compilation, screenshots, templates)
- Hooks encapsulate related functionality

### 3. **Dependency Injection**
- Services are singletons with getInstance() pattern
- Components receive dependencies through props
- Hooks abstract complex logic from components

### 4. **Type Safety**
- Comprehensive TypeScript types in `types.ts`
- Strict typing throughout the codebase
- Clear interfaces for all interactions

## 🔧 Core Components

### **CodeCanvas.tsx**
Main orchestrator component that:
- Manages overall state with `useCodeCanvasState`
- Handles code execution with `useCodeExecution`
- Manages templates with `useTemplateManager`
- Integrates AI observation capabilities
- Coordinates all child components

### **State Management**
```typescript
// Centralized state with reducer pattern
const { state, setCode, setActiveTab, ... } = useCodeCanvasState();

// Actions are dispatched through the reducer
dispatch({ type: 'SET_CODE', payload: { tab: 'html', code: '<div>Hello</div>' } });
```

### **Service Layer**
```typescript
// Singleton services for business logic
const compilerService = CompilerService.getInstance();
const screenshotService = ScreenshotService.getInstance();
const templateService = TemplateService.getInstance();
```

## 🎨 UI Components

### **TabBar**
- Displays navigation tabs (HTML, CSS, JS/TS, Preview, Console)
- Shows error indicators
- Handles tab switching with AI tracking

### **Toolbar**
- Action buttons (Run, Reset, Save, Load, Templates)
- Configuration toggles (TypeScript, Tailwind)
- Responsive design with proper spacing

### **CodeEditor**
- Syntax highlighting with Prism.js
- Line numbers
- Tailwind CSS autocomplete
- Tab indentation support
- Real-time error detection

### **ConsolePanel**
- Displays console output with timestamps
- Color-coded message types (log, error, warn, info)
- Clear functionality
- Message filtering capabilities

### **ErrorDisplay**
- Shows JavaScript and TypeScript errors
- Contextual error information (line, column)
- Helpful tips and suggestions
- Dismissible error messages

## 🔄 Hooks

### **useCodeCanvasState**
Central state management hook providing:
- State access and mutations
- Computed values (getCurrentCode, getLanguageForTab)
- Action dispatchers with proper typing

### **useCodeExecution**
Handles code execution logic:
- TypeScript compilation
- Source document generation
- Console message handling
- Execution timeout management
- Auto-preview updates

### **useTemplateManager**
Template and project operations:
- Template loading and filtering
- Project save/load/delete
- Import/export functionality
- Local storage management

## 🛠️ Services

### **CompilerService**
- TypeScript compilation with error handling
- JavaScript syntax validation
- Source document generation with console integration
- Execution timeout protection

### **ScreenshotService**
- Full CodeCanvas screenshots
- Preview iframe capture
- Fallback implementations
- Export utilities (download, blob conversion)

### **TemplateService**
- Default template library
- Project persistence (localStorage)
- Import/export functionality
- Template search and filtering

## 🤖 AI Integration

### **AI Observer Pattern**
```typescript
// AI state tracking
const getCurrentAIState = (): AICodeCanvasState => ({ ... });

// AI action notification
const notifyAIObserver = (action: AIUserAction) => { ... };

// Screenshot capture for AI analysis
const captureScreenshot = () => screenshotService.captureCodeCanvas();
```

### **Context Integration**
- Optional AI context provider integration
- Safe fallback when AI context unavailable
- Real-time state synchronization
- User action tracking and analytics

## 📦 Usage Examples

### **Basic Usage**
```typescript
import { CodeCanvasStandalone } from './components/CodeCanvas';

function MyApp() {
  return <CodeCanvasStandalone />;
}
```

### **AI-Enabled Usage**
```typescript
import { AICodeCanvasProvider } from './contexts/AICodeCanvasContext';
import { CodeCanvas, AICodeCanvasDemo } from './components/CodeCanvas';

function AIApp() {
  return (
    <AICodeCanvasProvider>
      <CodeCanvas enableAIObservation={true} />
    </AICodeCanvasProvider>
  );
}

// Or use the pre-configured demo
function DemoApp() {
  return <AICodeCanvasDemo />;
}
```

### **Custom Integration**
```typescript
import { 
  CodeCanvas, 
  useCodeCanvasState, 
  CompilerService,
  ScreenshotService 
} from './components/CodeCanvas';

function CustomApp() {
  const compilerService = CompilerService.getInstance();
  
  const customAIObserver = {
    onStateChange: (state) => {
      // Send to your AI service
      myAIService.analyze(state);
    },
    captureScreenshot: () => ScreenshotService.getInstance().captureCodeCanvas()
  };

  return (
    <CodeCanvas 
      aiObserver={customAIObserver}
      enableAIObservation={true}
    />
  );
}
```

## 🚀 Benefits of Refactored Architecture

### **Maintainability**
- Clear separation of concerns
- Easy to locate and modify specific functionality
- Reduced coupling between components

### **Testability**
- Services can be unit tested independently
- Hooks can be tested with React Testing Library
- Components have clear, testable interfaces

### **Scalability**
- Easy to add new features without affecting existing code
- Service layer can be extended with new capabilities
- Component library can grow organically

### **Reusability**
- Components can be used independently
- Services can be shared across different parts of the application
- Hooks encapsulate reusable logic

### **Type Safety**
- Comprehensive TypeScript coverage
- Clear interfaces prevent runtime errors
- Better IDE support and autocomplete

## 🔄 Migration Guide

### **From Old CodeCanvas**
The old monolithic `CodeCanvas.tsx` has been replaced with this modular structure. Update imports:

```typescript
// Old
import CodeCanvas from './components/CodeCanvas';

// New
import { CodeCanvas } from './components/CodeCanvas';
// or
import { CodeCanvasStandalone } from './components/CodeCanvas';
```

### **Accessing Internal Components**
```typescript
// Import specific components if needed
import { 
  CodeEditor, 
  ConsolePanel, 
  TabBar,
  Toolbar 
} from './components/CodeCanvas';
```

### **Using Services Directly**
```typescript
// Access services for custom integrations
import { 
  CompilerService, 
  ScreenshotService, 
  TemplateService 
} from './components/CodeCanvas';

const compiler = CompilerService.getInstance();
const result = await compiler.compileTypeScript(code);
```

This modular architecture provides a solid foundation for future enhancements while maintaining backward compatibility and improving code quality.
