// Main exports for the CodeCanvas module
export { default as CodeCanvas } from './CodeCanvas';
export { default as CodeCanvasStandalone } from './CodeCanvasStandalone';
export { default as AICodeCanvasDemo } from './AICodeCanvasDemo';

// Export types
export type { CodeCanvasProps, Tab, CodeError, ConsoleMessage } from './types';

// Export hooks
export { useCodeCanvasState } from './hooks/useCodeCanvasState';
export { useCodeExecution } from './hooks/useCodeExecution';
export { useTemplateManager } from './hooks/useTemplateManager';

// Export components
export { default as Toolbar } from './components/Toolbar';
export { default as TabBar } from './components/TabBar';
export { default as CodeEditor } from './components/CodeEditor';
export { default as ConsolePanel } from './components/ConsolePanel';
export { default as ErrorDisplay } from './components/ErrorDisplay';

// Export services
export { ScreenshotService } from './services/ScreenshotService';
export { TemplateService } from './services/TemplateService';
export { CompilerService } from './services/CompilerService';
