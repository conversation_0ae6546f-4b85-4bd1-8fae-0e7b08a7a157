import { useReducer, useCallback } from 'react';
import { codeCanvasReducer, initialState } from '../state/reducer';
import { Tab, CodeError, ConsoleMessage, SavedProject } from '../types';

export const useCodeCanvasState = () => {
  const [state, dispatch] = useReducer(codeCanvasReducer, initialState);

  // Code management actions
  const setCode = useCallback((tab: Tab, code: string) => {
    dispatch({ type: 'SET_CODE', payload: { tab, code } });
  }, []);

  const setActiveTab = useCallback((tab: Tab) => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: tab });
  }, []);

  const setTypeScriptMode = useCallback((enabled: boolean) => {
    dispatch({ type: 'SET_TYPESCRIPT_MODE', payload: enabled });
  }, []);

  const setTailwindMode = useCallback((enabled: boolean) => {
    dispatch({ type: 'SET_TAILWIND_MODE', payload: enabled });
  }, []);

  // Execution state actions
  const setExecuting = useCallback((executing: boolean) => {
    dispatch({ type: 'SET_EXECUTING', payload: executing });
  }, []);

  const setCompiledJs = useCallback((js: string) => {
    dispatch({ type: 'SET_COMPILED_JS', payload: js });
  }, []);

  // Error management actions
  const setErrors = useCallback((type: 'js' | 'ts', errors: CodeError[]) => {
    dispatch({ type: 'SET_ERRORS', payload: { type, errors } });
  }, []);

  // Console management actions
  const addConsoleLog = useCallback((log: ConsoleMessage) => {
    dispatch({ type: 'ADD_CONSOLE_LOG', payload: log });
  }, []);

  const clearConsole = useCallback(() => {
    dispatch({ type: 'CLEAR_CONSOLE' });
  }, []);

  // AI tracking actions
  const incrementKeystrokes = useCallback((count: number) => {
    dispatch({ type: 'INCREMENT_KEYSTROKES', payload: count });
  }, []);

  const incrementTabSwitches = useCallback(() => {
    dispatch({ type: 'INCREMENT_TAB_SWITCHES' });
  }, []);

  const incrementCodeExecutions = useCallback(() => {
    dispatch({ type: 'INCREMENT_CODE_EXECUTIONS' });
  }, []);

  const incrementErrorsEncountered = useCallback((count: number) => {
    dispatch({ type: 'INCREMENT_ERRORS_ENCOUNTERED', payload: count });
  }, []);

  // UI state actions
  const setTemplateDropdown = useCallback((show: boolean) => {
    dispatch({ type: 'SET_TEMPLATE_DROPDOWN', payload: show });
  }, []);

  const setSaveDialog = useCallback((show: boolean) => {
    dispatch({ type: 'SET_SAVE_DIALOG', payload: show });
  }, []);

  const setLoadDialog = useCallback((show: boolean) => {
    dispatch({ type: 'SET_LOAD_DIALOG', payload: show });
  }, []);

  const setProjectName = useCallback((name: string) => {
    dispatch({ type: 'SET_PROJECT_NAME', payload: name });
  }, []);

  const setSavedProjects = useCallback((projects: SavedProject[]) => {
    dispatch({ type: 'SET_SAVED_PROJECTS', payload: projects });
  }, []);

  // Fullscreen actions
  const setFullscreen = useCallback((fullscreen: boolean) => {
    dispatch({ type: 'SET_FULLSCREEN', payload: fullscreen });
  }, []);

  const setConsoleInFullscreen = useCallback((show: boolean) => {
    dispatch({ type: 'SET_CONSOLE_IN_FULLSCREEN', payload: show });
  }, []);

  // Utility actions
  const resetCode = useCallback(() => {
    dispatch({ type: 'RESET_CODE' });
  }, []);

  // Computed values
  const getCurrentCode = useCallback(() => {
    if (state.activeTab === "html") return state.html;
    if (state.activeTab === "css") return state.css;
    if (state.activeTab === "js" && !state.useTypeScript) return state.js;
    if (state.activeTab === "ts" && state.useTypeScript) return state.ts;
    return "";
  }, [state.activeTab, state.html, state.css, state.js, state.ts, state.useTypeScript]);

  const setCurrentCode = useCallback((code: string) => {
    if (state.activeTab === "html") dispatch({ type: 'SET_CODE', payload: { tab: 'html', code } });
    else if (state.activeTab === "css") dispatch({ type: 'SET_CODE', payload: { tab: 'css', code } });
    else if (state.activeTab === "js" && !state.useTypeScript) dispatch({ type: 'SET_CODE', payload: { tab: 'js', code } });
    else if (state.activeTab === "ts" && state.useTypeScript) dispatch({ type: 'SET_CODE', payload: { tab: 'ts', code } });
  }, [state.activeTab, state.useTypeScript]); // Use dispatch directly

  const getLanguageForTab = useCallback((tab: Tab): string => {
    switch (tab) {
      case "html": return "markup";
      case "css": return "css";
      case "js": return "javascript";
      case "ts": return "typescript";
      default: return "plain";
    }
  }, []);

  return {
    // State
    state,
    
    // Actions
    setCode,
    setActiveTab,
    setTypeScriptMode,
    setTailwindMode,
    setExecuting,
    setCompiledJs,
    setErrors,
    addConsoleLog,
    clearConsole,
    incrementKeystrokes,
    incrementTabSwitches,
    incrementCodeExecutions,
    incrementErrorsEncountered,
    setTemplateDropdown,
    setSaveDialog,
    setLoadDialog,
    setProjectName,
    setSavedProjects,
    setFullscreen,
    setConsoleInFullscreen,
    resetCode,
    
    // Computed values
    getCurrentCode,
    setCurrentCode,
    getLanguageForTab,
  };
};
