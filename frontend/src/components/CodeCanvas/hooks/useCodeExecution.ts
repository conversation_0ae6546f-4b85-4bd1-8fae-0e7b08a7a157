import { useCallback, useRef, useEffect, useMemo } from 'react';
import { CompilerService } from '../services/CompilerService';
import { ConsoleMessage, CodeError } from '../types';

interface UseCodeExecutionProps {
  html: string;
  css: string;
  js: string;
  ts: string;
  compiledJs: string;
  useTypeScript: boolean;
  useTailwind: boolean;
  onSetExecuting: (executing: boolean) => void;
  onSetCompiledJs: (js: string) => void;
  onSetTsErrors: (errors: CodeError[]) => void;
  onAddConsoleLog: (log: ConsoleMessage) => void;
  onClearConsole: () => void;
  onIncrementCodeExecutions: () => void;
  onIncrementErrorsEncountered: (count: number) => void;
}

export const useCodeExecution = ({
  html,
  css,
  js,
  ts,
  compiledJs,
  useTypeScript,
  useTailwind,
  onSetExecuting,
  onSetCompiledJs,
  onSetTsErrors,
  onAddConsoleLog,
  onClearConsole,
  onIncrementCodeExecutions,
  onIncrementErrorsEncountered
}: UseCodeExecutionProps) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const executionTimeoutRef = useRef<number | null>(null);
  const compilerService = CompilerService.getInstance();

  // Compile TypeScript when TS code changes
  useEffect(() => {
    if (ts.trim()) {
      compilerService.compileTypeScript(ts).then(({ js: compiledJs, errors }) => {
        onSetCompiledJs(compiledJs);
        onSetTsErrors(errors);

        if (errors.length > 0) {
          onIncrementErrorsEncountered(errors.length);
        }
      });
    } else {
      onSetCompiledJs('');
      onSetTsErrors([]);
    }
  }, [ts]); // Remove function dependencies to prevent infinite loops

  // Generate source document - memoize to prevent infinite re-renders
  const generateSrcDoc = useCallback(() => {
    const finalJsCode = useTypeScript && compiledJs ? compiledJs : js;
    return compilerService.generateSourceDoc(html, css, finalJsCode, useTailwind);
  }, [html, css, js, compiledJs, useTypeScript, useTailwind]); // Remove compilerService from deps

  // Handle console messages from iframe
  const handleConsoleMessage = useCallback((event: MessageEvent) => {
    if (event.data?.type === 'console') {
      const newMessage: ConsoleMessage = {
        type: event.data.level,
        message: event.data.message,
        timestamp: new Date(),
        line: event.data.line,
        column: event.data.column,
      };

      onAddConsoleLog(newMessage);

      if (event.data.level === 'error') {
        onIncrementErrorsEncountered(1);
      }
    }
  }, []); // Remove function dependencies to prevent re-creating listener

  // Set up message listener
  useEffect(() => {
    window.addEventListener('message', handleConsoleMessage);
    return () => window.removeEventListener('message', handleConsoleMessage);
  }, [handleConsoleMessage]);

  // Run code
  const runCode = useCallback(() => {
    if (iframeRef.current) {
      onSetExecuting(true);
      onClearConsole();
      onIncrementCodeExecutions();

      // Clear any existing timeout
      if (executionTimeoutRef.current) {
        clearTimeout(executionTimeoutRef.current);
      }

      // Set a backup timeout in case the iframe doesn't respond
      executionTimeoutRef.current = window.setTimeout(() => {
        onSetExecuting(false);
        onAddConsoleLog({
          type: 'error',
          message: 'Code execution timed out. The iframe may be unresponsive.',
          timestamp: new Date()
        });
      }, 6000);

      iframeRef.current.srcdoc = generateSrcDoc();

      // Reset execution state after a short delay
      setTimeout(() => {
        onSetExecuting(false);
        if (executionTimeoutRef.current) {
          clearTimeout(executionTimeoutRef.current);
          executionTimeoutRef.current = null;
        }
      }, 1000);
    }
  }, [generateSrcDoc]); // Only depend on generateSrcDoc

  // Auto-update preview
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (iframeRef.current) {
        iframeRef.current.srcdoc = generateSrcDoc();
      }
    }, 500);

    return () => clearTimeout(timeout);
  }, [generateSrcDoc]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (executionTimeoutRef.current) {
        clearTimeout(executionTimeoutRef.current);
      }
    };
  }, []);

  // Memoize srcDoc to prevent recalculation on every render
  const srcDoc = useMemo(() => generateSrcDoc(), [generateSrcDoc]);

  return {
    iframeRef,
    runCode,
    srcDoc
  };
};
