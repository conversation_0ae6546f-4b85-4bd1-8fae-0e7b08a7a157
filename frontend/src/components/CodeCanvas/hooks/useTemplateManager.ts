import { useCallback, useEffect } from 'react';
import { TemplateService } from '../services/TemplateService';
import { CodeTemplate, SavedProject, Tab } from '../types';

interface UseTemplateManagerProps {
  activeTab: Tab;
  useTypeScript: boolean;
  savedProjects: SavedProject[];
  onSetSavedProjects: (projects: SavedProject[]) => void;
  onSetCode: (tab: Tab, code: string) => void;
  onSetProjectName: (name: string) => void;
}

export const useTemplateManager = ({
  activeTab,
  useTypeScript,
  savedProjects,
  onSetSavedProjects,
  onSetCode,
  onSetProjectName
}: UseTemplateManagerProps) => {
  const templateService = TemplateService.getInstance();

  // Load saved projects on mount
  useEffect(() => {
    const projects = templateService.getSavedProjects();
    onSetSavedProjects(projects);
  }, [onSetSavedProjects]);

  // Get templates for current tab
  const getTemplatesForCurrentTab = useCallback((): CodeTemplate[] => {
    const currentTabKey = (activeTab === 'ts' && useTypeScript) ? 'ts' : 
                         (activeTab === 'js' && !useTypeScript) ? 'js' : activeTab;
    return templateService.getTemplatesForTab(currentTabKey);
  }, [activeTab, useTypeScript]);

  // Load template
  const loadTemplate = useCallback((template: CodeTemplate) => {
    if (activeTab === "html") onSetCode("html", template.code);
    else if (activeTab === "css") onSetCode("css", template.code);
    else if (activeTab === "js" && !useTypeScript) onSetCode("js", template.code);
    else if (activeTab === "ts" && useTypeScript) onSetCode("ts", template.code);
  }, [activeTab, useTypeScript, onSetCode]);

  // Save project
  const saveProject = useCallback((projectData: {
    name: string;
    html: string;
    css: string;
    js: string;
    ts: string;
  }) => {
    const savedProject = templateService.saveProject(projectData);
    const updatedProjects = templateService.getSavedProjects();
    onSetSavedProjects(updatedProjects);
    return savedProject;
  }, [onSetSavedProjects]);

  // Load project
  const loadProject = useCallback((project: SavedProject) => {
    onSetCode("html", project.html);
    onSetCode("css", project.css);
    onSetCode("js", project.js);
    onSetCode("ts", project.ts);
    onSetProjectName(project.name);
  }, [onSetCode, onSetProjectName]);

  // Delete project
  const deleteProject = useCallback((id: string) => {
    const success = templateService.deleteProject(id);
    if (success) {
      const updatedProjects = templateService.getSavedProjects();
      onSetSavedProjects(updatedProjects);
    }
    return success;
  }, [onSetSavedProjects]);

  // Export project
  const exportProject = useCallback((project: SavedProject): string => {
    return templateService.exportProject(project);
  }, []);

  // Import project
  const importProject = useCallback((jsonString: string): SavedProject | null => {
    const importedProject = templateService.importProject(jsonString);
    if (importedProject) {
      const updatedProjects = templateService.getSavedProjects();
      onSetSavedProjects(updatedProjects);
    }
    return importedProject;
  }, [onSetSavedProjects]);

  // Search templates
  const searchTemplates = useCallback((query: string): CodeTemplate[] => {
    return templateService.searchTemplates(query);
  }, []);

  return {
    // Template operations
    getTemplatesForCurrentTab,
    loadTemplate,
    searchTemplates,
    
    // Project operations
    saveProject,
    loadProject,
    deleteProject,
    exportProject,
    importProject,
    
    // Data
    savedProjects
  };
};
