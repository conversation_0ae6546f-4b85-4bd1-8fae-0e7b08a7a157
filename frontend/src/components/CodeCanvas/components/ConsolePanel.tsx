import React from 'react';
import { Terminal, Trash2 } from 'lucide-react';
import { ConsoleMessage } from '../types';

interface ConsolePanelProps {
  consoleLogs: ConsoleMessage[];
  onClearConsole: () => void;
}

const ConsolePanel: React.FC<ConsolePanelProps> = ({ consoleLogs, onClearConsole }) => {
  const getLogIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <span className="text-red-500 font-bold">✕</span>;
      case 'warn':
        return <span className="text-yellow-500 font-bold">⚠</span>;
      case 'info':
        return <span className="text-blue-500 font-bold">ℹ</span>;
      default:
        return <span className="text-gray-500 font-bold">›</span>;
    }
  };

  const getLogColor = (type: string) => {
    switch (type) {
      case 'error':
        return 'text-red-700 bg-red-50 border-l-red-500';
      case 'warn':
        return 'text-yellow-700 bg-yellow-50 border-l-yellow-500';
      case 'info':
        return 'text-blue-700 bg-blue-50 border-l-blue-500';
      default:
        return 'text-gray-700 bg-gray-50 border-l-gray-500';
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-900 text-green-400 font-mono">
      {/* Console header */}
      <div className="flex items-center justify-between p-3 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <Terminal size={16} />
          <span className="font-semibold">Console Output</span>
          {consoleLogs.length > 0 && (
            <span className="text-xs bg-gray-700 px-2 py-1 rounded">
              {consoleLogs.length} message{consoleLogs.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>
        <button
          onClick={onClearConsole}
          className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 rounded transition-colors"
          title="Clear console"
        >
          <Trash2 size={12} />
          Clear
        </button>
      </div>

      {/* Console content */}
      <div className="flex-1 overflow-y-auto p-3 space-y-1">
        {consoleLogs.length === 0 ? (
          <div className="text-gray-500 text-sm italic">
            Console output will appear here...
          </div>
        ) : (
          consoleLogs.map((log, index) => (
            <div
              key={index}
              className={`p-2 rounded border-l-4 text-sm ${getLogColor(log.type)}`}
            >
              <div className="flex items-start gap-2">
                {getLogIcon(log.type)}
                <div className="flex-1">
                  <div className="font-mono whitespace-pre-wrap break-words">
                    {log.message}
                  </div>
                  <div className="text-xs opacity-70 mt-1">
                    {log.timestamp.toLocaleTimeString()}
                    {log.line && (
                      <span className="ml-2">
                        Line {log.line}{log.column && `:${log.column}`}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Console footer with tips */}
      <div className="p-2 bg-gray-800 border-t border-gray-700 text-xs text-gray-400">
        <div className="flex items-center justify-between">
          <span>Use console.log(), console.error(), console.warn() in your code</span>
          <span>{consoleLogs.length} / ∞</span>
        </div>
      </div>
    </div>
  );
};

export default ConsolePanel;
