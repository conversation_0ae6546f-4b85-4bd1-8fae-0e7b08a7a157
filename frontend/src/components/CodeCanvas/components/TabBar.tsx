import React from 'react';
import { <PERSON>, Eye, Terminal } from 'lucide-react';
import { Tab, CodeError } from '../types';

interface TabBarProps {
  activeTab: Tab;
  useTypeScript: boolean;
  errors: CodeError[];
  tsErrors: CodeError[];
  consoleLogs: any[];
  onTabChange: (tab: Tab) => void;
}

const TabBar: React.FC<TabBarProps> = ({
  activeTab,
  useTypeScript,
  errors,
  tsErrors,
  consoleLogs,
  onTabChange
}) => {
  const renderTabButton = (tab: Tab, label: string, icon: React.ReactNode) => (
    <button
      key={tab}
      className={`px-4 py-2.5 font-medium transition-colors relative ${
        activeTab === tab
          ? "text-blue-600 bg-white border-b-2 border-blue-600"
          : "text-gray-600 hover:bg-gray-200"
      }`}
      onClick={() => onTabChange(tab)}
    >
      {icon}
      {label}
      
      {/* Error indicators */}
      {tab === "js" && !useTypeScript && errors.length > 0 && (
        <span className="ml-2 px-2 py-1 bg-red-100 text-red-700 text-xs rounded">
          {errors.length} error{errors.length > 1 ? 's' : ''}
        </span>
      )}
      {tab === "ts" && useTypeScript && tsErrors.length > 0 && (
        <span className="ml-2 px-2 py-1 bg-red-100 text-red-700 text-xs rounded">
          {tsErrors.length} error{tsErrors.length > 1 ? 's' : ''}
        </span>
      )}
    </button>
  );

  return (
    <div className="flex bg-gray-100 border-b border-gray-200">
      {renderTabButton("html", "HTML", <Code size={16} className="inline mr-2" />)}
      {renderTabButton("css", "CSS", <Code size={16} className="inline mr-2" />)}
      {useTypeScript 
        ? renderTabButton("ts", "TypeScript", <Code size={16} className="inline mr-2" />)
        : renderTabButton("js", "JavaScript", <Code size={16} className="inline mr-2" />)
      }
      {renderTabButton("preview", "Preview", <Eye size={16} className="inline mr-2" />)}
      {renderTabButton("console", `Console ${consoleLogs.length > 0 ? `(${consoleLogs.length})` : ''}`, <Terminal size={16} className="inline mr-2" />)}
    </div>
  );
};

export default TabBar;
