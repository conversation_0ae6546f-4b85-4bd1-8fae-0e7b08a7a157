import React from 'react';
import { Play, RotateCcw, ChevronDown, FileText, Save, FolderOpen, Maximize2, Minimize2, Terminal } from 'lucide-react';

interface ToolbarProps {
  useTypeScript: boolean;
  useTailwind: boolean;
  isExecuting: boolean;
  showTemplateDropdown: boolean;
  showSaveDialog: boolean;
  showLoadDialog: boolean;
  isFullscreen?: boolean;
  showConsoleInFullscreen?: boolean;
  onTypeScriptToggle: (enabled: boolean) => void;
  onTailwindToggle: (enabled: boolean) => void;
  onRunCode: () => void;
  onResetCode: () => void;
  onToggleTemplateDropdown: () => void;
  onToggleSaveDialog: () => void;
  onToggleLoadDialog: () => void;
  onToggleFullscreen?: () => void;
  onToggleConsoleInFullscreen?: () => void;
}

const Toolbar: React.FC<ToolbarProps> = ({
  useTypeScript,
  useTailwind,
  isExecuting,
  showTemplateDropdown,
  showSaveDialog,
  showLoadDialog,
  isFullscreen = false,
  showConsoleInFullscreen = false,
  onTypeScriptToggle,
  onTailwindToggle,
  onRunCode,
  onResetCode,
  onToggleTemplateDropdown,
  onToggleSaveDialog,
  onToggleLoadDialog,
  onToggleFullscreen,
  onToggleConsoleInFullscreen
}) => {
  return (
    <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
      {/* Left side - Title and toggles */}
      <div className="flex items-center gap-4">
        <h2 className="text-xl font-semibold text-gray-800 m-0">Interactive Code Canvas</h2>
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={useTypeScript}
              onChange={(e) => onTypeScriptToggle(e.target.checked)}
              className="rounded"
            />
            <span className="text-gray-600">Use TypeScript</span>
          </label>
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={useTailwind}
              onChange={(e) => onTailwindToggle(e.target.checked)}
              className="rounded"
            />
            <span className="text-gray-600">Enable Tailwind CSS</span>
          </label>
        </div>
      </div>

      {/* Right side - Action buttons */}
      <div className="flex items-center gap-2">
        {/* Template dropdown */}
        <div className="relative">
          <button
            onClick={onToggleTemplateDropdown}
            className="flex items-center gap-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
          >
            <FileText size={16} />
            Templates
            <ChevronDown size={14} />
          </button>
        </div>

        {/* Save button */}
        <button
          onClick={onToggleSaveDialog}
          className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
        >
          <Save size={16} />
          Save
        </button>

        {/* Load button */}
        <button
          onClick={onToggleLoadDialog}
          className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
        >
          <FolderOpen size={16} />
          Load
        </button>

        {/* Reset button */}
        <button
          onClick={onResetCode}
          className="flex items-center gap-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
        >
          <RotateCcw size={16} />
          Reset
        </button>

        {/* Fullscreen toggle button */}
        {onToggleFullscreen && (
          <button
            onClick={onToggleFullscreen}
            className="flex items-center gap-2 px-3 py-2 bg-gray-200 text-gray-700 hover:bg-gray-300 rounded-lg transition-colors text-sm"
            title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
          >
            {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </button>
        )}

        {/* Console toggle button (only in fullscreen) */}
        {isFullscreen && onToggleConsoleInFullscreen && (
          <button
            onClick={onToggleConsoleInFullscreen}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors text-sm ${
              showConsoleInFullscreen
                ? 'bg-orange-600 text-white hover:bg-orange-700'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
            title={showConsoleInFullscreen ? 'Hide Console' : 'Show Console'}
          >
            <Terminal size={16} />
          </button>
        )}

        {/* Run button */}
        <button
          onClick={onRunCode}
          disabled={isExecuting}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
        >
          <Play size={16} />
          {isExecuting ? 'Running...' : 'Run Code'}
        </button>
      </div>
    </div>
  );
};

export default Toolbar;
