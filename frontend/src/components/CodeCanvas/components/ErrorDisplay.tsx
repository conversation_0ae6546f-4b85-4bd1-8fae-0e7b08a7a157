import React from 'react';
import { AlertTriangle, X } from 'lucide-react';
import { CodeError, Tab } from '../types';

interface ErrorDisplayProps {
  activeTab: Tab;
  useTypeScript: boolean;
  errors: CodeError[];
  tsErrors: CodeError[];
  onDismissError?: (index: number, type: 'js' | 'ts') => void;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  activeTab,
  useTypeScript,
  errors,
  tsErrors,
  onDismissError
}) => {
  const shouldShowJSErrors = activeTab === "js" && !useTypeScript && errors.length > 0;
  const shouldShowTSErrors = activeTab === "ts" && useTypeScript && tsErrors.length > 0;

  if (!shouldShowJSErrors && !shouldShowTSErrors) {
    return null;
  }

  const currentErrors = shouldShowJSErrors ? errors : tsErrors;
  const errorType = shouldShowJSErrors ? 'js' : 'ts';
  const errorLabel = shouldShowJSErrors ? 'JavaScript' : 'TypeScript';

  return (
    <div className="bg-red-50 border-b border-red-200">
      <div className="p-3">
        <div className="flex items-center gap-2 mb-2">
          <AlertTriangle size={16} className="text-red-600" />
          <h4 className="font-semibold text-red-800">
            {errorLabel} Errors ({currentErrors.length})
          </h4>
        </div>
        
        <div className="space-y-2">
          {currentErrors.map((error, index) => (
            <div
              key={index}
              className="bg-white border border-red-200 rounded-lg p-3 shadow-sm"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-xs font-medium text-red-600 bg-red-100 px-2 py-1 rounded">
                      {error.type === 'syntax' ? 'Syntax Error' : 'Runtime Error'}
                    </span>
                    {error.line && (
                      <span className="text-xs text-gray-500">
                        Line {error.line}{error.column ? `, Column ${error.column}` : ''}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-red-700 font-mono">
                    {error.message}
                  </p>
                </div>
                
                {onDismissError && (
                  <button
                    onClick={() => onDismissError(index, errorType)}
                    className="ml-2 p-1 text-red-400 hover:text-red-600 hover:bg-red-100 rounded transition-colors"
                    title="Dismiss error"
                  >
                    <X size={14} />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Error help tips */}
        <div className="mt-3 p-2 bg-red-100 rounded-lg">
          <p className="text-xs text-red-700">
            <strong>💡 Tips:</strong>
            {errorType === 'js' ? (
              <>
                {' '}Check for missing semicolons, unmatched brackets, or undefined variables.
                Use the browser's developer tools (F12) for more detailed debugging.
              </>
            ) : (
              <>
                {' '}Check your type annotations, interface definitions, and ensure all variables are properly typed.
                TypeScript errors must be fixed before the code can be compiled.
              </>
            )}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ErrorDisplay;
