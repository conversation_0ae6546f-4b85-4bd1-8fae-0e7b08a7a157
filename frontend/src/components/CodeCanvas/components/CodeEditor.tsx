import React, { useEffect, useRef, useState } from "react";
import Prism from "prismjs";
import "prismjs/themes/prism-tomorrow.css";
import "prismjs/components/prism-markup";
import "prismjs/components/prism-css";
import "prismjs/components/prism-javascript";
import "prismjs/components/prism-typescript";
import { CodeEditorProps } from '../types';

// Tailwind CSS classes for suggestions
const TAILWIND_CLASSES = [
  // Layout
  'container', 'mx-auto', 'flex', 'grid', 'block', 'inline', 'inline-block', 'hidden',
  // Flexbox
  'flex-row', 'flex-col', 'flex-wrap', 'justify-center', 'justify-between', 'justify-around', 'items-center', 'items-start', 'items-end',
  // Grid
  'grid-cols-1', 'grid-cols-2', 'grid-cols-3', 'grid-cols-4', 'grid-cols-12', 'col-span-1', 'col-span-2', 'col-span-3',
  // Spacing
  'p-0', 'p-1', 'p-2', 'p-3', 'p-4', 'p-5', 'p-6', 'p-8', 'p-10', 'p-12',
  'px-1', 'px-2', 'px-3', 'px-4', 'px-6', 'px-8', 'py-1', 'py-2', 'py-3', 'py-4', 'py-6', 'py-8',
  'm-0', 'm-1', 'm-2', 'm-3', 'm-4', 'm-5', 'm-6', 'm-8', 'm-10', 'm-12',
  'mx-1', 'mx-2', 'mx-3', 'mx-4', 'mx-6', 'mx-8', 'my-1', 'my-2', 'my-3', 'my-4', 'my-6', 'my-8',
  // Colors
  'text-white', 'text-black', 'text-gray-100', 'text-gray-200', 'text-gray-300', 'text-gray-400', 'text-gray-500', 'text-gray-600', 'text-gray-700', 'text-gray-800', 'text-gray-900',
  'bg-white', 'bg-black', 'bg-gray-100', 'bg-gray-200', 'bg-gray-300', 'bg-gray-400', 'bg-gray-500', 'bg-gray-600', 'bg-gray-700', 'bg-gray-800', 'bg-gray-900',
  'bg-blue-100', 'bg-blue-200', 'bg-blue-300', 'bg-blue-400', 'bg-blue-500', 'bg-blue-600', 'bg-blue-700', 'bg-blue-800', 'bg-blue-900',
  'text-blue-100', 'text-blue-200', 'text-blue-300', 'text-blue-400', 'text-blue-500', 'text-blue-600', 'text-blue-700', 'text-blue-800', 'text-blue-900',
  // Typography
  'text-xs', 'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl', 'text-4xl', 'text-5xl', 'text-6xl',
  'font-thin', 'font-light', 'font-normal', 'font-medium', 'font-semibold', 'font-bold', 'font-extrabold', 'font-black',
  // Borders
  'border', 'border-0', 'border-2', 'border-4', 'border-8', 'border-gray-200', 'border-gray-300', 'border-gray-400',
  'rounded', 'rounded-sm', 'rounded-md', 'rounded-lg', 'rounded-xl', 'rounded-2xl', 'rounded-full',
  // Shadows
  'shadow', 'shadow-sm', 'shadow-md', 'shadow-lg', 'shadow-xl', 'shadow-2xl',
  // Width & Height
  'w-full', 'w-1/2', 'w-1/3', 'w-2/3', 'w-1/4', 'w-3/4', 'w-auto', 'w-screen',
  'h-full', 'h-screen', 'h-auto', 'h-64', 'h-32', 'h-16', 'h-8', 'h-4',
  // Position
  'relative', 'absolute', 'fixed', 'sticky', 'static',
  'top-0', 'right-0', 'bottom-0', 'left-0',
  // Display
  'block', 'inline-block', 'inline', 'flex', 'inline-flex', 'grid', 'inline-grid', 'hidden',
  // Hover states
  'hover:bg-gray-100', 'hover:bg-gray-200', 'hover:text-blue-600', 'hover:shadow-lg',
  // Transitions
  'transition', 'transition-colors', 'transition-transform', 'duration-200', 'duration-300', 'ease-in-out'
];

const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChange,
  language,
  placeholder = "",
  showLineNumbers = true,
  enableTailwindSuggestions = false
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const preRef = useRef<HTMLPreElement>(null);
  const [lineCount, setLineCount] = useState(1);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [suggestionPosition, setSuggestionPosition] = useState({ top: 0, left: 0 });
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(0);

  useEffect(() => {
    if (value) {
      const lines = value.split('\n').length;
      setLineCount(lines);
    }
  }, [value]);

  const [highlightedCode, setHighlightedCode] = useState("");

  useEffect(() => {
    if (value) {
      const highlighted = Prism.highlight(value, Prism.languages[language] || Prism.languages.plain, language);
      setHighlightedCode(highlighted);
    } else {
      setHighlightedCode("");
    }
  }, [value, language]);

  const handleScroll = (e: React.UIEvent<HTMLTextAreaElement>) => {
    if (preRef.current) {
      preRef.current.scrollTop = e.currentTarget.scrollTop;
      preRef.current.scrollLeft = e.currentTarget.scrollLeft;
    }
  };

  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    onChange(newValue);

    // Handle Tailwind suggestions
    if (enableTailwindSuggestions && language === 'markup') {
      handleTailwindSuggestions(e.target);
    }
  };

  const handleTailwindSuggestions = (textarea: HTMLTextAreaElement) => {
    const cursorPosition = textarea.selectionStart;
    const textBeforeCursor = textarea.value.substring(0, cursorPosition);
    
    // Check if we're inside a class attribute
    const classMatch = textBeforeCursor.match(/class\s*=\s*["']([^"']*)$/);
    
    if (classMatch) {
      const classValue = classMatch[1];
      const words = classValue.split(/\s+/);
      const currentWord = words[words.length - 1];
      
      if (currentWord.length > 0) {
        const filteredSuggestions = TAILWIND_CLASSES.filter(cls => 
          cls.toLowerCase().startsWith(currentWord.toLowerCase())
        ).slice(0, 10);
        
        if (filteredSuggestions.length > 0) {
          setSuggestions(filteredSuggestions);
          setSelectedSuggestionIndex(0);
          setShowSuggestions(true);
          
          // Calculate position for suggestions dropdown
          const rect = textarea.getBoundingClientRect();
          const textMetrics = getTextMetrics(textarea, textBeforeCursor);
          setSuggestionPosition({
            top: rect.top + textMetrics.height + 20,
            left: rect.left + textMetrics.width
          });
        } else {
          setShowSuggestions(false);
        }
      } else {
        setShowSuggestions(false);
      }
    } else {
      setShowSuggestions(false);
    }
  };

  const getTextMetrics = (textarea: HTMLTextAreaElement, text: string) => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (context) {
      const style = window.getComputedStyle(textarea);
      context.font = `${style.fontSize} ${style.fontFamily}`;
      const metrics = context.measureText(text);
      return {
        width: metrics.width,
        height: parseInt(style.lineHeight) || parseInt(style.fontSize)
      };
    }
    return { width: 0, height: 20 };
  };

  const applySuggestion = (suggestion: string) => {
    if (!textareaRef.current) return;
    
    const textarea = textareaRef.current;
    const cursorPosition = textarea.selectionStart;
    const textBeforeCursor = textarea.value.substring(0, cursorPosition);
    const textAfterCursor = textarea.value.substring(cursorPosition);
    
    // Find the class attribute and current word
    const classMatch = textBeforeCursor.match(/class\s*=\s*["']([^"']*)$/);
    if (classMatch) {
      const beforeClass = textBeforeCursor.substring(0, classMatch.index! + classMatch[0].length - classMatch[1].length);
      const classValue = classMatch[1];
      const words = classValue.split(/\s+/);
      const currentWord = words[words.length - 1];
      
      // Replace the current word with the suggestion
      const beforeCurrentWord = words.slice(0, -1).join(' ');
      const newClassValue = beforeCurrentWord ? `${beforeCurrentWord} ${suggestion}` : suggestion;
      const newValue = beforeClass + newClassValue + textAfterCursor;
      
      onChange(newValue);
      setShowSuggestions(false);
      
      // Set cursor position after the suggestion
      setTimeout(() => {
        const newCursorPosition = beforeClass.length + newClassValue.length;
        textarea.setSelectionRange(newCursorPosition, newCursorPosition);
        textarea.focus();
      }, 0);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (showSuggestions) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedSuggestionIndex(prev => 
            prev < suggestions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedSuggestionIndex(prev => 
            prev > 0 ? prev - 1 : suggestions.length - 1
          );
          break;
        case 'Enter':
        case 'Tab':
          e.preventDefault();
          applySuggestion(suggestions[selectedSuggestionIndex]);
          break;
        case 'Escape':
          setShowSuggestions(false);
          break;
      }
    }

    // Handle tab indentation
    if (e.key === 'Tab' && !showSuggestions) {
      e.preventDefault();
      const textarea = e.currentTarget;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newValue = value.substring(0, start) + '  ' + value.substring(end);
      onChange(newValue);
      
      setTimeout(() => {
        textarea.setSelectionRange(start + 2, start + 2);
      }, 0);
    }
  };

  // Add custom styles to improve syntax highlighting - matching original working version
  useEffect(() => {
    const styleId = 'code-editor-custom-styles';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .code-editor-container pre,
        .code-editor-container code,
        .code-editor-container textarea {
          text-align: left !important;
          direction: ltr !important;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace !important;
          font-size: 14px !important;
          line-height: 1.5 !important;
          letter-spacing: 0 !important;
          word-spacing: 0 !important;
        }

        .code-editor-container .token {
          text-align: inherit !important;
          font-family: inherit !important;
        }

        .code-editor-container pre {
          tab-size: 2 !important;
          -moz-tab-size: 2 !important;
        }

        .code-editor-container textarea {
          tab-size: 2 !important;
          -moz-tab-size: 2 !important;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  return (
    <div className="relative h-full font-mono text-sm">
      <div className="code-editor-container relative flex-1 overflow-hidden bg-gray-900 flex h-full">
        {/* Line numbers */}
        {showLineNumbers && (
          <div className="flex-shrink-0 w-12 bg-gray-800 border-r border-gray-700 overflow-hidden">
            <div className="p-4 pr-2 text-right text-gray-400 select-none pointer-events-none"
                 style={{
                   fontSize: '14px',
                   lineHeight: '1.5',
                   fontFamily: 'Monaco, Menlo, Ubuntu Mono, Consolas, source-code-pro, monospace',
                 }}>
              {Array.from({ length: lineCount }, (_, i) => (
                <div key={i + 1} className="leading-6">
                  {i + 1}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Editor container */}
        <div className="relative flex-1 overflow-hidden">
          {/* Syntax highlighting */}
          <pre
            ref={preRef}
            className="absolute inset-0 p-4 m-0 overflow-auto pointer-events-none whitespace-pre leading-6"
            style={{
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, source-code-pro, monospace',
              fontSize: '14px',
              lineHeight: '1.5',
              textAlign: 'left',
              direction: 'ltr',
              unicodeBidi: 'normal',
              margin: 0,
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word'
            }}
          >
            <code
              className={`language-${language}`}
              dangerouslySetInnerHTML={{ __html: highlightedCode }}
              style={{
                textAlign: 'left',
                direction: 'ltr',
                fontFamily: 'inherit',
                fontSize: 'inherit',
                lineHeight: 'inherit'
              }}
            />
          </pre>

          {/* Text input */}
          <textarea
            ref={textareaRef}
            value={value}
            onChange={handleInput}
            onScroll={handleScroll}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="absolute inset-0 p-4 m-0 resize-none outline-none bg-transparent leading-6 whitespace-pre"
            style={{
              color: 'transparent',
              caretColor: '#1f2937',
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, source-code-pro, monospace',
              fontSize: '14px',
              lineHeight: '1.5',
              textAlign: 'left',
              direction: 'ltr',
              unicodeBidi: 'normal',
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word'
            }}
            spellCheck={false}
            autoComplete="off"
            autoCorrect="off"
            autoCapitalize="off"
          />
        </div>

        {/* Tailwind suggestions dropdown */}
        {showSuggestions && (
          <div
            className="fixed bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-48 overflow-y-auto"
            style={{
              top: suggestionPosition.top,
              left: suggestionPosition.left,
              minWidth: '200px'
            }}
          >
            {suggestions.map((suggestion, index) => (
              <div
                key={suggestion}
                className={`px-3 py-2 cursor-pointer text-sm ${
                  index === selectedSuggestionIndex
                    ? 'bg-blue-100 text-blue-800'
                    : 'hover:bg-gray-100'
                }`}
                onClick={() => applySuggestion(suggestion)}
              >
                <span className="font-mono">{suggestion}</span>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default CodeEditor;
