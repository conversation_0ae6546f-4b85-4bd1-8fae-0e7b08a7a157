import React, { useState } from 'react';
import { Category, Topic } from '../types';

// Types
interface CurriculumData {
  curriculum: Category[];
  [key: string]: any;
}

interface CurriculumEditorProps {
  curriculum: CurriculumData;
  onCurriculumChange: (curriculum: CurriculumData) => void;
}

const CurriculumEditor: React.FC<CurriculumEditorProps> = ({ curriculum, onCurriculumChange }) => {
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editingTopic, setEditingTopic] = useState<string | null>(null);

  const handleAddCategory = (): void => {
    const newCategory: Category = {
      id: `category-${Date.now()}`,
      title: 'New Category',
      description: '',
      topics: []
    };

    const updatedCurriculum: CurriculumData = {
      ...curriculum,
      curriculum: [...curriculum.curriculum, newCategory]
    };

    onCurriculumChange(updatedCurriculum);
    setEditingCategory(newCategory.id);
  };

  const handleDeleteCategory = (categoryId: string): void => {
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.filter(cat => cat.id !== categoryId)
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  const handleUpdateCategory = (categoryId: string, field: keyof Category, value: string) => {
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => 
        cat.id === categoryId ? { ...cat, [field]: value } : cat
      )
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  const handleAddTopic = (categoryId: string) => {
    const newTopic: Topic = {
      id: `topic-${Date.now()}`,
      title: 'New Topic',
      description: ''
    };
    
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => 
        cat.id === categoryId 
          ? { ...cat, topics: [...cat.topics, newTopic] }
          : cat
      )
    };
    
    onCurriculumChange(updatedCurriculum);
    setEditingTopic(newTopic.id);
  };

  const handleDeleteTopic = (categoryId: string, topicId: string) => {
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => 
        cat.id === categoryId 
          ? { ...cat, topics: cat.topics.filter(topic => topic.id !== topicId) }
          : cat
      )
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  const handleUpdateTopic = (categoryId: string, topicId: string, field: keyof Topic, value: string) => {
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => 
        cat.id === categoryId 
          ? {
              ...cat,
              topics: cat.topics.map(topic => 
                topic.id === topicId ? { ...topic, [field]: value } : topic
              )
            }
          : cat
      )
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  const handleMoveCategoryUp = (categoryIndex: number) => {
    if (categoryIndex === 0) return;
    
    const newCurriculum = [...curriculum.curriculum];
    [newCurriculum[categoryIndex - 1], newCurriculum[categoryIndex]] = 
    [newCurriculum[categoryIndex], newCurriculum[categoryIndex - 1]];
    
    onCurriculumChange({ ...curriculum, curriculum: newCurriculum });
  };

  const handleMoveCategoryDown = (categoryIndex: number) => {
    if (categoryIndex === curriculum.curriculum.length - 1) return;
    
    const newCurriculum = [...curriculum.curriculum];
    [newCurriculum[categoryIndex], newCurriculum[categoryIndex + 1]] = 
    [newCurriculum[categoryIndex + 1], newCurriculum[categoryIndex]];
    
    onCurriculumChange({ ...curriculum, curriculum: newCurriculum });
  };

  const handleMoveTopicUp = (categoryId: string, topicIndex: number) => {
    if (topicIndex === 0) return;
    
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => {
        if (cat.id === categoryId) {
          const newTopics = [...cat.topics];
          [newTopics[topicIndex - 1], newTopics[topicIndex]] = 
          [newTopics[topicIndex], newTopics[topicIndex - 1]];
          return { ...cat, topics: newTopics };
        }
        return cat;
      })
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  const handleMoveTopicDown = (categoryId: string, topicIndex: number, topicsLength: number) => {
    if (topicIndex === topicsLength - 1) return;
    
    const updatedCurriculum = {
      ...curriculum,
      curriculum: curriculum.curriculum.map(cat => {
        if (cat.id === categoryId) {
          const newTopics = [...cat.topics];
          [newTopics[topicIndex], newTopics[topicIndex + 1]] = 
          [newTopics[topicIndex + 1], newTopics[topicIndex]];
          return { ...cat, topics: newTopics };
        }
        return cat;
      })
    };
    
    onCurriculumChange(updatedCurriculum);
  };

  return (
    <div className="bg-gray-50 p-4 rounded-lg">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold">Edit Curriculum</h3>
        <button onClick={handleAddCategory} className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
          + Add Category
        </button>
      </div>

      <div className="space-y-4">
        {curriculum.curriculum.map((category, categoryIndex) => (
          <div key={category.id} className="bg-white p-4 rounded-md shadow">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="flex flex-col mr-2">
                  <button
                    onClick={() => handleMoveCategoryUp(categoryIndex)}
                    disabled={categoryIndex === 0}
                    className="text-gray-500 hover:text-gray-700 disabled:opacity-50"
                    title="Move up"
                  >
                    ↑
                  </button>
                  <button
                    onClick={() => handleMoveCategoryDown(categoryIndex)}
                    disabled={categoryIndex === curriculum.curriculum.length - 1}
                    className="text-gray-500 hover:text-gray-700 disabled:opacity-50"
                    title="Move down"
                  >
                    ↓
                  </button>
                </div>
                {editingCategory === category.id ? (
                  <input
                    type="text"
                    value={category.title}
                    onChange={(e) => handleUpdateCategory(category.id, 'title', e.target.value)}
                    onBlur={() => setEditingCategory(null)}
                    onKeyPress={(e) => e.key === 'Enter' && setEditingCategory(null)}
                    className="text-lg font-semibold border-b-2 border-blue-500 focus:outline-none"
                    autoFocus
                  />
                ) : (
                  <h4 
                    className="text-lg font-semibold cursor-pointer"
                    onClick={() => setEditingCategory(category.id)}
                  >
                    {category.title}
                  </h4>
                )}
              </div>
              <button
                onClick={() => handleDeleteCategory(category.id)}
                className="text-red-500 hover:text-red-700"
                title="Delete category"
              >
                ×
              </button>
            </div>

            <textarea
              value={category.description}
              onChange={(e) => handleUpdateCategory(category.id, 'description', e.target.value)}
              placeholder="Category description..."
              className="w-full p-2 border rounded-md mb-2"
              rows={2}
            />

            <div className="ml-8 space-y-2">
              {category.topics.map((topic, topicIndex) => (
                <div key={topic.id} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                  <div className="flex items-center">
                    <div className="flex flex-col mr-2">
                      <button
                        onClick={() => handleMoveTopicUp(category.id, topicIndex)}
                        disabled={topicIndex === 0}
                        className="text-gray-500 hover:text-gray-700 disabled:opacity-50"
                        title="Move up"
                      >
                        ↑
                      </button>
                      <button
                        onClick={() => handleMoveTopicDown(category.id, topicIndex, category.topics.length)}
                        disabled={topicIndex === category.topics.length - 1}
                        className="text-gray-500 hover:text-gray-700 disabled:opacity-50"
                        title="Move down"
                      >
                        ↓
                      </button>
                    </div>
                    {editingTopic === topic.id ? (
                      <input
                        type="text"
                        value={topic.title}
                        onChange={(e) => handleUpdateTopic(category.id, topic.id, 'title', e.target.value)}
                        onBlur={() => setEditingTopic(null)}
                        onKeyPress={(e) => e.key === 'Enter' && setEditingTopic(null)}
                        className="border-b-2 border-blue-500 focus:outline-none"
                        autoFocus
                      />
                    ) : (
                      <span 
                        className="cursor-pointer"
                        onClick={() => setEditingTopic(topic.id)}
                      >
                        {topic.title}
                      </span>
                    )}
                  </div>
                  <input
                    type="text"
                    value={topic.description}
                    onChange={(e) => handleUpdateTopic(category.id, topic.id, 'description', e.target.value)}
                    placeholder="Topic description..."
                    className="w-1/2 p-1 border rounded-md ml-4"
                  />
                  <button
                    onClick={() => handleDeleteTopic(category.id, topic.id)}
                    className="text-red-500 hover:text-red-700"
                    title="Delete topic"
                  >
                    ×
                  </button>
                </div>
              ))}
              
              <button
                onClick={() => handleAddTopic(category.id)}
                className="text-blue-500 hover:text-blue-700 mt-2"
              >
                + Add Topic
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CurriculumEditor;