import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { GeneratedCourse } from './UniversityCourseCreation';

interface Topic {
  id: string;
  title: string;
}

interface CurriculumCategory {
  id: string;
  title: string;
  topics: Topic[];
}

interface Module {
  id: string;
  title: string;
  description: string;
  duration: string;
  order: number;
  prerequisites: string[];
  learningObjectives: string[];
  curriculum: CurriculumCategory[];
}

interface Course {
  title: string;
  description: string;
  duration: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  learningOutcomes: string[];
  modules: Module[];
}

interface CourseEditorProps {
  course: GeneratedCourse;
  onSave: (course: { course: Course; modules: Module[] }) => void;
  onCancel: () => void;
  onBackToGeneration: () => void;
}

function CourseEditor({ course, onSave, onCancel, onBackToGeneration }: CourseEditorProps) {
  const { selectedLanguage } = useLanguage();
  const [editedCourse, setEditedCourse] = useState<Course>({
    ...course.course,
    modules: [...course.modules]
  });
  const [activeTab, setActiveTab] = useState('overview');
  const [expandedModule, setExpandedModule] = useState<number | null>(null);

  const handleCourseInfoChange = (field: keyof Course, value: string) => {
    setEditedCourse(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLearningOutcomeChange = (index: number, value: string) => {
    const newOutcomes = [...editedCourse.learningOutcomes];
    newOutcomes[index] = value;
    setEditedCourse(prev => ({
      ...prev,
      learningOutcomes: newOutcomes
    }));
  };

  const addLearningOutcome = () => {
    setEditedCourse(prev => ({
      ...prev,
      learningOutcomes: [...prev.learningOutcomes, 'New learning outcome']
    }));
  };

  const removeLearningOutcome = (index: number) => {
    const newOutcomes = editedCourse.learningOutcomes.filter((_, i) => i !== index);
    setEditedCourse(prev => ({
      ...prev,
      learningOutcomes: newOutcomes
    }));
  };

  const handleModuleChange = (moduleIndex: number, field: keyof Module, value: string) => {
    const newModules = [...editedCourse.modules];
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      [field]: value
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const handleObjectiveChange = (moduleIndex: number, objIndex: number, value: string) => {
    const newModules = [...editedCourse.modules];
    const newObjectives = [...newModules[moduleIndex].learningObjectives];
    newObjectives[objIndex] = value;
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      learningObjectives: newObjectives
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const addObjective = (moduleIndex: number) => {
    const newModules = [...editedCourse.modules];
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      learningObjectives: [...newModules[moduleIndex].learningObjectives, 'New learning objective']
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const removeObjective = (moduleIndex: number, objIndex: number) => {
    const newModules = [...editedCourse.modules];
    const newObjectives = newModules[moduleIndex].learningObjectives.filter((_, i) => i !== objIndex);
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      learningObjectives: newObjectives
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const moveModule = (fromIndex: number, toIndex: number) => {
    const newModules = [...editedCourse.modules];
    const [movedModule] = newModules.splice(fromIndex, 1);
    newModules.splice(toIndex, 0, movedModule);
    
    newModules.forEach((module, index) => {
      module.order = index + 1;
    });
    
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const removeModule = (moduleIndex: number) => {
    if (window.confirm('Are you sure you want to remove this module? This action cannot be undone.')) {
      const newModules = editedCourse.modules.filter((_, i) => i !== moduleIndex);
      newModules.forEach((module, index) => {
        module.order = index + 1;
      });
      setEditedCourse(prev => ({
        ...prev,
        modules: newModules
      }));
    }
  };

  const addNewModule = () => {
    const newModule: Module = {
      id: `module-${Date.now()}`,
      title: 'New Module',
      description: 'Description for the new module',
      duration: '2 weeks',
      order: editedCourse.modules.length + 1,
      prerequisites: [],
      learningObjectives: ['New learning objective'],
      curriculum: [
        {
          id: `category-${Date.now()}`,
          title: 'New Category',
          topics: [
            {
              id: `topic-${Date.now()}`,
              title: 'New Topic'
            }
          ]
        }
      ]
    };

    setEditedCourse(prev => ({
      ...prev,
      modules: [...prev.modules, newModule]
    }));
  };

  const handleCurriculumCategoryChange = (moduleIndex: number, categoryIndex: number, field: keyof CurriculumCategory, value: string) => {
    const newModules = [...editedCourse.modules];
    const newCurriculum = [...newModules[moduleIndex].curriculum];
    newCurriculum[categoryIndex] = {
      ...newCurriculum[categoryIndex],
      [field]: value
    };
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      curriculum: newCurriculum
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const addCurriculumCategory = (moduleIndex: number) => {
    const newModules = [...editedCourse.modules];
    const newCategory: CurriculumCategory = {
      id: `category-${Date.now()}`,
      title: 'New Category',
      topics: [
        {
          id: `topic-${Date.now()}`,
          title: 'New Topic'
        }
      ]
    };
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      curriculum: [...newModules[moduleIndex].curriculum, newCategory]
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const removeCurriculumCategory = (moduleIndex: number, categoryIndex: number) => {
    if (window.confirm('Are you sure you want to remove this curriculum category?')) {
      const newModules = [...editedCourse.modules];
      const newCurriculum = newModules[moduleIndex].curriculum.filter((_, i) => i !== categoryIndex);
      newModules[moduleIndex] = {
        ...newModules[moduleIndex],
        curriculum: newCurriculum
      };
      setEditedCourse(prev => ({
        ...prev,
        modules: newModules
      }));
    }
  };

  const handleTopicChange = (moduleIndex: number, categoryIndex: number, topicIndex: number, value: string) => {
    const newModules = [...editedCourse.modules];
    const newCurriculum = [...newModules[moduleIndex].curriculum];
    const newTopics = [...newCurriculum[categoryIndex].topics];
    newTopics[topicIndex] = {
      ...newTopics[topicIndex],
      title: value
    };
    newCurriculum[categoryIndex] = {
      ...newCurriculum[categoryIndex],
      topics: newTopics
    };
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      curriculum: newCurriculum
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const addTopic = (moduleIndex: number, categoryIndex: number) => {
    const newModules = [...editedCourse.modules];
    const newTopic: Topic = {
      id: `topic-${Date.now()}`,
      title: 'New Topic'
    };
    const newCurriculum = [...newModules[moduleIndex].curriculum];
    newCurriculum[categoryIndex] = {
      ...newCurriculum[categoryIndex],
      topics: [...newCurriculum[categoryIndex].topics, newTopic]
    };
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      curriculum: newCurriculum
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const removeTopic = (moduleIndex: number, categoryIndex: number, topicIndex: number) => {
    const newModules = [...editedCourse.modules];
    const newCurriculum = [...newModules[moduleIndex].curriculum];
    const newTopics = newCurriculum[categoryIndex].topics.filter((_, i) => i !== topicIndex);
    newCurriculum[categoryIndex] = {
      ...newCurriculum[categoryIndex],
      topics: newTopics
    };
    newModules[moduleIndex] = {
      ...newModules[moduleIndex],
      curriculum: newCurriculum
    };
    setEditedCourse(prev => ({
      ...prev,
      modules: newModules
    }));
  };

  const handleSave = () => {
    onSave({
      course: editedCourse,
      modules: editedCourse.modules
    });
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <button className="text-blue-500 hover:underline" onClick={onCancel}>
            ← Back to Preview
          </button>
          <h1 className="text-2xl font-bold ml-4">Customize Your Course</h1>
        </div>
        <div className="flex items-center">
          <div className="mr-4">
            🌐 {selectedLanguage.nativeName}
          </div>
          <button className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded" onClick={onBackToGeneration}>
            🔄 Generate New Course
          </button>
        </div>
      </div>

      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'overview' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
            onClick={() => setActiveTab('overview')}
          >
            📚 Course Overview
          </button>
          <button
            className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'modules' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
            onClick={() => setActiveTab('modules')}
          >
            📦 Modules ({editedCourse.modules.length})
          </button>
        </nav>
      </div>

      <div className="mt-8">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Course Information</h3>
              <div className="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                <div className="sm:col-span-6">
                  <label htmlFor="course-title" className="block text-sm font-medium text-gray-700">Course Title</label>
                  <input
                    type="text"
                    id="course-title"
                    value={editedCourse.title}
                    onChange={(e) => handleCourseInfoChange('title', e.target.value)}
                    className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
                
                <div className="sm:col-span-6">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    id="description"
                    value={editedCourse.description}
                    onChange={(e) => handleCourseInfoChange('description', e.target.value)}
                    className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                    rows={4}
                  />
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="duration" className="block text-sm font-medium text-gray-700">Duration</label>
                  <input
                    type="text"
                    id="duration"
                    value={editedCourse.duration}
                    onChange={(e) => handleCourseInfoChange('duration', e.target.value)}
                    className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
                
                <div className="sm:col-span-3">
                  <label htmlFor="difficulty" className="block text-sm font-medium text-gray-700">Difficulty</label>
                  <select
                    id="difficulty"
                    value={editedCourse.difficulty}
                    onChange={(e) => handleCourseInfoChange('difficulty', e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  >
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium leading-6 text-gray-900">Learning Outcomes</h3>
                <button className="text-sm font-medium text-indigo-600 hover:text-indigo-500" onClick={addLearningOutcome}>
                  + Add Outcome
                </button>
              </div>
              
              <div className="mt-4 space-y-4">
                {editedCourse.learningOutcomes.map((outcome, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <textarea
                      value={outcome}
                      onChange={(e) => handleLearningOutcomeChange(index, e.target.value)}
                      className="block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                      rows={2}
                    />
                    <button 
                      className="text-red-500 hover:text-red-700"
                      onClick={() => removeLearningOutcome(index)}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'modules' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium leading-6 text-gray-900">Course Modules</h3>
                <p className="mt-1 text-sm text-gray-500">Drag and drop to reorder modules. Click to expand and edit.</p>
              </div>
              <button className="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded" onClick={addNewModule}>
                + Add New Module
              </button>
            </div>
            
            <div className="space-y-4">
              {editedCourse.modules.map((module, moduleIndex) => (
                <div key={module.id} className="bg-white shadow rounded-lg">
                  <div className="p-4 flex justify-between items-center">
                    <div className="flex items-center space-x-4">
                      <div className="text-gray-500 font-medium">
                        Module {moduleIndex + 1}
                      </div>
                      <h4 className="text-lg font-bold">{module.title}</h4>
                      <span className="text-sm text-gray-500">{module.duration}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {moduleIndex > 0 && (
                        <button 
                          className="text-gray-500 hover:text-gray-700"
                          onClick={() => moveModule(moduleIndex, moduleIndex - 1)}
                        >
                          ↑
                        </button>
                      )}
                      {moduleIndex < editedCourse.modules.length - 1 && (
                        <button 
                          className="text-gray-500 hover:text-gray-700"
                          onClick={() => moveModule(moduleIndex, moduleIndex + 1)}
                        >
                          ↓
                        </button>
                      )}
                      <button 
                        className="text-gray-500 hover:text-gray-700"
                        onClick={() => setExpandedModule(expandedModule === moduleIndex ? null : moduleIndex)}
                      >
                        {expandedModule === moduleIndex ? '−' : '+'}
                      </button>
                      <button 
                        className="text-red-500 hover:text-red-700"
                        onClick={() => removeModule(moduleIndex)}
                      >
                        🗑️
                      </button>
                    </div>
                  </div>

                  {expandedModule === moduleIndex && (
                    <div className="p-4 border-t border-gray-200 space-y-6">
                      <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                        <div className="sm:col-span-6">
                          <label className="block text-sm font-medium text-gray-700">Module Title</label>
                          <input
                            type="text"
                            value={module.title}
                            onChange={(e) => handleModuleChange(moduleIndex, 'title', e.target.value)}
                            className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          />
                        </div>
                        
                        <div className="sm:col-span-6">
                          <label className="block text-sm font-medium text-gray-700">Description</label>
                          <textarea
                            value={module.description}
                            onChange={(e) => handleModuleChange(moduleIndex, 'description', e.target.value)}
                            className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                            rows={3}
                          />
                        </div>

                        <div className="sm:col-span-6">
                          <label className="block text-sm font-medium text-gray-700">Duration</label>
                          <input
                            type="text"
                            value={module.duration}
                            onChange={(e) => handleModuleChange(moduleIndex, 'duration', e.target.value)}
                            className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                          />
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center">
                          <label className="block text-sm font-medium text-gray-700">Learning Objectives</label>
                          <button 
                            className="text-sm font-medium text-indigo-600 hover:text-indigo-500"
                            onClick={() => addObjective(moduleIndex)}
                          >
                            + Add Objective
                          </button>
                        </div>
                        
                        <div className="mt-2 space-y-2">
                          {module.learningObjectives.map((objective, objIndex) => (
                            <div key={objIndex} className="flex items-center space-x-2">
                              <textarea
                                value={objective}
                                onChange={(e) => handleObjectiveChange(moduleIndex, objIndex, e.target.value)}
                                className="block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                                rows={2}
                              />
                              <button
                                className="text-red-500 hover:text-red-700"
                                onClick={() => removeObjective(moduleIndex, objIndex)}
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between items-center">
                          <label className="block text-sm font-medium text-gray-700">Curriculum Categories</label>
                          <button
                            className="text-sm font-medium text-indigo-600 hover:text-indigo-500"
                            onClick={() => addCurriculumCategory(moduleIndex)}
                          >
                            + Add Category
                          </button>
                        </div>

                        <div className="mt-2 space-y-4">
                          {module.curriculum.map((category, categoryIndex) => (
                            <div key={category.id} className="p-4 border border-gray-200 rounded-md">
                              <div className="flex justify-between items-center">
                                <input
                                  type="text"
                                  value={category.title}
                                  onChange={(e) => handleCurriculumCategoryChange(moduleIndex, categoryIndex, 'title', e.target.value)}
                                  className="text-lg font-medium leading-6 text-gray-900"
                                  placeholder="Category title"
                                />
                                <button
                                  className="text-red-500 hover:text-red-700"
                                  onClick={() => removeCurriculumCategory(moduleIndex, categoryIndex)}
                                >
                                  ×
                                </button>
                              </div>

                              <div className="mt-4">
                                <div className="flex justify-between items-center text-sm">
                                  <span className="text-gray-500">Topics ({category.topics.length})</span>
                                  <button
                                    className="font-medium text-indigo-600 hover:text-indigo-500"
                                    onClick={() => addTopic(moduleIndex, categoryIndex)}
                                  >
                                    + Add Topic
                                  </button>
                                </div>

                                <div className="mt-2 space-y-2">
                                  {category.topics.map((topic, topicIndex) => (
                                    <div key={topic.id} className="flex items-center space-x-2">
                                      <input
                                        type="text"
                                        value={topic.title}
                                        onChange={(e) => handleTopicChange(moduleIndex, categoryIndex, topicIndex, e.target.value)}
                                        className="block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                                        placeholder="Topic title"
                                      />
                                      <button
                                        className="text-red-500 hover:text-red-700"
                                        onClick={() => removeTopic(moduleIndex, categoryIndex, topicIndex)}
                                      >
                                        ×
                                      </button>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="mt-8 pt-5">
        <div className="flex justify-end">
          <button className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" onClick={onCancel}>
            Cancel Changes
          </button>
          <button className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" onClick={handleSave}>
            💾 Save Customizations
          </button>
        </div>
      </div>
    </div>
  );
}

export default CourseEditor;