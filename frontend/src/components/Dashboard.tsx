import React, { useState, useEffect } from 'react';
import axios from 'axios';

// Types
interface Subject {
  id: string;
  title: string;
  description?: string;
  has_curriculum: boolean;
  created_at: string;
  [key: string]: any;
}

interface DashboardProps {
  onSubjectSelect: (subject: Subject) => void;
  onCreateSubject: () => void;
  onCreateUniversityCourse?: () => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onSubjectSelect, onCreateSubject, onCreateUniversityCourse }) => {
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSubjects();
  }, []);

  const fetchSubjects = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const response = await axios.get<Subject[]>('http://localhost:3001/api/subjects');
      setSubjects(response.data);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching subjects:', error);
      setError('Failed to load subjects. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteSubject = async (subjectId: string, subjectTitle: string): Promise<void> => {
    if (!window.confirm(`Are you sure you want to delete "${subjectTitle}"? This will also delete all associated curricula and courses.`)) {
      return;
    }

    try {
      await axios.delete(`http://localhost:3001/api/subjects/${subjectId}`);
      setSubjects(subjects.filter(subject => subject.id !== subjectId));
    } catch (error: any) {
      console.error('Error deleting subject:', error);
      alert('Failed to delete subject. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500"></div>
        <p className="ml-4 text-lg">Loading your subjects...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <h2 className="text-2xl font-bold mb-4 text-red-600">Error</h2>
        <p>{error}</p>
        <button onClick={fetchSubjects} className="mt-4 px-4 py-2 rounded-md text-white bg-blue-600 hover:bg-blue-700">
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 md:p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Your Learning Subjects</h1>
        <div className="flex space-x-2">
          <button
            className="px-4 py-2 rounded-md text-white bg-blue-600 hover:bg-blue-700"
            onClick={onCreateSubject}
          >
            + Create New Subject
          </button>
          {onCreateUniversityCourse && (
            <button
              className="px-4 py-2 rounded-md text-white bg-purple-600 hover:bg-purple-700"
              onClick={onCreateUniversityCourse}
            >
              🎓 Create University Course
            </button>
          )}
        </div>
      </div>

      {subjects.length === 0 ? (
        <div className="text-center py-16">
          <h2 className="text-2xl font-semibold mb-2">No subjects yet</h2>
          <p className="text-gray-600 mb-4">Create your first learning subject to get started!</p>
          <button 
            className="px-6 py-3 rounded-md text-white bg-blue-600 hover:bg-blue-700 font-bold"
            onClick={onCreateSubject}
          >
            Create Your First Subject
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {subjects.map((subject) => (
            <div key={subject.id} className="bg-white rounded-lg shadow-md flex flex-col">
              <div className="p-4 flex-grow">
                <div className="flex justify-between items-start">
                  <h3 className="text-lg font-bold mb-2">{subject.title}</h3>
                  <button
                    className="text-red-500 hover:text-red-700 text-2xl"
                    onClick={() => handleDeleteSubject(subject.id, subject.title)}
                    title="Delete subject"
                  >
                    ×
                  </button>
                </div>
                
                {subject.description && (
                  <p className="text-gray-600 text-sm mb-4">{subject.description}</p>
                )}
              </div>
              
              <div className="p-4 bg-gray-50">
                <div className="flex justify-between items-center text-xs text-gray-500 mb-4">
                  <span className={subject.has_curriculum ? 'text-green-600' : 'text-yellow-600'}>
                    {subject.has_curriculum ? '✓ Curriculum ready' : '⚠ No curriculum'}
                  </span>
                  <span>
                    Created {new Date(subject.created_at).toLocaleDateString()}
                  </span>
                </div>
                
                <button
                  className="w-full px-4 py-2 rounded-md text-white font-semibold disabled:bg-gray-400 enabled:bg-blue-600 enabled:hover:bg-blue-700"
                  onClick={() => onSubjectSelect(subject)}
                  disabled={!subject.has_curriculum}
                >
                  {subject.has_curriculum ? 'Start Learning' : 'Setup Required'}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Dashboard;