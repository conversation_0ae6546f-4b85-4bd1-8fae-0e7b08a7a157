import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';

interface HeaderProps {
  onSettingsClick: () => void;
  onLanguageClick: () => void;
}

function Header({ onSettingsClick, onLanguageClick }: HeaderProps) {
  const { selectedLanguage } = useLanguage();

  return (
    <header className="flex justify-between items-center p-4 bg-gray-800 text-white">
      <h1 className="text-2xl font-bold">QtMaster.io</h1>
      <div className="flex items-center">
        <button
          className="mr-4 bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded"
          onClick={onLanguageClick}
          title="Change Language"
        >
          🌐 {selectedLanguage.nativeName}
        </button>
        <button
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          onClick={onSettingsClick}
        >
          Settings
        </button>
      </div>
    </header>
  );
}

export default Header;