import React, { useState } from 'react';
import { AICodeCanvasProvider } from '../contexts/AICodeCanvasContext';
import CodeCanvas from './CodeCanvas';
import AIAssistant from './AIAssistant';

/**
 * Demo component showing how to use the AI-observable CodeCanvas
 * This demonstrates the complete integration of AI monitoring and assistance
 */
const AICodeCanvasDemo: React.FC = () => {
  const [showAIAssistant, setShowAIAssistant] = useState(false);

  return (
    <AICodeCanvasProvider>
      <div className="relative w-full h-full">
        {/* Main CodeCanvas with AI observation enabled */}
        <CodeCanvas 
          enableAIObservation={true}
        />
        
        {/* AI Assistant that can observe and interact with the CodeCanvas */}
        <AIAssistant 
          isVisible={showAIAssistant}
          onToggle={() => setShowAIAssistant(!showAIAssistant)}
        />
      </div>
    </AICodeCanvasProvider>
  );
};

export default AICodeCanvasDemo;
