import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const DocumentationPage = () => {
  const [activeSection, setActiveSection] = useState('getting-started');

  const sections = [
    { id: 'getting-started', title: 'Getting Started', icon: '🚀' },
    { id: 'creating-subjects', title: 'Creating Subjects', icon: '📚' },
    { id: 'university-courses', title: 'University Courses', icon: '🎓' },
    { id: 'progress-tracking', title: 'Progress Tracking', icon: '📊' },
    { id: 'customization', title: 'Customization', icon: '⚙️' },
    { id: 'ai-features', title: 'AI Features', icon: '🤖' },
    { id: 'best-practices', title: 'Best Practices', icon: '💡' },
    { id: 'troubleshooting', title: 'Troubleshooting', icon: '🔧' },
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'getting-started':
        return (
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-white mb-4">Getting Started with QtMaster</h2>
            <p className="text-gray-300 text-lg mb-6">
              Welcome to QtMaster! This guide will help you get started with our AI-powered learning platform.
            </p>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-white mb-4">Quick Start</h3>
              <ol className="list-decimal list-inside space-y-3 text-gray-300">
                <li>Create your free account or sign in</li>
                <li>Choose between creating a simple subject or a comprehensive university course</li>
                <li>Describe your learning goals in natural language</li>
                <li>Let our AI generate a personalized curriculum</li>
                <li>Customize and refine the generated content</li>
                <li>Start learning and track your progress</li>
              </ol>
            </div>

            <div className="bg-blue-900 border border-blue-700 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-blue-100 mb-3">💡 Pro Tip</h3>
              <p className="text-blue-200">
                The more specific you are when describing your learning goals, the better our AI can tailor 
                the curriculum to your needs. Include your current skill level, preferred learning style, 
                and specific outcomes you want to achieve.
              </p>
            </div>
          </div>
        );

      case 'creating-subjects':
        return (
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-white mb-4">Creating Subjects</h2>
            <p className="text-gray-300 text-lg mb-6">
              Learn how to create and manage learning subjects with AI-generated curricula.
            </p>

            <div className="space-y-6">
              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">Step 1: Create a New Subject</h3>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li>Click "Create New Subject" from your dashboard</li>
                  <li>Enter a descriptive title (e.g., "React Development", "Digital Marketing")</li>
                  <li>Provide a detailed description of what you want to learn</li>
                  <li>Select your preferred language for the curriculum</li>
                </ul>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">Step 2: Generate Curriculum</h3>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li>Click "Generate Curriculum" to let AI create your learning path</li>
                  <li>Our AI will analyze your subject and create structured categories and topics</li>
                  <li>Each topic includes estimated time, difficulty level, and resources</li>
                  <li>The curriculum is automatically organized from beginner to advanced concepts</li>
                </ul>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">Step 3: Customize Your Learning Path</h3>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li>Review the generated curriculum and make adjustments</li>
                  <li>Add, remove, or modify topics to match your specific needs</li>
                  <li>Reorder topics based on your preferred learning sequence</li>
                  <li>Set personal deadlines and learning goals</li>
                </ul>
              </div>
            </div>

            <div className="bg-green-900 border border-green-700 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-green-100 mb-3">✅ Best Practices</h3>
              <ul className="list-disc list-inside space-y-2 text-green-200">
                <li>Be specific about your current skill level and goals</li>
                <li>Include context about how you plan to use the knowledge</li>
                <li>Mention any time constraints or deadlines you have</li>
                <li>Specify if you prefer hands-on projects or theoretical learning</li>
              </ul>
            </div>
          </div>
        );

      case 'university-courses':
        return (
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-white mb-4">University Courses</h2>
            <p className="text-gray-300 text-lg mb-6">
              Create comprehensive, university-style courses with multiple modules and detailed curricula.
            </p>

            <div className="space-y-6">
              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">What are University Courses?</h3>
                <p className="text-gray-300 mb-4">
                  University courses are comprehensive learning programs that include:
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li>Multiple specialized modules (typically 5-8 modules)</li>
                  <li>Detailed learning objectives and outcomes</li>
                  <li>Prerequisites and recommended background knowledge</li>
                  <li>Structured progression from basic to advanced concepts</li>
                  <li>Estimated duration (usually 12-20 weeks)</li>
                </ul>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">Creating a University Course</h3>
                <ol className="list-decimal list-inside space-y-3 text-gray-300">
                  <li>
                    <strong>Describe Your Vision:</strong> Write a detailed description of what you want to learn. 
                    Include your goals, current knowledge level, and desired outcomes.
                  </li>
                  <li>
                    <strong>AI Analysis:</strong> Our AI will analyze your description and create a structured 
                    course with multiple specialized modules.
                  </li>
                  <li>
                    <strong>Review Course Structure:</strong> Examine the generated course outline, modules, 
                    and learning objectives.
                  </li>
                  <li>
                    <strong>Customize Modules:</strong> Edit module titles, descriptions, duration, and 
                    learning objectives to match your preferences.
                  </li>
                  <li>
                    <strong>Refine Curriculum:</strong> Dive into each module to customize the detailed 
                    curriculum, topics, and learning materials.
                  </li>
                </ol>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">Course Editor Features</h3>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li><strong>Module Management:</strong> Add, remove, or reorder course modules</li>
                  <li><strong>Learning Outcomes:</strong> Define and edit specific learning outcomes</li>
                  <li><strong>Prerequisites:</strong> Set required knowledge or skills</li>
                  <li><strong>Duration Planning:</strong> Adjust time estimates for modules and topics</li>
                  <li><strong>Curriculum Editing:</strong> Modify categories and topics within each module</li>
                </ul>
              </div>
            </div>

            <div className="bg-purple-900 border border-purple-700 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-purple-100 mb-3">🎓 Example Use Cases</h3>
              <ul className="list-disc list-inside space-y-2 text-purple-200">
                <li><strong>Full-Stack Development:</strong> Complete web development bootcamp</li>
                <li><strong>Data Science Mastery:</strong> From statistics to machine learning</li>
                <li><strong>Digital Marketing:</strong> Comprehensive marketing strategy course</li>
                <li><strong>Business Analytics:</strong> Data-driven decision making program</li>
              </ul>
            </div>
          </div>
        );

      case 'progress-tracking':
        return (
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-white mb-4">Progress Tracking</h2>
            <p className="text-gray-300 text-lg mb-6">
              Monitor your learning journey with detailed analytics and insights.
            </p>

            <div className="space-y-6">
              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">Tracking Features</h3>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li><strong>Completion Status:</strong> Mark topics as completed as you learn</li>
                  <li><strong>Progress Percentage:</strong> See your overall progress for each subject/course</li>
                  <li><strong>Time Tracking:</strong> Monitor when you last accessed each topic</li>
                  <li><strong>Module Statistics:</strong> Detailed breakdown by module or category</li>
                  <li><strong>Learning Streaks:</strong> Track consistent learning habits</li>
                </ul>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">How to Track Progress</h3>
                <ol className="list-decimal list-inside space-y-3 text-gray-300">
                  <li>Open any subject or course from your dashboard</li>
                  <li>Navigate through the curriculum topics</li>
                  <li>Click the checkbox next to completed topics</li>
                  <li>View your progress statistics in the course overview</li>
                  <li>Use the dashboard to see progress across all your courses</li>
                </ol>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">Progress Analytics</h3>
                <p className="text-gray-300 mb-4">
                  Your dashboard provides comprehensive analytics including:
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li>Overall completion percentage across all subjects</li>
                  <li>Recently accessed topics and courses</li>
                  <li>Learning velocity and consistency metrics</li>
                  <li>Recommended next steps based on your progress</li>
                </ul>
              </div>
            </div>
          </div>
        );

      case 'ai-features':
        return (
          <div className="space-y-6">
            <h2 className="text-3xl font-bold text-white mb-4">AI Features</h2>
            <p className="text-gray-300 text-lg mb-6">
              Discover the powerful AI capabilities that make QtMaster unique.
            </p>

            <div className="space-y-6">
              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">Smart Persona Selection</h3>
                <p className="text-gray-300 mb-4">
                  Our AI automatically selects the most appropriate expert persona based on your subject:
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li><strong>TechMentor:</strong> Programming, software development, computer science</li>
                  <li><strong>DataSage:</strong> Data science, machine learning, AI, statistics</li>
                  <li><strong>CloudArchitect:</strong> Cloud computing, DevOps, infrastructure</li>
                  <li><strong>BusinessGuru:</strong> Business strategy, management, marketing</li>
                  <li><strong>CreativeMaster:</strong> Design, art, UX, digital media</li>
                  <li><strong>ScienceExplorer:</strong> Natural sciences, mathematics, research</li>
                </ul>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">Curriculum Generation</h3>
                <p className="text-gray-300 mb-4">
                  Our AI creates comprehensive curricula with:
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li>Logical topic progression from basic to advanced</li>
                  <li>Estimated time requirements for each topic</li>
                  <li>Difficulty levels and prerequisites</li>
                  <li>Relevant resources and learning materials</li>
                  <li>Practical exercises and projects</li>
                </ul>
              </div>

              <div className="bg-gray-800 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-white mb-4">Multi-Language Support</h3>
                <p className="text-gray-300 mb-4">
                  Generate curricula in multiple languages:
                </p>
                <ul className="list-disc list-inside space-y-2 text-gray-300">
                  <li>English (default)</li>
                  <li>Spanish, French, German</li>
                  <li>Chinese, Japanese, Korean</li>
                  <li>And many more languages supported</li>
                </ul>
              </div>
            </div>

            <div className="bg-yellow-900 border border-yellow-700 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-yellow-100 mb-3">⚡ AI Tips</h3>
              <ul className="list-disc list-inside space-y-2 text-yellow-200">
                <li>Be specific about your learning context and goals</li>
                <li>Mention your current skill level for better personalization</li>
                <li>Include time constraints or deadlines in your description</li>
                <li>Specify if you prefer theoretical or hands-on learning</li>
              </ul>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-12">
            <p className="text-gray-400">Select a section from the sidebar to view documentation.</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-slate-950">
      {/* Navigation */}
      <nav className="bg-slate-900/80 backdrop-blur-sm border-b border-slate-800 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center space-x-3 group">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 via-green-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-emerald-500/25 transition-all duration-300">
                  <span className="text-white font-bold text-xl">Q</span>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">QtMaster</span>
              </Link>
            </div>
            <div className="hidden md:flex items-center space-x-1">
              <Link to="/" className="text-slate-300 hover:text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors hover:bg-slate-800/50">
                Home
              </Link>
              <Link to="/pricing" className="text-slate-300 hover:text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors hover:bg-slate-800/50">
                Pricing
              </Link>
              <Link to="/dashboard" className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-emerald-500/25 ml-4">
                Dashboard
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col lg:flex-row gap-12">
          {/* Sidebar */}
          <div className="lg:w-1/4">
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl p-8 sticky top-24 border border-slate-700/50">
              <h3 className="text-2xl font-bold text-white mb-8 flex items-center">
                <svg className="w-6 h-6 mr-3 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                Documentation
              </h3>
              <nav className="space-y-3">
                {sections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full text-left px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${
                      activeSection === section.id
                        ? 'bg-gradient-to-r from-emerald-600 to-green-600 text-white shadow-lg shadow-emerald-500/25'
                        : 'text-slate-300 hover:text-white hover:bg-slate-700/50 hover:translate-x-1'
                    }`}
                  >
                    <span className="text-lg mr-3">{section.icon}</span>
                    {section.title}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:w-3/4">
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl p-10 border border-slate-700/50">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentationPage;
