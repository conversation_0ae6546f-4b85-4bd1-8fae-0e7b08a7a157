import { useEffect, useCallback, useRef } from 'react';
import { useAICodeCanvas } from '../contexts/AICodeCanvasContext';
import type { AICodeCanvasState, AIUserAction, AISuggestion } from '../contexts/AICodeCanvasContext';

export interface AIObserverConfig {
  enableAutoSuggestions?: boolean;
  suggestionThreshold?: {
    errorCount: number;
    timeWithoutProgress: number; // milliseconds
    keystrokesWithoutExecution: number;
  };
  analysisInterval?: number; // milliseconds
  screenshotInterval?: number; // milliseconds
}

const defaultConfig: AIObserverConfig = {
  enableAutoSuggestions: true,
  suggestionThreshold: {
    errorCount: 3,
    timeWithoutProgress: 30000, // 30 seconds
    keystrokesWithoutExecution: 100
  },
  analysisInterval: 5000, // 5 seconds
  screenshotInterval: 30000 // 30 seconds
};

export const useAICodeCanvasObserver = (config: AIObserverConfig = defaultConfig) => {
  const {
    currentState,
    userActions,
    addSuggestion,
    getCodeAnalysis,
    setAIActive,
    captureScreenshot
  } = useAICodeCanvas();

  const lastAnalysisRef = useRef<Date>(new Date());
  const lastScreenshotRef = useRef<Date>(new Date());
  const keystrokesSinceExecutionRef = useRef(0);

  // AI Analysis and Suggestion Generation
  const analyzeAndSuggest = useCallback(async () => {
    if (!currentState) return;

    const analysis = getCodeAnalysis();
    const now = new Date();
    
    // Check if user needs help based on various indicators
    const needsHelp = 
      analysis.errorRate > 50 ||
      currentState.errors.length >= (config.suggestionThreshold?.errorCount || 3) ||
      currentState.tsErrors.length >= (config.suggestionThreshold?.errorCount || 3) ||
      keystrokesSinceExecutionRef.current >= (config.suggestionThreshold?.keystrokesWithoutExecution || 100);

    if (needsHelp && config.enableAutoSuggestions) {
      // Generate contextual suggestions based on current state
      if (currentState.errors.length > 0) {
        const latestError = currentState.errors[currentState.errors.length - 1];
        addSuggestion({
          type: 'debug_help',
          message: `I noticed you have a ${latestError.type} error: "${latestError.message}". Would you like help fixing this?`,
          priority: 'high',
          targetTab: currentState.useTypeScript ? 'ts' : 'js'
        });
      }

      if (currentState.tsErrors.length > 0) {
        const latestTsError = currentState.tsErrors[currentState.tsErrors.length - 1];
        addSuggestion({
          type: 'debug_help',
          message: `TypeScript compilation error: "${latestTsError.message}". Let me help you resolve this.`,
          priority: 'high',
          targetTab: 'ts'
        });
      }

      if (keystrokesSinceExecutionRef.current > 50 && currentState.codeExecutions === 0) {
        addSuggestion({
          type: 'learning_tip',
          message: "You've written some code but haven't run it yet. Try clicking 'Run Code' to see your changes!",
          priority: 'medium'
        });
      }

      if (analysis.complexity > 70 && currentState.html.length === 0) {
        addSuggestion({
          type: 'template_suggestion',
          message: "Your code is getting complex. Would you like to start with a basic HTML template?",
          priority: 'medium',
          targetTab: 'html'
        });
      }
    }

    lastAnalysisRef.current = now;
  }, [currentState, config, addSuggestion, getCodeAnalysis]);

  // Periodic screenshot capture for AI visual analysis
  const capturePeriodicScreenshot = useCallback(async () => {
    const now = new Date();
    const timeSinceLastScreenshot = now.getTime() - lastScreenshotRef.current.getTime();
    
    if (timeSinceLastScreenshot >= (config.screenshotInterval || 30000)) {
      try {
        await captureScreenshot();
        lastScreenshotRef.current = now;
      } catch (error) {
        console.warn('Failed to capture screenshot:', error);
      }
    }
  }, [config.screenshotInterval, captureScreenshot]);

  // Monitor user actions for patterns
  const analyzeUserBehavior = useCallback(() => {
    if (!currentState || userActions.length === 0) return;

    const recentActions = userActions.slice(-10); // Last 10 actions
    const codeChangeActions = recentActions.filter(a => a.type === 'code_change');
    const executionActions = recentActions.filter(a => a.type === 'code_execution');

    // Update keystroke counter
    if (codeChangeActions.length > 0 && executionActions.length === 0) {
      keystrokesSinceExecutionRef.current += codeChangeActions.length;
    } else if (executionActions.length > 0) {
      keystrokesSinceExecutionRef.current = 0;
    }

    // Detect patterns that might indicate user is struggling
    const rapidTabSwitching = recentActions.filter(a => a.type === 'tab_switch').length > 5;
    const manyErrorsRecently = recentActions.filter(a => a.type === 'error_occurred').length > 2;
    const noExecutionRecently = executionActions.length === 0 && codeChangeActions.length > 5;

    if (rapidTabSwitching && config.enableAutoSuggestions) {
      addSuggestion({
        type: 'learning_tip',
        message: "I notice you're switching tabs frequently. Try focusing on one section at a time for better results.",
        priority: 'low'
      });
    }

    if (manyErrorsRecently && config.enableAutoSuggestions) {
      addSuggestion({
        type: 'debug_help',
        message: "You're encountering several errors. Would you like me to help you debug step by step?",
        priority: 'high'
      });
    }

    if (noExecutionRecently && config.enableAutoSuggestions) {
      addSuggestion({
        type: 'learning_tip',
        message: "Remember to test your code frequently by clicking 'Run Code' to see immediate results!",
        priority: 'medium'
      });
    }
  }, [currentState, userActions, config.enableAutoSuggestions, addSuggestion]);

  // Main observer effect
  useEffect(() => {
    if (!currentState) return;

    setAIActive(true);

    const analysisInterval = setInterval(() => {
      analyzeAndSuggest();
      analyzeUserBehavior();
      capturePeriodicScreenshot();
    }, config.analysisInterval || 5000);

    return () => {
      clearInterval(analysisInterval);
      setAIActive(false);
    };
  }, [currentState, config.analysisInterval, analyzeAndSuggest, analyzeUserBehavior, capturePeriodicScreenshot, setAIActive]);

  // Provide methods for manual AI interaction
  const manualAnalysis = useCallback(async () => {
    await analyzeAndSuggest();
    return getCodeAnalysis();
  }, [analyzeAndSuggest, getCodeAnalysis]);

  const requestHelp = useCallback((topic: string) => {
    addSuggestion({
      type: 'debug_help',
      message: `You requested help with: ${topic}. Let me analyze your code and provide assistance.`,
      priority: 'high'
    });
  }, [addSuggestion]);

  const suggestTemplate = useCallback((language: 'html' | 'css' | 'js' | 'ts') => {
    addSuggestion({
      type: 'template_suggestion',
      message: `Would you like me to suggest a ${language.toUpperCase()} template to get you started?`,
      priority: 'medium',
      targetTab: language
    });
  }, [addSuggestion]);

  return {
    // State
    isObserving: !!currentState,
    currentAnalysis: getCodeAnalysis(),
    
    // Manual actions
    manualAnalysis,
    requestHelp,
    suggestTemplate,
    captureScreenshot,
    
    // Configuration
    updateConfig: (newConfig: Partial<AIObserverConfig>) => {
      Object.assign(config, newConfig);
    }
  };
};

export default useAICodeCanvasObserver;
