import React, { create<PERSON>ontext, useContext, useState, useCallback, ReactNode } from 'react';

// Types for AI CodeCanvas Context
export interface AICodeCanvasState {
  html: string;
  css: string;
  js: string;
  ts: string;
  compiledJs: string;
  activeTab: 'html' | 'css' | 'js' | 'ts' | 'preview' | 'console';
  useTypeScript: boolean;
  useTailwind: boolean;
  isExecuting: boolean;
  errors: Array<{
    message: string;
    line?: number;
    column?: number;
    type: 'syntax' | 'runtime';
  }>;
  tsErrors: Array<{
    message: string;
    line?: number;
    column?: number;
    type: 'syntax' | 'runtime';
  }>;
  consoleLogs: Array<{
    type: 'log' | 'error' | 'warn' | 'info';
    message: string;
    timestamp: Date;
    line?: number;
    column?: number;
  }>;
  lastModified: Date;
  totalKeystrokes: number;
  tabSwitches: number;
  codeExecutions: number;
  errorsEncountered: number;
}

export interface AIUserAction {
  type: 'code_change' | 'tab_switch' | 'code_execution' | 'template_load' | 'error_occurred' | 'console_output';
  timestamp: Date;
  details: any;
  context: Partial<AICodeCanvasState>;
}

export interface AISuggestion {
  id: string;
  type: 'code_fix' | 'template_suggestion' | 'learning_tip' | 'debug_help';
  message: string;
  code?: string;
  targetTab?: 'html' | 'css' | 'js' | 'ts' | 'preview' | 'console';
  priority: 'low' | 'medium' | 'high';
  timestamp: Date;
}

export interface AICodeCanvasContextType {
  // State
  currentState: AICodeCanvasState | null;
  userActions: AIUserAction[];
  suggestions: AISuggestion[];
  isAIActive: boolean;
  
  // Actions
  updateState: (state: AICodeCanvasState) => void;
  addUserAction: (action: AIUserAction) => void;
  addSuggestion: (suggestion: Omit<AISuggestion, 'id' | 'timestamp'>) => void;
  removeSuggestion: (id: string) => void;
  clearSuggestions: () => void;
  setAIActive: (active: boolean) => void;
  
  // AI Analysis
  getCodeAnalysis: () => {
    complexity: number;
    errorRate: number;
    productivityScore: number;
    strugglingAreas: string[];
    recommendations: string[];
  };
  
  // Screenshot
  captureScreenshot: () => Promise<string>;
  lastScreenshot: string | null;
}

const AICodeCanvasContext = createContext<AICodeCanvasContextType | undefined>(undefined);

export const useAICodeCanvas = () => {
  const context = useContext(AICodeCanvasContext);
  if (context === undefined) {
    throw new Error('useAICodeCanvas must be used within an AICodeCanvasProvider');
  }
  return context;
};

// Safe version that returns null if not within provider
export const useAICodeCanvasSafe = () => {
  const context = useContext(AICodeCanvasContext);
  return context || null;
};

interface AICodeCanvasProviderProps {
  children: ReactNode;
}

export const AICodeCanvasProvider: React.FC<AICodeCanvasProviderProps> = ({ children }) => {
  const [currentState, setCurrentState] = useState<AICodeCanvasState | null>(null);
  const [userActions, setUserActions] = useState<AIUserAction[]>([]);
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
  const [isAIActive, setIsAIActive] = useState(false);
  const [lastScreenshot, setLastScreenshot] = useState<string | null>(null);

  const updateState = useCallback((state: AICodeCanvasState) => {
    setCurrentState(state);
  }, []);

  const addUserAction = useCallback((action: AIUserAction) => {
    setUserActions(prev => [...prev.slice(-99), action]); // Keep last 100 actions
  }, []);

  const addSuggestion = useCallback((suggestion: Omit<AISuggestion, 'id' | 'timestamp'>) => {
    const newSuggestion: AISuggestion = {
      ...suggestion,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date()
    };
    setSuggestions(prev => [...prev, newSuggestion]);
  }, []);

  const removeSuggestion = useCallback((id: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== id));
  }, []);

  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
  }, []);

  const setAIActive = useCallback((active: boolean) => {
    setIsAIActive(active);
  }, []);

  const getCodeAnalysis = useCallback(() => {
    if (!currentState) {
      return {
        complexity: 0,
        errorRate: 0,
        productivityScore: 0,
        strugglingAreas: [],
        recommendations: []
      };
    }

    const totalCode = currentState.html.length + currentState.css.length + 
                     currentState.js.length + currentState.ts.length;
    const totalErrors = currentState.errors.length + currentState.tsErrors.length;
    
    // Calculate complexity based on code length and structure
    const complexity = Math.min(100, Math.floor(totalCode / 50));
    
    // Calculate error rate
    const errorRate = currentState.codeExecutions > 0 ? 
      (currentState.errorsEncountered / currentState.codeExecutions) * 100 : 0;
    
    // Calculate productivity score
    const productivityScore = Math.max(0, 100 - errorRate - (totalErrors * 5));
    
    // Identify struggling areas
    const strugglingAreas: string[] = [];
    if (currentState.errors.length > 3) strugglingAreas.push('JavaScript Syntax');
    if (currentState.tsErrors.length > 3) strugglingAreas.push('TypeScript');
    if (errorRate > 50) strugglingAreas.push('Code Execution');
    if (currentState.tabSwitches > currentState.codeExecutions * 3) strugglingAreas.push('Focus');

    // Generate recommendations
    const recommendations: string[] = [];
    if (totalErrors > 0) recommendations.push('Review syntax errors before running code');
    if (errorRate > 30) recommendations.push('Test code more frequently with smaller changes');
    if (strugglingAreas.includes('Focus')) recommendations.push('Try to complete one section before switching tabs');

    return {
      complexity,
      errorRate: Math.round(errorRate),
      productivityScore: Math.round(productivityScore),
      strugglingAreas,
      recommendations
    };
  }, [currentState]);

  const captureScreenshot = useCallback(async (): Promise<string> => {
    // This will be implemented by the CodeCanvas component
    return new Promise((resolve) => {
      resolve(lastScreenshot || '');
    });
  }, [lastScreenshot]);

  const value: AICodeCanvasContextType = {
    currentState,
    userActions,
    suggestions,
    isAIActive,
    updateState,
    addUserAction,
    addSuggestion,
    removeSuggestion,
    clearSuggestions,
    setAIActive,
    getCodeAnalysis,
    captureScreenshot,
    lastScreenshot
  };

  return (
    <AICodeCanvasContext.Provider value={value}>
      {children}
    </AICodeCanvasContext.Provider>
  );
};

export default AICodeCanvasContext;
