import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Topic } from './types';

interface ContentDisplayProps {
  content: string;
  isLoading: boolean;
  selectedTopic: Topic | null;
}

function ContentDisplay({ content, isLoading, selectedTopic }: ContentDisplayProps) {
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500"></div>
        <p className="mt-4 text-lg">Generating your course...</p>
      </div>
    );
  }

  if (!selectedTopic) {
    return (
      <div className="text-center h-full flex flex-col justify-center">
        <h2 className="text-2xl font-bold mb-2">Welcome to QtMaster.io</h2>
        <p className="text-gray-600">Select a topic from the curriculum to get started.</p>
      </div>
    );
  }

  if (!content) {
    return (
      <div className="text-center h-full flex flex-col justify-center">
        <h2 className="text-2xl font-bold mb-2">{selectedTopic.title}</h2>
        <p className="text-gray-600">Click "Generate New Course" to create a personalized learning module.</p>
      </div>
    );
  }

  return (
    <div className="prose lg:prose-xl max-w-none">
      <ReactMarkdown>{content}</ReactMarkdown>
    </div>
  );
}

export default ContentDisplay;