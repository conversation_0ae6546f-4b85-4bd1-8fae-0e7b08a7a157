{"timestamp":"2025-08-26 00:57:57","level":"error","message":"[GET] /sw.js >> StatusCode: 500, Message: ERR syntax error","meta":{"correlationId":"d4e15a6a-10d9-4f3d-8260-156d8c5efad9","status":500,"method":"GET","path":"/sw.js","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"}}}
{"timestamp":"2025-08-26 00:57:57","level":"error","message":"Error middleware failed","meta":{"originalError":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"},"internalError":{"name":"Error","message":"Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:700:11)\n    at ServerResponse.header (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:684:10)\n    at ServerResponse.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:161:12)\n    at ServerResponse.res.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/responseLogger.middleware.ts:55:25)\n    at ServerResponse.json (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:250:15)\n    at errorMiddleware (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/error.middleware.ts:73:24)\n    at Layer.handleError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/lib/layer.js:116:17)\n    at trimPrefix (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:340:13)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:582:12)"},"path":"/sw.js","method":"GET"}}
{"timestamp":"2025-08-26 01:01:35","level":"error","message":"[GET] /api/v1/google >> StatusCode: 500, Message: ERR syntax error","meta":{"correlationId":"e6bdf54e-df2f-4db3-ad76-e6bf8589cc41","status":500,"method":"GET","path":"/api/v1/google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"}}}
{"timestamp":"2025-08-26 01:01:35","level":"error","message":"Error middleware failed","meta":{"originalError":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"},"internalError":{"name":"Error","message":"Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:700:11)\n    at ServerResponse.header (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:684:10)\n    at ServerResponse.json (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:247:10)\n    at errorMiddleware (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/error.middleware.ts:73:24)\n    at Layer.handleError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/lib/layer.js:116:17)\n    at trimPrefix (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:340:13)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:291:5)\n    at Layer.handleError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/lib/layer.js:111:12)"},"path":"/api/v1/google","method":"GET"}}
{"timestamp":"2025-08-26 01:01:50","level":"error","message":"[GET] /sw.js >> StatusCode: 500, Message: ERR syntax error","meta":{"correlationId":"e91480f7-80fb-49b3-94b6-b883d54979fc","status":500,"method":"GET","path":"/sw.js","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"}}}
{"timestamp":"2025-08-26 01:01:50","level":"error","message":"Error middleware failed","meta":{"originalError":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"},"internalError":{"name":"Error","message":"Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:700:11)\n    at ServerResponse.header (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:684:10)\n    at ServerResponse.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:161:12)\n    at ServerResponse.res.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/responseLogger.middleware.ts:55:25)\n    at ServerResponse.json (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:250:15)\n    at errorMiddleware (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/error.middleware.ts:73:24)\n    at Layer.handleError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/lib/layer.js:116:17)\n    at trimPrefix (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:340:13)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:582:12)"},"path":"/sw.js","method":"GET"}}
{"timestamp":"2025-08-26 01:03:22","level":"error","message":"[GET] /api/v1/google/callback >> StatusCode: 500, Message: ERR syntax error","meta":{"correlationId":"0c6eddd9-02c8-4b6d-a2b4-526e77274e45","status":500,"method":"GET","path":"/api/v1/google/callback","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"}}}
{"timestamp":"2025-08-26 01:03:22","level":"error","message":"Error middleware failed","meta":{"originalError":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"},"internalError":{"name":"Error","message":"Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:700:11)\n    at ServerResponse.header (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:684:10)\n    at ServerResponse.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:161:12)\n    at ServerResponse.res.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/responseLogger.middleware.ts:55:25)\n    at ServerResponse.json (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:250:15)\n    at errorMiddleware (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/error.middleware.ts:73:24)\n    at Layer.handleError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/lib/layer.js:116:17)\n    at trimPrefix (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:340:13)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:582:12)"},"path":"/api/v1/google/callback","method":"GET"}}
