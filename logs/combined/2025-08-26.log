{"timestamp":"2025-08-26 00:46:35","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-26 00:46:36","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-26 00:46:41","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-26 00:46:41","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-26 00:46:41","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-26 00:46:41","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
