{"timestamp":"2025-08-26 00:46:35","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-26 00:46:36","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-26 00:46:40","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-26 00:46:41","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-26 00:46:41","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-26 00:46:41","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-26 00:46:41","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-26 00:47:05","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-26 00:47:05","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17561404..."}}
{"timestamp":"2025-08-26 00:47:05","level":"info","message":"GET /api/v1/auth/oauth/google 302 0 - 6.318 ms"}
{"timestamp":"2025-08-26 00:47:12","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-26 00:47:12","level":"info","message":"GET /sw.js 404 61 - 3.255 ms"}
{"timestamp":"2025-08-26 00:47:13","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":73,"sanitizedLength":78,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-26 00:47:13","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":116,"sanitizedLength":156,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-26 00:47:13","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":78,"sanitizedLength":82,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/google/callback"}}
{"timestamp":"2025-08-26 00:47:13","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":156,"sanitizedLength":188,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/google/callback"}}
{"timestamp":"2025-08-26 00:47:13","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"https://accounts.google.com/"}}
{"timestamp":"2025-08-26 00:47:13","level":"warn","message":"OAuth state validation failed","meta":{"provider":"google","hasReceivedState":true,"hasSessionState":false,"statesMatch":false,"ip":"::1"}}
{"timestamp":"2025-08-26 00:47:13","level":"info","message":"GET /api/v1/auth/oauth/google/callback?state=1756140425617_9glneju76uo_anon&code=4%2F0AVMBsJgsreM9U7DDJHH06pm91MDLwCU7F220HQhxmdo70RBdHUJKZ8QMUItZNwnoU8ReDg&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+openid&authuser=0&prompt=consent 302 100 - 9.201 ms"}
{"timestamp":"2025-08-26 00:56:38","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-26 00:57:46","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-26 00:57:46","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-26 00:57:48","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-26 00:57:57","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-26 00:57:57","level":"info","message":"GET /sw.js 404 61 - 6.623 ms"}
{"timestamp":"2025-08-26 00:57:57","level":"error","message":"[GET] /sw.js >> StatusCode: 500, Message: ERR syntax error","meta":{"correlationId":"d4e15a6a-10d9-4f3d-8260-156d8c5efad9","status":500,"method":"GET","path":"/sw.js","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"}}}
{"timestamp":"2025-08-26 00:57:57","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":500,"body":"{\"success\":false,\"message\":\"ERR syntax error\",\"timestamp\":\"2025-08-25T16:57:57.676Z\",\"path\":\"/sw.js\",\"correlationId\":\"d4e15a6a-10d9-4f3d-8260-156d8c5efad9\",\"error\":{\"code\":\"ReplyError\",\"details\":\"Internal server error\"}}"}}
{"timestamp":"2025-08-26 00:57:57","level":"error","message":"Error middleware failed","meta":{"originalError":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"},"internalError":{"name":"Error","message":"Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:700:11)\n    at ServerResponse.header (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:684:10)\n    at ServerResponse.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:161:12)\n    at ServerResponse.res.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/responseLogger.middleware.ts:55:25)\n    at ServerResponse.json (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:250:15)\n    at errorMiddleware (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/error.middleware.ts:73:24)\n    at Layer.handleError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/lib/layer.js:116:17)\n    at trimPrefix (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:340:13)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:582:12)"},"path":"/sw.js","method":"GET"}}
{"timestamp":"2025-08-26 00:57:57","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":500,"body":"{\"success\":false,\"message\":\"Internal server error\",\"timestamp\":\"2025-08-25T16:57:57.682Z\",\"path\":\"/sw.js\",\"correlationId\":\"af811edf-86ec-4490-9d60-3cb94791ab1a\"}"}}
{"timestamp":"2025-08-26 00:58:35","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-26 01:01:35","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17561412..."}}
{"timestamp":"2025-08-26 01:01:35","level":"info","message":"GET /api/v1/auth/oauth/google 302 0 - 180013.517 ms"}
{"timestamp":"2025-08-26 01:01:35","level":"error","message":"[GET] /api/v1/google >> StatusCode: 500, Message: ERR syntax error","meta":{"correlationId":"e6bdf54e-df2f-4db3-ad76-e6bf8589cc41","status":500,"method":"GET","path":"/api/v1/google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"}}}
{"timestamp":"2025-08-26 01:01:35","level":"error","message":"Error middleware failed","meta":{"originalError":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"},"internalError":{"name":"Error","message":"Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:700:11)\n    at ServerResponse.header (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:684:10)\n    at ServerResponse.json (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:247:10)\n    at errorMiddleware (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/error.middleware.ts:73:24)\n    at Layer.handleError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/lib/layer.js:116:17)\n    at trimPrefix (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:340:13)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:291:5)\n    at Layer.handleError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/lib/layer.js:111:12)"},"path":"/api/v1/google","method":"GET"}}
{"timestamp":"2025-08-26 01:01:50","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-26 01:01:50","level":"info","message":"GET /sw.js 404 61 - 1.108 ms"}
{"timestamp":"2025-08-26 01:01:50","level":"error","message":"[GET] /sw.js >> StatusCode: 500, Message: ERR syntax error","meta":{"correlationId":"e91480f7-80fb-49b3-94b6-b883d54979fc","status":500,"method":"GET","path":"/sw.js","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"}}}
{"timestamp":"2025-08-26 01:01:50","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":500,"body":"{\"success\":false,\"message\":\"ERR syntax error\",\"timestamp\":\"2025-08-25T17:01:50.245Z\",\"path\":\"/sw.js\",\"correlationId\":\"e91480f7-80fb-49b3-94b6-b883d54979fc\",\"error\":{\"code\":\"ReplyError\",\"details\":\"Internal server error\"}}"}}
{"timestamp":"2025-08-26 01:01:50","level":"error","message":"Error middleware failed","meta":{"originalError":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"},"internalError":{"name":"Error","message":"Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:700:11)\n    at ServerResponse.header (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:684:10)\n    at ServerResponse.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:161:12)\n    at ServerResponse.res.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/responseLogger.middleware.ts:55:25)\n    at ServerResponse.json (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:250:15)\n    at errorMiddleware (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/error.middleware.ts:73:24)\n    at Layer.handleError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/lib/layer.js:116:17)\n    at trimPrefix (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:340:13)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:582:12)"},"path":"/sw.js","method":"GET"}}
{"timestamp":"2025-08-26 01:01:50","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":500,"body":"{\"success\":false,\"message\":\"Internal server error\",\"timestamp\":\"2025-08-25T17:01:50.247Z\",\"path\":\"/sw.js\",\"correlationId\":\"70a5a8b0-8936-4f76-83f1-f31a398fb6c3\"}"}}
{"timestamp":"2025-08-26 01:01:52","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":73,"sanitizedLength":78,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-26 01:01:52","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":116,"sanitizedLength":156,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-26 01:01:52","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":78,"sanitizedLength":82,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/google/callback"}}
{"timestamp":"2025-08-26 01:01:52","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":156,"sanitizedLength":188,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/google/callback"}}
{"timestamp":"2025-08-26 01:01:52","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"https://accounts.google.com/"}}
{"timestamp":"2025-08-26 01:03:22","level":"warn","message":"OAuth state validation failed","meta":{"provider":"google","hasReceivedState":true,"hasSessionState":false,"statesMatch":false,"ip":"::1"}}
{"timestamp":"2025-08-26 01:03:22","level":"info","message":"GET /api/v1/auth/oauth/google/callback?state=1756141295922_usla2t05u5i_anon&code=4%2F0AVMBsJh8bUZBO3_fi2S1DSt1N_6TFsJGG9gj6Zut1oQOYRVtjRutcqDGK2YGkvpLD6XbnA&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+openid&authuser=0&prompt=consent 302 100 - 90040.162 ms"}
{"timestamp":"2025-08-26 01:03:22","level":"error","message":"[GET] /api/v1/google/callback >> StatusCode: 500, Message: ERR syntax error","meta":{"correlationId":"0c6eddd9-02c8-4b6d-a2b4-526e77274e45","status":500,"method":"GET","path":"/api/v1/google/callback","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","error":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"}}}
{"timestamp":"2025-08-26 01:03:22","level":"info","message":"Response sent","meta":{"method":"GET","path":"/api/v1/google/callback","statusCode":500,"body":"{\"success\":false,\"message\":\"ERR syntax error\",\"timestamp\":\"2025-08-25T17:03:22.255Z\",\"path\":\"/api/v1/auth/oauth/google/callback?state=1756141295922_usla2t05u5i_anon&code=4%2F0AVMBsJh8bUZBO3_fi2S1DSt1N_6TFsJGG9gj6Zut1oQOYRVtjRutcqDGK2YGkvpLD6XbnA&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+openid&authuser=0&prompt=consent\",\"correlationId\":\"0c6eddd9-02c8-4b6d-a2b4-526e77274e45\",\"error\":{\"code\":\"ReplyError\","}}
{"timestamp":"2025-08-26 01:03:22","level":"error","message":"Error middleware failed","meta":{"originalError":{"name":"ReplyError","message":"ERR syntax error","stack":"ReplyError: ERR syntax error\n    at parseError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:179:12)\n    at parseType (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/redis-parser/lib/parser.js:302:14)"},"internalError":{"name":"Error","message":"Cannot set headers after they are sent to the client","stack":"Error: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:700:11)\n    at ServerResponse.header (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:684:10)\n    at ServerResponse.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:161:12)\n    at ServerResponse.res.send (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/responseLogger.middleware.ts:55:25)\n    at ServerResponse.json (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/express/lib/response.js:250:15)\n    at errorMiddleware (/Users/<USER>/Downloads/QtMaster.io/secure_backend/src/middlewares/error.middleware.ts:73:24)\n    at Layer.handleError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/lib/layer.js:116:17)\n    at trimPrefix (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:340:13)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/router/index.js:582:12)"},"path":"/api/v1/google/callback","method":"GET"}}
{"timestamp":"2025-08-26 01:03:22","level":"info","message":"Response sent","meta":{"method":"GET","path":"/api/v1/google/callback","statusCode":500,"body":"{\"success\":false,\"message\":\"Internal server error\",\"timestamp\":\"2025-08-25T17:03:22.257Z\",\"path\":\"/api/v1/auth/oauth/google/callback?state=1756141295922_usla2t05u5i_anon&code=4%2F0AVMBsJh8bUZBO3_fi2S1DSt1N_6TFsJGG9gj6Zut1oQOYRVtjRutcqDGK2YGkvpLD6XbnA&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+openid&authuser=0&prompt=consent\",\"correlationId\":\"6f277876-4d3e-4097-9e65-0e2f6a1adeb7\"}"}}
{"timestamp":"2025-08-26 01:04:23","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
