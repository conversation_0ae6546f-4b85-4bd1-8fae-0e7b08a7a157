{"timestamp":"2025-08-23 18:55:27","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-23 18:55:28","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-23 18:55:32","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-23 18:55:32","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-23 18:55:32","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-23 18:55:32","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-23 18:55:32","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-23 18:55:32","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-23 18:55:32","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-23 18:55:32","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-23 18:55:33","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-23 18:55:33","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-23 18:55:33","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-23 18:55:33","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-23 18:55:57","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17559465..."}}
{"timestamp":"2025-08-23 18:55:57","level":"info","message":"GET /api/v1/auth/oauth/google 302 0 - 7.307 ms"}
{"timestamp":"2025-08-23 18:56:03","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":73,"sanitizedLength":78,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-23 18:56:03","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":116,"sanitizedLength":156,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-23 18:56:03","level":"error","message":"Passport authentication failed","meta":{"provider":"google","error":{"name":"TokenError","message":"Malformed auth code.","stack":"TokenError: Malformed auth code.\n    at OAuth2Strategy.parseErrorResponse (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:373:12)\n    at OAuth2Strategy._createOAuthError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:420:16)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:177:45\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:196:18\n    at passBackControl (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:132:9)\n    at IncomingMessage.<anonymous> (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:157:7)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"},"ip":"::1"}}
{"timestamp":"2025-08-23 18:56:03","level":"info","message":"GET /api/v1/auth/oauth/google/callback?state=1755946557708_4pu8ss5c8e_anon&code=4%2F0AVMBsJgZQQ8h5B3vv2G12LceWOEgqm9KCbkhJgBlxU1oftNIkj9c9-Xlr1sw62CQ77CFFA&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+openid+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile&authuser=0&prompt=none 302 97 - 335.136 ms"}
{"timestamp":"2025-08-23 19:07:56","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-23 19:07:56","level":"info","message":"GET /sw.js 404 61 - 12.830 ms"}
{"timestamp":"2025-08-23 19:08:02","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-23 19:08:07","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-23 19:08:08","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-23 19:08:09","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-23 19:08:32","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-23 19:08:35","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-23 19:08:35","level":"info","message":"GET /sw.js 404 61 - 2.307 ms"}
{"timestamp":"2025-08-23 19:08:47","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-23 19:08:51","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-23 19:08:51","level":"info","message":"GET /sw.js 404 61 - 1.635 ms"}
{"timestamp":"2025-08-23 19:09:13","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-23 19:09:15","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-23 19:09:15","level":"info","message":"GET /sw.js 404 61 - 1.141 ms"}
{"timestamp":"2025-08-23 19:09:19","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-23 19:09:36","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-23 19:09:36","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-23 19:09:38","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-23 19:10:23","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-23 19:10:26","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-23 19:10:26","level":"info","message":"GET /sw.js 404 61 - 3.839 ms"}
{"timestamp":"2025-08-23 19:11:59","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-23 19:12:04","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-23 19:12:04","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-23 19:12:06","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-23 19:12:35","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17559475..."}}
{"timestamp":"2025-08-23 19:12:35","level":"info","message":"GET /api/v1/auth/oauth/google 302 0 - 5.811 ms"}
{"timestamp":"2025-08-23 19:13:01","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":73,"sanitizedLength":78,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-23 19:13:01","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":116,"sanitizedLength":156,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-23 19:13:02","level":"error","message":"Passport authentication failed","meta":{"provider":"google","error":{"name":"TokenError","message":"Malformed auth code.","stack":"TokenError: Malformed auth code.\n    at OAuth2Strategy.parseErrorResponse (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:373:12)\n    at OAuth2Strategy._createOAuthError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:420:16)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:177:45\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:196:18\n    at passBackControl (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:132:9)\n    at IncomingMessage.<anonymous> (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:157:7)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"},"ip":"::1"}}
{"timestamp":"2025-08-23 19:13:02","level":"info","message":"GET /api/v1/auth/oauth/google/callback?state=1755947555326_86hcskn3he8_anon&code=4%2F0AVMBsJgrvWEdRh9IFEI8tpMvup8jUqk4yUKgfc1QAB_vm1DWVlXnRba_AedRICXhVy7LSw&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+openid&authuser=0&prompt=none 302 97 - 405.397 ms"}
{"timestamp":"2025-08-23 19:14:33","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-23 19:14:33","level":"info","message":"GET /sw.js 404 61 - 2.874 ms"}
{"timestamp":"2025-08-23 19:17:05","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-23 19:17:14","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-23 19:17:15","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-23 19:17:16","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-23 19:21:18","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17559480..."}}
{"timestamp":"2025-08-23 19:21:18","level":"info","message":"GET /api/v1/auth/oauth/google 302 0 - 180009.199 ms"}
{"timestamp":"2025-08-23 19:25:39","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":73,"sanitizedLength":78,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-23 19:25:39","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":116,"sanitizedLength":156,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-23 19:27:09","level":"error","message":"Passport authentication failed","meta":{"provider":"google","error":{"name":"TokenError","message":"Malformed auth code.","stack":"TokenError: Malformed auth code.\n    at OAuth2Strategy.parseErrorResponse (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:373:12)\n    at OAuth2Strategy._createOAuthError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:420:16)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:177:45\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:196:18\n    at passBackControl (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:132:9)\n    at IncomingMessage.<anonymous> (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:157:7)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"},"ip":"::1"}}
{"timestamp":"2025-08-23 19:27:09","level":"info","message":"GET /api/v1/auth/oauth/google/callback?state=1755948078861_7shr3rp40jf_anon&code=4%2F0AVMBsJgfXlRgVjNrVai5EhjlVtqjuXADDLUKoZQRAA6nunSh0bksOhO-RA3FoQ0Hnhtyaw&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+openid+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email&authuser=0&prompt=none 302 97 - 90283.340 ms"}
{"timestamp":"2025-08-23 19:28:31","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-23 19:37:09","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-23 19:37:09","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-23 19:37:11","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-23 19:37:23","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-23 19:37:23","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17559490..."}}
{"timestamp":"2025-08-23 19:37:23","level":"info","message":"GET /api/v1/auth/oauth/google 302 0 - 7.415 ms"}
{"timestamp":"2025-08-23 19:37:34","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":73,"sanitizedLength":78,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-23 19:37:34","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":116,"sanitizedLength":156,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-23 19:37:34","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":78,"sanitizedLength":82,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/google/callback"}}
{"timestamp":"2025-08-23 19:37:34","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":156,"sanitizedLength":188,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/google/callback"}}
{"timestamp":"2025-08-23 19:37:34","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"https://accounts.google.com/"}}
{"timestamp":"2025-08-23 19:37:34","level":"warn","message":"OAuth state validation failed","meta":{"provider":"google","hasReceivedState":true,"hasSessionState":false,"statesMatch":false,"ip":"::1"}}
{"timestamp":"2025-08-23 19:37:34","level":"info","message":"GET /api/v1/auth/oauth/google/callback?state=1755949043259_lefdf61yxo_anon&code=4%2F0AVMBsJjkqSUjjSuGSg4VORNifoDcLBZdtAz3wkHIbeAlALOlUBvvMCuIKYREr4gepKpwnw&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+openid&authuser=0&prompt=consent 302 100 - 7.758 ms"}
{"timestamp":"2025-08-23 19:45:32","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-23 19:45:37","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-23 19:45:37","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-23 19:45:39","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-23 19:46:22","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-23 19:49:22","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17559497..."}}
{"timestamp":"2025-08-23 19:49:22","level":"info","message":"GET /api/v1/auth/oauth/google 302 0 - 180008.590 ms"}
{"timestamp":"2025-08-23 19:49:33","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":73,"sanitizedLength":78,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-23 19:49:33","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":116,"sanitizedLength":156,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-23 19:49:33","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":78,"sanitizedLength":82,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/google/callback"}}
{"timestamp":"2025-08-23 19:49:33","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":156,"sanitizedLength":188,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/google/callback"}}
{"timestamp":"2025-08-23 19:49:33","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"https://accounts.google.com/"}}
{"timestamp":"2025-08-23 19:51:03","level":"warn","message":"OAuth state validation failed","meta":{"provider":"google","hasReceivedState":true,"hasSessionState":false,"statesMatch":false,"ip":"::1"}}
{"timestamp":"2025-08-23 19:51:03","level":"info","message":"GET /api/v1/auth/oauth/google/callback?state=1755949762877_vg5skrafa3_anon&code=4%2F0AVMBsJjBWu2FjLXEH-1I3sZDJnkFcOcXlVm06jizfAnmDjoRUwpjkLFBXwxO1hsUiHp9vg&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+openid&authuser=0&prompt=consent 302 100 - 90009.344 ms"}
{"timestamp":"2025-08-23 19:55:28","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
