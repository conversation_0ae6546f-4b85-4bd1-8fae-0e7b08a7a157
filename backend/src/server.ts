import dotenv from "dotenv";
import express from "express";
import cors from "cors";
import { GoogleGenAI } from "@google/genai";
import sqlite3 from "sqlite3";
import path from "path";
import { fileURLToPath } from "url";

import { DatabaseHelper } from "./utils/database.js";
import { getConfig, validateConfig } from "./utils/config.js";
import { createSubjectsRoutes } from "./routes/subjects.js";
import { createCurriculumRoutes } from "./routes/curriculum.js";
import { createCoursesRoutes } from "./routes/courses.js";
import { createUniversityCoursesRoutes } from "./routes/university-courses.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

// Load and validate configuration
const config = getConfig();
validateConfig(config);

const app = express();
const ai = new GoogleGenAI({});

// Database setup
const dbPath = path.join(__dirname, 'qtmaster.db');
const db = new sqlite3.Database(dbPath);
const dbHelper = new DatabaseHelper(db);

// Initialize database tables
db.serialize(() => {
  // Subjects table - stores user's learning subjects
  db.run(`CREATE TABLE IF NOT EXISTS subjects (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // Curricula table - stores the curriculum structure for each subject
  db.run(`CREATE TABLE IF NOT EXISTS curricula (
    id TEXT PRIMARY KEY,
    subject_id TEXT NOT NULL,
    structure TEXT NOT NULL, -- JSON string of curriculum structure
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE
  )`);

  // Courses table - stores generated course content
  db.run(`CREATE TABLE IF NOT EXISTS courses (
    id TEXT PRIMARY KEY,
    subject_id TEXT NOT NULL,
    topic_id TEXT NOT NULL,
    topic_title TEXT NOT NULL,
    content TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE
  )`);

  // User progress table - tracks user progress through curricula
  db.run(`CREATE TABLE IF NOT EXISTS user_progress (
    id TEXT PRIMARY KEY,
    subject_id TEXT NOT NULL,
    topic_id TEXT NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE
  )`);

  // University courses table - stores comprehensive multi-module courses
  db.run(`CREATE TABLE IF NOT EXISTS university_courses (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    duration TEXT NOT NULL,
    difficulty TEXT NOT NULL CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
    prerequisites TEXT, -- JSON string array
    learning_outcomes TEXT NOT NULL, -- JSON string array
    user_sketch TEXT NOT NULL, -- Original user input
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // University course modules table - stores individual modules within courses
  db.run(`CREATE TABLE IF NOT EXISTS university_course_modules (
    id TEXT PRIMARY KEY,
    course_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    duration TEXT NOT NULL,
    module_order INTEGER NOT NULL,
    prerequisites TEXT, -- JSON string array
    learning_objectives TEXT NOT NULL, -- JSON string array
    curriculum_structure TEXT NOT NULL, -- JSON string of curriculum structure
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES university_courses (id) ON DELETE CASCADE
  )`);
});

// Middleware
app.use(cors({
  origin: config.corsOrigin,
  credentials: true
}));
app.use(express.json());

// Routes
app.use('/api', createSubjectsRoutes(dbHelper));
app.use('/api', createCurriculumRoutes(dbHelper, ai));
app.use('/api', createCoursesRoutes(dbHelper, ai));
app.use('/api/university-courses', createUniversityCoursesRoutes(dbHelper, ai));

// Health check endpoint
app.get('/api/health', (_req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Start the server
app.listen(config.port, () => {
  console.log(`Server running on port ${config.port}`);
  console.log(`Environment: ${config.nodeEnv}`);
  console.log(`CORS origin: ${config.corsOrigin}`);
});
