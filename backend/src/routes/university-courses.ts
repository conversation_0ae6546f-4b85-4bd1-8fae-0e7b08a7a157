import { Router } from 'express';
import { GoogleGenAI } from '@google/genai';
import { v4 as uuidv4 } from 'uuid';
import {
  GenerateUniversityCourseRequest,
  GenerateUniversityCourseResponse,
  CreateUniversityCourseRequest,
  CreateUniversityCourseResponse,
  UniversityCoursesListResponse,
  UniversityCourseResponse,
  RouteHandler
} from '../types/index.js';
import { DatabaseHelper } from '../utils/database.js';
import { CourseAnalyzer } from '../utils/course-analyzer.js';
import { validateGenerateUniversityCourseRequest } from '../utils/validation.js';
import { sendErrorResponse, NotFoundError, LLMError } from '../utils/errors.js';

export function createUniversityCoursesRoutes(dbHelper: DatabaseHelper, ai: GoogleGenAI): Router {
  const router = Router();
  const courseAnalyzer = new CourseAnalyzer(ai);

  // Generate university course from user sketch
  const generateUniversityCourse: RouteHandler<GenerateUniversityCourseRequest, GenerateUniversityCourseResponse> = async (req, res) => {
    try {
      const { userSketch, language } = validateGenerateUniversityCourseRequest(req.body);
      const targetLanguage = language || 'English';
      
      console.log(`Analyzing course sketch (Language: ${targetLanguage})`);
      console.log(`Sketch length: ${userSketch.length} characters`);
      
      const courseAnalysis = await courseAnalyzer.analyzeCourseSketch(userSketch, targetLanguage);
      
      console.log(`Generated course: ${courseAnalysis.courseInfo.title}`);
      console.log(`Modules created: ${courseAnalysis.modules.length}`);
      
      const response: GenerateUniversityCourseResponse = {
        course: {
          course: courseAnalysis.courseInfo,
          modules: courseAnalysis.modules
        }
      };

      res.json(response);
    } catch (error) {
      console.error('Error generating university course:', error);
      if (error instanceof Error && error.message.includes('Failed to generate valid course structure')) {
        sendErrorResponse(res, new LLMError(error.message));
      } else {
        sendErrorResponse(res, error as Error);
      }
    }
  };

  // Create and save university course
  const createUniversityCourse: RouteHandler<CreateUniversityCourseRequest, CreateUniversityCourseResponse> = async (req, res) => {
    try {
      const {
        title,
        description,
        duration,
        difficulty,
        prerequisites = [],
        learningOutcomes,
        userSketch,
        modules
      } = req.body;

      // Validate required fields
      if (!title || !description || !duration || !difficulty || !learningOutcomes || !userSketch || !modules) {
        return sendErrorResponse(res, new Error('Missing required fields'));
      }

      if (!['beginner', 'intermediate', 'advanced'].includes(difficulty)) {
        return sendErrorResponse(res, new Error('Difficulty must be beginner, intermediate, or advanced'));
      }

      const courseId = uuidv4();
      const now = new Date().toISOString();

      // Insert course
      await dbHelper.run(
        `INSERT INTO university_courses (
          id, title, description, duration, difficulty, prerequisites, 
          learning_outcomes, user_sketch, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          courseId,
          title,
          description,
          duration,
          difficulty,
          JSON.stringify(prerequisites),
          JSON.stringify(learningOutcomes),
          userSketch,
          now,
          now
        ]
      );

      // Insert modules
      for (const module of modules) {
        const moduleId = uuidv4();
        await dbHelper.run(
          `INSERT INTO university_course_modules (
            id, course_id, title, description, duration, module_order,
            prerequisites, learning_objectives, curriculum_structure, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            moduleId,
            courseId,
            module.title,
            module.description,
            module.duration,
            module.order,
            JSON.stringify(module.prerequisites || []),
            JSON.stringify(module.learningObjectives),
            JSON.stringify(module.curriculum),
            now,
            now
          ]
        );
      }

      res.json({
        id: courseId,
        message: 'University course created successfully'
      });
    } catch (error) {
      console.error('Error creating university course:', error);
      sendErrorResponse(res, error as Error);
    }
  };

  // Get all university courses
  const getUniversityCourses: RouteHandler<{}, UniversityCoursesListResponse> = async (_req, res) => {
    try {
      const courses = await dbHelper.all(`
        SELECT 
          c.*,
          m.id as module_id,
          m.title as module_title,
          m.description as module_description,
          m.duration as module_duration,
          m.module_order,
          m.prerequisites as module_prerequisites,
          m.learning_objectives as module_learning_objectives,
          m.curriculum_structure
        FROM university_courses c
        LEFT JOIN university_course_modules m ON c.id = m.course_id
        ORDER BY c.created_at DESC, m.module_order ASC
      `);

      // Group modules by course
      const coursesMap = new Map();
      
      for (const row of courses) {
        const courseId = row['id'] as string;
        
        if (!coursesMap.has(courseId)) {
          coursesMap.set(courseId, {
            id: courseId,
            title: row['title'] as string,
            description: row['description'] as string,
            duration: row['duration'] as string,
            difficulty: row['difficulty'] as string,
            prerequisites: row['prerequisites'] ? JSON.parse(row['prerequisites'] as string) : null,
            learning_outcomes: JSON.parse(row['learning_outcomes'] as string),
            user_sketch: row['user_sketch'] as string,
            created_at: row['created_at'] as string,
            updated_at: row['updated_at'] as string,
            modules: []
          });
        }

        if (row['module_id']) {
          coursesMap.get(courseId).modules.push({
            id: row['module_id'] as string,
            course_id: courseId,
            title: row['module_title'] as string,
            description: row['module_description'] as string,
            duration: row['module_duration'] as string,
            order: row['module_order'] as number,
            prerequisites: row['module_prerequisites'] ? JSON.parse(row['module_prerequisites'] as string) : null,
            learning_objectives: JSON.parse(row['module_learning_objectives'] as string),
            curriculum_structure: row['curriculum_structure'] as string,
            created_at: row['created_at'] as string,
            updated_at: row['updated_at'] as string
          });
        }
      }

      const result = Array.from(coursesMap.values());
      res.json(result);
    } catch (error) {
      console.error('Error fetching university courses:', error);
      sendErrorResponse(res, error as Error);
    }
  };

  // Get specific university course
  const getUniversityCourse: RouteHandler<{}, UniversityCourseResponse> = async (req, res) => {
    try {
      const { id } = req.params;
      
      if (!id) {
        return sendErrorResponse(res, new Error('Course ID is required'));
      }

      const courseData = await dbHelper.all(`
        SELECT 
          c.*,
          m.id as module_id,
          m.title as module_title,
          m.description as module_description,
          m.duration as module_duration,
          m.module_order,
          m.prerequisites as module_prerequisites,
          m.learning_objectives as module_learning_objectives,
          m.curriculum_structure
        FROM university_courses c
        LEFT JOIN university_course_modules m ON c.id = m.course_id
        WHERE c.id = ?
        ORDER BY m.module_order ASC
      `, [id]);

      if (courseData.length === 0) {
        throw new NotFoundError('University course not found');
      }

      const course = courseData[0];
      if (!course) {
        throw new NotFoundError('University course not found');
      }

      const result: UniversityCourseResponse = {
        id: course['id'] as string,
        title: course['title'] as string,
        description: course['description'] as string,
        duration: course['duration'] as string,
        difficulty: course['difficulty'] as string,
        prerequisites: course['prerequisites'] ? JSON.parse(course['prerequisites'] as string) : null,
        learning_outcomes: JSON.parse(course['learning_outcomes'] as string),
        user_sketch: course['user_sketch'] as string,
        created_at: course['created_at'] as string,
        updated_at: course['updated_at'] as string,
        modules: courseData
          .filter(row => row['module_id'])
          .map(row => ({
            id: row['module_id'] as string,
            course_id: row['id'] as string,
            title: row['module_title'] as string,
            description: row['module_description'] as string,
            duration: row['module_duration'] as string,
            order: row['module_order'] as number,
            prerequisites: row['module_prerequisites'] ? JSON.parse(row['module_prerequisites'] as string) : null,
            learning_objectives: JSON.parse(row['module_learning_objectives'] as string),
            curriculum_structure: row['curriculum_structure'] as string,
            created_at: row['created_at'] as string,
            updated_at: row['updated_at'] as string,
            curriculum: JSON.parse(row['curriculum_structure'] as string)
          }))
      };

      res.json(result);
    } catch (error) {
      console.error('Error fetching university course:', error);
      if (error instanceof NotFoundError) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, error as Error);
      }
    }
  };

  // Routes
  router.post('/generate', generateUniversityCourse);
  router.post('/', createUniversityCourse);
  router.get('/', getUniversityCourses);
  router.get('/:id', getUniversityCourse);

  return router;
}
