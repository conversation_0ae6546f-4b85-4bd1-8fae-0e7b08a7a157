import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import {
  CreateSubjectRequest,
  CreateSubjectResponse,
  UpdateSubjectRequest,
  SubjectsListResponse,
  SubjectResponse,
  RouteHandler
} from '../types/index.js';
import { DatabaseHelper } from '../utils/database.js';
import { 
  validateCreateSubjectRequest, 
  validateUpdateSubjectRequest,
  validateIdParam 
} from '../utils/validation.js';
import { sendErrorResponse, NotFoundError } from '../utils/errors.js';

export function createSubjectsRoutes(dbHelper: DatabaseHelper): Router {
  const router = Router();

  // Get all subjects
  const getAllSubjects: RouteHandler<void, SubjectsListResponse> = async (_req, res) => {
    try {
      const subjects = await dbHelper.all(`
        SELECT s.*,
               CASE WHEN c.id IS NOT NULL THEN 1 ELSE 0 END as has_curriculum
        FROM subjects s
        LEFT JOIN curricula c ON s.id = c.subject_id
        ORDER BY s.updated_at DESC
      `);

      const formattedSubjects = subjects.map(subject => ({
        id: subject['id'] as string,
        title: subject['title'] as string,
        description: subject['description'] as string | null,
        created_at: subject['created_at'] as string,
        updated_at: subject['updated_at'] as string,
        has_curriculum: Boolean(subject['has_curriculum'])
      }));

      res.json(formattedSubjects);
    } catch (error) {
      if (error instanceof Error) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Get subject by ID
  const getSubjectById: RouteHandler<void, SubjectResponse> = async (req, res) => {
    try {
      const id = validateIdParam(req.params['id'] as string);

      const subject = await dbHelper.get(`
        SELECT s.*,
               CASE WHEN c.id IS NOT NULL THEN 1 ELSE 0 END as has_curriculum
        FROM subjects s
        LEFT JOIN curricula c ON s.id = c.subject_id
        WHERE s.id = ?
      `, [id]);

      if (!subject) {
        throw new NotFoundError("Subject not found");
      }

      const formattedSubject: SubjectResponse = {
        id: subject['id'] as string,
        title: subject['title'] as string,
        description: subject['description'] as string | null,
        created_at: subject['created_at'] as string,
        updated_at: subject['updated_at'] as string,
        has_curriculum: Boolean(subject['has_curriculum'])
      };

      res.json(formattedSubject);
    } catch (error) {
      if (error instanceof Error) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Create new subject
  const createSubject: RouteHandler<CreateSubjectRequest, CreateSubjectResponse> = async (req, res) => {
    try {
      const { title, description } = validateCreateSubjectRequest(req.body);

      const subjectId = uuidv4();
      await dbHelper.run(`
        INSERT INTO subjects (id, title, description)
        VALUES (?, ?, ?)
      `, [subjectId, title, description || ""]);

      res.json({
        id: subjectId,
        title,
        description: description || "",
        message: "Subject created successfully"
      });
    } catch (error) {
      if (error instanceof Error) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Update subject
  const updateSubject: RouteHandler<UpdateSubjectRequest, { message: string }> = async (req, res) => {
    try {
      const id = validateIdParam(req.params['id'] as string);
      const { title, description } = validateUpdateSubjectRequest(req.body);

      const result = await dbHelper.run(`
        UPDATE subjects
        SET title = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [title, description || "", id]);

      if (result.changes === 0) {
        throw new NotFoundError("Subject not found");
      }

      res.json({ message: "Subject updated successfully" });
    } catch (error) {
      if (error instanceof Error) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Delete subject
  const deleteSubject: RouteHandler<void, { message: string }> = async (req, res) => {
    try {
      const id = validateIdParam(req.params['id'] as string);

      const result = await dbHelper.run("DELETE FROM subjects WHERE id = ?", [id]);

      if (result.changes === 0) {
        throw new NotFoundError("Subject not found");
      }

      res.json({ message: "Subject deleted successfully" });
    } catch (error) {
      if (error instanceof Error) {
        sendErrorResponse(res, error);
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Register routes
  router.get('/subjects', getAllSubjects);
  router.get('/subjects/:id', getSubjectById);
  router.post('/subjects', createSubject);
  router.put('/subjects/:id', updateSubject);
  router.delete('/subjects/:id', deleteSubject);

  return router;
}
