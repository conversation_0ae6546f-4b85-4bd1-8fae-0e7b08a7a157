import { Router } from 'express';
import { GoogleGenAI } from '@google/genai';
import { v4 as uuidv4 } from 'uuid';
import {
  GenerateCourseRequest,
  GenerateCourseResponse,
  RouteHandler
} from '../types/index.js';
import { DatabaseHelper } from '../utils/database.js';
import { validateGenerateCourseRequest } from '../utils/validation.js';
import { sendErrorResponse, handleLLMError } from '../utils/errors.js';
import { PersonaSelector } from '../utils/persona-selector.js';

export function createCoursesRoutes(dbHelper: DatabaseHelper, ai: GoogleGenAI): Router {
  const router = Router();
  const personaSelector = new PersonaSelector(ai);

  // Generate course content
  const generateCourse: RouteHandler<GenerateCourseRequest, GenerateCourseResponse> = async (req, res) => {
    try {
      const { topic, subjectId, topicId, language } = validateGenerateCourseRequest(req.body);
      const targetLanguage = language || 'English';

      // Get the subject title to help with persona selection
      let subjectTitle = 'Unknown Subject';
      if (subjectId) {
        try {
          const subject = await dbHelper.get('SELECT title FROM subjects WHERE id = ?', [subjectId]);
          if (subject) {
            subjectTitle = subject['title'] as string;
          }
        } catch (dbError) {
          console.error("Error fetching subject title:", dbError);
          // Continue with default subject title
        }
      }

      // Stage 1: Select the appropriate persona for this subject/topic combination
      const topicContext = `${subjectTitle}: ${topic}`;
      console.log(`Selecting persona for topic context: ${topicContext} (Language: ${targetLanguage})`);
      const personaSelection = await personaSelector.selectPersona(topicContext, targetLanguage);
      console.log(`Selected domain: ${personaSelection.domain}`);
      console.log(`Reasoning: ${personaSelection.reasoning}`);

      // Stage 2: Generate content using the specialized persona
      const contentPrompt = personaSelection.contentPrompt
        .replace('${topic}', topic)
        .replace('${subject}', subjectTitle);

      // Add uniqueness requirements to the specialized prompt
      const fullContentPrompt = `${contentPrompt}

**CRITICAL INSTRUCTION FOR UNIQUENESS:** This is a mastery-based learning application. The user will request lessons on the same topic repeatedly. You **MUST NOT** repeat content. For every request, you must generate a **completely new and distinct module**. To achieve this, you must:
- Use novel analogies and different explanatory approaches.
- Create entirely new, practical examples that illustrate the concept from a different perspective.
- Design a unique, practical task or exercise that is different from any you have created before.

**Output Format:**
You **MUST** format your entire response in Markdown. The structure must be as follows:
##  Module: ${topic}
### 💡 The "Why": Introduction & Relevance
(Provide a fresh, engaging introduction explaining why this topic is essential.)
### ⚙️ The "What": Core Concepts Explained
(Break down the core principles of the topic using new analogies and explanations. Do not repeat previous explanations.)
### 💻 The "How": Practical Example
(Provide a complete, well-commented, and practical example that is completely different from any previous example for this topic.)
### ✅ Your New Task: Hands-On Practice
(Design a new, specific, and practical task that requires the user to apply what they've learned in this module. The task must be unique.)`;

      const llmResponse = await ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: fullContentPrompt,
        config: {
          thinkingConfig: {
            thinkingBudget: 0, // Disables thinking
          },
          systemInstruction: personaSelection.systemPrompt,
        },
      });

      // Extract the content from the LLM response
      const content = llmResponse.text || '';

      // Save the generated course content if subjectId and topicId are provided
      if (subjectId && topicId) {
        try {
          const courseId = uuidv4();
          await dbHelper.run(`
            INSERT INTO courses (id, subject_id, topic_id, topic_title, content)
            VALUES (?, ?, ?, ?, ?)
          `, [courseId, subjectId, topicId, topic, content]);
        } catch (dbError) {
          console.error("Error saving course content:", dbError);
          // Continue without failing the request
        }
      }

      // Send the content back to the frontend
      res.json({ content });
    } catch (error) {
      if (error instanceof Error) {
        // Handle LLM-specific errors
        if (error.name === 'LLMApiError') {
          const llmError = handleLLMError(error as any);
          sendErrorResponse(res, llmError);
        } else {
          sendErrorResponse(res, error);
        }
      } else {
        sendErrorResponse(res, new Error('Unknown error occurred'));
      }
    }
  };

  // Register routes
  router.post('/generate', generateCourse);

  return router;
}
