/**
 * Database model interfaces
 */

export interface Subject {
  id: string;
  title: string;
  description: string | null;
  created_at: string;
  updated_at: string;
}

export interface SubjectWithCurriculum extends Subject {
  has_curriculum: boolean;
}

export interface Curriculum {
  id: string;
  subject_id: string;
  structure: string; // JSON string
  created_at: string;
  updated_at: string;
}

export interface CurriculumWithSubject extends Curriculum {
  subject_title: string;
  subject_description: string | null;
}

export interface Course {
  id: string;
  subject_id: string;
  topic_id: string;
  topic_title: string;
  content: string;
  created_at: string;
}

export interface UserProgress {
  id: string;
  subject_id: string;
  topic_id: string;
  completed: boolean;
  last_accessed: string;
}

/**
 * Curriculum structure types
 */
export interface Topic {
  id: string;
  title: string;
  description?: string;
}

export interface Category {
  id: string;
  title: string;
  description?: string;
  topics: Topic[];
}

export interface CurriculumStructure {
  curriculum: Category[];
}

// Aliases for consistency
export interface CurriculumCategory extends Category {}
export interface CurriculumTopic extends Topic {}

/**
 * University-Style Course Structure Types
 */
export interface UniversityCourseStructure {
  course: UniversityCourseInfo;
  modules: UniversityCourseModule[];
}

export interface UniversityCourseInfo {
  id: string;
  title: string;
  description: string;
  duration: string; // e.g., "12 weeks", "3 months"
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites?: string[];
  learningOutcomes: string[];
}

export interface UniversityCourseModule {
  id: string;
  title: string;
  description: string;
  duration: string; // e.g., "2 weeks"
  order: number;
  curriculum: Category[];
  prerequisites?: string[];
  learningObjectives: string[];
}

// Database entities for university courses
export interface UniversityCourse {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: string;
  prerequisites: string | null; // JSON string
  learning_outcomes: string; // JSON string
  user_sketch: string; // Original user input
  created_at: string;
  updated_at: string;
}

export interface UniversityCourseModuleDB {
  id: string;
  course_id: string;
  title: string;
  description: string;
  duration: string;
  order: number;
  prerequisites: string | null; // JSON string
  learning_objectives: string; // JSON string
  curriculum_structure: string; // JSON string of Category[]
  created_at: string;
  updated_at: string;
}

export interface UniversityCourseWithModules extends UniversityCourse {
  modules: UniversityCourseModuleDB[];
}

/**
 * Database operation result types
 */
export interface DatabaseResult {
  id?: number;
  changes: number;
}

/**
 * Database helper function types
 */
export type DatabaseRow = Record<string, any>;
export type DatabaseParams = (string | number | boolean | null)[];
