/**
 * Environment variable types
 */
export interface EnvironmentVariables {
  PORT?: string;
  NODE_ENV?: 'development' | 'production' | 'test';
  DATABASE_URL?: string;
  GOOGLE_API_KEY?: string;
  CORS_ORIGIN?: string;
}

/**
 * Parsed environment configuration
 */
export interface Config {
  port: number;
  nodeEnv: string;
  databaseUrl: string | undefined;
  googleApiKey: string | undefined;
  corsOrigin: string;
}

/**
 * Extend NodeJS ProcessEnv interface
 */
declare global {
  namespace NodeJS {
    interface ProcessEnv extends EnvironmentVariables {}
  }
}
