import { Request, Response } from 'express';
import {
  SubjectWithCurriculum,
  CurriculumStructure,
  CurriculumWithSubject,
  UniversityCourseStructure,
  UniversityCourseWithModules,
  UniversityCourseModuleDB
} from './database.js';

/**
 * API Request body types
 */
export interface GenerateCurriculumRequest {
  subject: string;
  language?: string;
}

export interface GenerateCourseRequest {
  topic: string;
  subjectId?: string;
  topicId?: string;
  apiKey?: string;
  language?: string;
}

export interface CreateSubjectRequest {
  title: string;
  description?: string;
}

export interface UpdateSubjectRequest {
  title: string;
  description?: string;
}

export interface CreateCurriculumRequest {
  subjectId: string;
  structure: CurriculumStructure;
}

export interface GenerateUniversityCourseRequest {
  userSketch: string;
  language?: string;
}

export interface CreateUniversityCourseRequest {
  title: string;
  description: string;
  duration: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites?: string[];
  learningOutcomes: string[];
  userSketch: string;
  modules: CreateUniversityCourseModuleRequest[];
}

export interface CreateUniversityCourseModuleRequest {
  title: string;
  description: string;
  duration: string;
  order: number;
  prerequisites?: string[];
  learningObjectives: string[];
  curriculum: CurriculumStructure;
}

/**
 * API Response types
 */
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
}

export interface GenerateCurriculumResponse extends CurriculumStructure {}

export interface GenerateCourseResponse {
  content: string;
}

export interface CreateSubjectResponse {
  id: string;
  title: string;
  description: string;
  message: string;
}

export interface SubjectsListResponse extends Array<SubjectWithCurriculum> {}

export interface SubjectResponse extends SubjectWithCurriculum {}

export interface CurriculaListResponse extends Array<CurriculumWithSubject> {}

export interface CurriculumResponse extends Omit<CurriculumWithSubject, 'structure'> {
  structure: CurriculumStructure;
}

export interface CreateCurriculumResponse {
  id: string;
  subjectId: string;
  message: string;
}

export interface GenerateUniversityCourseResponse {
  course: UniversityCourseStructure;
}

export interface CreateUniversityCourseResponse {
  id: string;
  message: string;
}

export interface UniversityCoursesListResponse extends Array<UniversityCourseWithModules> {}

export interface UniversityCourseResponse extends UniversityCourseWithModules {
  modules: Array<UniversityCourseModuleDB & { curriculum: CurriculumStructure }>;
}

/**
 * Error response types
 */
export interface ErrorResponse {
  error: string;
}

/**
 * Express request/response types with proper typing
 */
export interface TypedRequest<T = any> extends Request {
  body: T;
}

export interface TypedResponse<T = any> extends Response {
  json: (body: T) => this;
}

/**
 * Route handler types
 */
export type RouteHandler<TRequest = any, TResponse = any> = (
  req: TypedRequest<TRequest>,
  res: TypedResponse<TResponse>
) => Promise<void> | void;

/**
 * LLM API Error types
 */
export interface LLMApiError extends Error {
  response?: {
    status: number;
    data: {
      error?: {
        message?: string;
      };
    };
  };
  request?: any;
}
