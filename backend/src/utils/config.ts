import { Config } from '../types/index.js';

/**
 * Parse and validate environment variables
 */
export function getConfig(): Config {
  const port = parseInt(process.env['PORT'] || '3001', 10);
  const nodeEnv = process.env['NODE_ENV'] || 'development';
  const corsOrigin = process.env['CORS_ORIGIN'] || 'http://localhost:5173';
  
  if (isNaN(port)) {
    throw new Error('PORT must be a valid number');
  }

  return {
    port,
    nodeEnv,
    databaseUrl: process.env['DATABASE_URL'],
    googleApiKey: process.env['GOOGLE_API_KEY'],
    corsOrigin,
  };
}

/**
 * Validate required environment variables
 */
export function validateConfig(config: Config): void {
  const requiredVars: (keyof Config)[] = ['port'];
  
  for (const varName of requiredVars) {
    if (config[varName] === undefined || config[varName] === null) {
      throw new Error(`Required environment variable ${varName} is not set`);
    }
  }
}
