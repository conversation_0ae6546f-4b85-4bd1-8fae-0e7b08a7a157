import sqlite3 from 'sqlite3';
import { DatabaseResult, DatabaseRow, DatabaseParams } from '../types/index.js';

/**
 * Typed database helper functions
 */
export class DatabaseHelper {
  private db: sqlite3.Database;

  constructor(db: sqlite3.Database) {
    this.db = db;
  }

  /**
   * Execute a SELECT query that returns a single row
   */
  get<T extends DatabaseRow = DatabaseRow>(
    sql: string, 
    params: DatabaseParams = []
  ): Promise<T | undefined> {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err: Error | null, row: T) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  /**
   * Execute a SELECT query that returns multiple rows
   */
  all<T extends DatabaseRow = DatabaseRow>(
    sql: string, 
    params: DatabaseParams = []
  ): Promise<T[]> {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err: Error | null, rows: T[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Execute an INSERT, UPDATE, or DELETE query
   */
  run(
    sql: string, 
    params: DatabaseParams = []
  ): Promise<DatabaseResult> {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(this: sqlite3.RunResult, err: Error | null) {
        if (err) {
          reject(err);
        } else {
          resolve({ 
            id: this.lastID, 
            changes: this.changes 
          });
        }
      });
    });
  }

  /**
   * Execute multiple statements in a transaction
   */
  async transaction<T>(callback: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.db.serialize(async () => {
        try {
          await this.run('BEGIN TRANSACTION');
          const result = await callback();
          await this.run('COMMIT');
          resolve(result);
        } catch (error) {
          await this.run('ROLLBACK');
          reject(error);
        }
      });
    });
  }

  /**
   * Close the database connection
   */
  close(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db.close((err: Error | null) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }
}
