import { GoogleGenAI } from '@google/genai';
import { PersonaSelector } from './persona-selector.js';
import { UniversityCourseInfo, UniversityCourseModule } from '../types/database.js';

export interface CourseAnalysisResult {
  courseInfo: UniversityCourseInfo;
  modules: UniversityCourseModule[];
  reasoning: string;
}

export class CourseAnalyzer {
  private ai: GoogleGenAI;
  private personaSelector: PersonaSelector;

  constructor(ai: GoogleGenAI) {
    this.ai = ai;
    this.personaSelector = new PersonaSelector(ai);
  }

  async analyzeCourseSketch(userSketch: string, language: string = 'English'): Promise<CourseAnalysisResult> {
    // Stage 1: Analyze the user sketch and extract learning domains
    const analysisPrompt = `**Your Role:** You are an expert educational architect and curriculum designer with extensive experience in creating comprehensive university-level courses.

**Your Task:** Analyze the following user learning sketch and design a comprehensive university-style course structure with multiple specialized modules.

**User Learning Sketch:**
${userSketch}

**Your Analysis Should:**
1. Identify the main learning goal and overall course theme
2. Break down the content into distinct learning domains/modules (3-8 modules)
3. Determine appropriate course duration, difficulty level, and prerequisites
4. Define clear learning outcomes for the entire course
5. For each module, specify learning objectives, duration, and prerequisites
6. Ensure logical progression between modules

**Output Requirements:**
- Course should be structured like a university course with clear academic rigor
- Each module should focus on a specific domain or skill set
- Modules should build upon each other logically
- Include realistic time estimates for completion
- Consider prerequisite knowledge and progressive difficulty
- ALL content must be in ${language} language

**Output Format:**
You **MUST** respond with a valid JSON object in this exact structure:
{
  "courseInfo": {
    "id": "course-id",
    "title": "Course Title",
    "description": "Comprehensive course description",
    "duration": "Duration estimate (e.g., '12 weeks', '3 months')",
    "difficulty": "beginner|intermediate|advanced",
    "prerequisites": ["prerequisite1", "prerequisite2"],
    "learningOutcomes": ["outcome1", "outcome2", "outcome3"]
  },
  "modules": [
    {
      "id": "module-1",
      "title": "Module Title",
      "description": "Module description",
      "duration": "Module duration (e.g., '2 weeks')",
      "order": 1,
      "prerequisites": ["prerequisite1"],
      "learningObjectives": ["objective1", "objective2"],
      "curriculum": []
    }
  ],
  "reasoning": "Explanation of the course structure and module organization"
}

**Important:**
- Use kebab-case for all IDs
- Ensure modules are ordered logically
- Keep descriptions comprehensive but concise
- Focus on practical, applicable skills
- Consider industry standards and best practices`;

    const llmResponse = await this.ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: analysisPrompt,
      config: {
        thinkingConfig: {
          thinkingBudget: 0,
        },
      },
    });

    // Parse the course structure
    let responseText = (llmResponse.text || '').trim();
    
    // Remove markdown code blocks if present
    if (responseText.startsWith('```json')) {
      responseText = responseText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
    } else if (responseText.startsWith('```')) {
      responseText = responseText.replace(/^```\s*/, '').replace(/\s*```$/, '');
    }

    try {
      const courseAnalysis: CourseAnalysisResult = JSON.parse(responseText);
      
      // Stage 2: Generate detailed curriculum for each module
      console.log(`Generating detailed curricula for ${courseAnalysis.modules.length} modules...`);
      
      for (let i = 0; i < courseAnalysis.modules.length; i++) {
        const module = courseAnalysis.modules[i];
        if (!module) continue;

        console.log(`Generating curriculum for module: ${module.title}`);

        // Select appropriate persona for this module's domain
        const moduleContext = `${courseAnalysis.courseInfo.title} - ${module.title}: ${module.description}`;
        const personaSelection = await this.personaSelector.selectPersona(moduleContext, language);

        // Generate detailed curriculum for this module
        const curriculumPrompt = `${personaSelection.curriculumPrompt.replace('${subject}', module.title)}

**Module Context:**
- Course: ${courseAnalysis.courseInfo.title}
- Module: ${module.title}
- Description: ${module.description}
- Duration: ${module.duration}
- Learning Objectives: ${module.learningObjectives.join(', ')}

**Output Format:**
You **MUST** respond with a valid JSON object containing the curriculum array:
{
  "curriculum": [
    {
      "id": "category-1",
      "title": "Category Title",
      "description": "Brief description",
      "topics": [
        {
          "id": "topic-1",
          "title": "Topic Title",
          "description": "Brief description"
        }
      ]
    }
  ]
}`;

        const curriculumResponse = await this.ai.models.generateContent({
          model: "gemini-2.5-flash",
          contents: curriculumPrompt,
          config: {
            thinkingConfig: {
              thinkingBudget: 0,
            },
            systemInstruction: personaSelection.systemPrompt,
          },
        });

        // Parse and assign curriculum to module
        let curriculumText = (curriculumResponse.text || '').trim();
        if (curriculumText.startsWith('```json')) {
          curriculumText = curriculumText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (curriculumText.startsWith('```')) {
          curriculumText = curriculumText.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        try {
          const curriculumData = JSON.parse(curriculumText);
          const currentModule = courseAnalysis.modules[i];
          if (currentModule) {
            currentModule.curriculum = curriculumData.curriculum || [];
          }
        } catch (curriculumError) {
          console.error(`Failed to parse curriculum for module ${module.title}:`, curriculumError);
          // Provide fallback empty curriculum
          const currentModule = courseAnalysis.modules[i];
          if (currentModule) {
            currentModule.curriculum = [];
          }
        }
      }

      return courseAnalysis;
    } catch (parseError) {
      console.error("Failed to parse course analysis JSON:", parseError);
      console.error("Raw response:", responseText);
      throw new Error("Failed to generate valid course structure. Please try again.");
    }
  }
}
