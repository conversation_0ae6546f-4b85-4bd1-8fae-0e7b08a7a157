import { Response } from 'express';
import { ErrorResponse, LLMApiError } from '../types/index.js';
import { ValidationError } from './validation.js';

/**
 * Custom error classes
 */
export class NotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class DatabaseError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'DatabaseError';
  }
}

export class LLMError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'LLMError';
  }
}

/**
 * Error response helper
 */
export function sendErrorResponse(res: Response, error: Error): void {
  console.error('Error:', error);

  if (error instanceof ValidationError) {
    res.status(400).json({ error: error.message } as ErrorResponse);
    return;
  }

  if (error instanceof NotFoundError) {
    res.status(404).json({ error: error.message } as ErrorResponse);
    return;
  }

  if (error instanceof LLMError) {
    const statusCode = error.statusCode || 500;
    res.status(statusCode).json({ error: error.message } as ErrorResponse);
    return;
  }

  if (error instanceof DatabaseError) {
    res.status(500).json({ error: 'Database operation failed' } as ErrorResponse);
    return;
  }

  // Generic error
  res.status(500).json({ error: 'Internal server error' } as ErrorResponse);
}

/**
 * Handle LLM API errors
 */
export function handleLLMError(error: LLMApiError): LLMError {
  if (error.response) {
    const status = error.response.status;
    const message = error.response.data?.error?.message || 'LLM API error';
    
    if (status === 401) {
      return new LLMError('Invalid API key', 401);
    }
    
    if (status === 429) {
      return new LLMError('Rate limit exceeded. Please try again later.', 429);
    }
    
    if (status >= 400 && status < 500) {
      return new LLMError(`Client error: ${message}`, status);
    }
    
    return new LLMError(`Server error: ${message}`, status);
  }
  
  if (error.request) {
    return new LLMError('Network error: Unable to reach LLM API');
  }
  
  return new LLMError('Unexpected error occurred');
}

/**
 * Async error handler wrapper
 */
export function asyncHandler<T extends any[], R>(
  fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      throw error;
    }
  };
}
