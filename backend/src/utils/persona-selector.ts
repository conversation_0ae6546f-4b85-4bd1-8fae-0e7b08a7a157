import { GoogleGenAI } from '@google/genai';

export interface PersonaSelection {
  domain: string;
  systemPrompt: string;
  curriculumPrompt: string;
  contentPrompt: string;
  reasoning: string;
}

export class PersonaSelector {
  private ai: GoogleGenAI;

  constructor(ai: GoogleGenAI) {
    this.ai = ai;
  }

  async selectPersona(topic: string, language: string = 'English'): Promise<PersonaSelection> {
    const metaPrompt = `**Your Role:** You are an expert educational consultant and AI prompt engineer. Your task is to analyze a learning topic and determine the most effective specialized persona and prompts for creating high-quality educational content.

**Your Task:** Analyze the topic: **"${topic}"** and determine:
1. The most appropriate domain/field of expertise
2. The ideal specialized persona (expert role) for this topic
3. Specialized system prompt for that persona
4. Specialized curriculum generation prompt
5. Specialized content generation prompt

**Guidelines for Persona Selection:**
- Consider the specific domain expertise needed (e.g., programming, science, arts, business, etc.)
- Think about what kind of expert would be most qualified to teach this topic
- Consider the teaching style that would be most effective for this domain
- Ensure the persona has deep practical experience, not just theoretical knowledge

**Output Format:**
You **MUST** respond with a valid JSON object in this exact structure:
{
  "domain": "The specific domain/field (e.g., 'Software Development', 'Data Science', 'Digital Marketing', etc.)",
  "systemPrompt": "A detailed system prompt that establishes the AI as a specialized expert in this domain with specific qualifications, teaching philosophy, and approach",
  "curriculumPrompt": "A specialized prompt template for generating curricula in this domain, including domain-specific requirements and structure",
  "contentPrompt": "A specialized prompt template for generating learning content in this domain, including domain-specific teaching methods and examples",
  "reasoning": "Brief explanation of why this persona and approach is optimal for the given topic"
}

**Important Requirements:**
- Make the system prompt highly specific to the domain with concrete expertise
- Include domain-specific terminology and concepts in the prompts
- Ensure the curriculum prompt focuses on practical, hands-on learning appropriate for the domain
- Make the content prompt emphasize real-world applications and industry best practices
- Keep all prompts professional but engaging
- Ensure the persona has both theoretical knowledge and practical experience
- ALL content must be generated in ${language} language - this is critical for user accessibility`;

    const llmResponse = await this.ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: metaPrompt,
      config: {
        thinkingConfig: {
          thinkingBudget: 0,
        },
      },
    });

    // Extract and parse the persona selection
    let responseText = (llmResponse.text || '').trim();

    // Remove markdown code blocks if present
    if (responseText.startsWith('```json')) {
      responseText = responseText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
    } else if (responseText.startsWith('```')) {
      responseText = responseText.replace(/^```\s*/, '').replace(/\s*```$/, '');
    }

    try {
      const personaSelection: PersonaSelection = JSON.parse(responseText);
      return personaSelection;
    } catch (parseError) {
      console.error("Failed to parse persona selection JSON:", parseError);
      console.error("Raw response:", responseText);
      throw new Error("Failed to generate valid persona selection. Please try again.");
    }
  }
}
