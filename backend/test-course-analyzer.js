import { GoogleGenAI } from '@google/genai';
import { CourseAnalyzer } from './dist/utils/course-analyzer.js';
import fs from 'fs';

// Test the course analyzer with the example content
async function testCourseAnalyzer() {
  try {
    console.log('🚀 Testing University Course Generation System...\n');
    
    // Initialize AI
    const ai = new GoogleGenAI({});
    const courseAnalyzer = new CourseAnalyzer(ai);
    
    // Read the example user content
    const userSketch = fs.readFileSync('../example-user-content.md', 'utf8');
    console.log('📖 User Sketch Length:', userSketch.length, 'characters');
    console.log('📖 First 200 characters:', userSketch.substring(0, 200) + '...\n');
    
    // Test with English
    console.log('🔍 Analyzing course sketch in English...');
    const courseAnalysis = await courseAnalyzer.analyzeCourseSketch(userSketch, 'English');
    
    console.log('\n✅ Course Analysis Complete!');
    console.log('📚 Course Title:', courseAnalysis.courseInfo.title);
    console.log('⏱️  Duration:', courseAnalysis.courseInfo.duration);
    console.log('📈 Difficulty:', courseAnalysis.courseInfo.difficulty);
    console.log('🎯 Learning Outcomes:', courseAnalysis.courseInfo.learningOutcomes.length);
    console.log('📦 Modules Generated:', courseAnalysis.modules.length);
    
    console.log('\n📋 Module Overview:');
    courseAnalysis.modules.forEach((module, index) => {
      console.log(`  ${index + 1}. ${module.title} (${module.duration})`);
      console.log(`     📝 ${module.description}`);
      console.log(`     🎯 Objectives: ${module.learningObjectives.length}`);
      console.log(`     📚 Curriculum Categories: ${module.curriculum.length}`);
      console.log('');
    });
    
    console.log('💡 Reasoning:', courseAnalysis.reasoning);
    
    // Save the result for inspection
    fs.writeFileSync('course-analysis-result.json', JSON.stringify(courseAnalysis, null, 2));
    console.log('\n💾 Full analysis saved to course-analysis-result.json');
    
  } catch (error) {
    console.error('❌ Error testing course analyzer:', error);
  }
}

testCourseAnalyzer();
