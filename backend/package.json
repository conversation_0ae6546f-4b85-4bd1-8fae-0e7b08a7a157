{"name": "qtmaster-backend", "version": "1.0.0", "description": "Backend for QtMaster.io", "main": "server.js", "type": "module", "scripts": {"start": "node dist/server.js", "dev": "tsx watch src/server.ts", "build": "tsc", "build:clean": "rm -rf dist && npm run build", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "lint": "echo 'Add ESLint configuration for linting'", "test": "echo 'Add test framework for testing'", "db:reset": "rm -f src/qtmaster.db", "prestart": "npm run build"}, "dependencies": {"@google/genai": "^1.15.0", "@types/sqlite3": "^3.1.11", "@types/uuid": "^10.0.0", "axios": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.3.0", "nodemon": "^3.0.1", "ts-node": "^10.9.2", "tsx": "^4.20.4", "typescript": "^5.9.2"}}