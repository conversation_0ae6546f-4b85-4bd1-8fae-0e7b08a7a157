# OAuth Integration Setup Guide

This guide will help you set up OAuth authentication with Google, Facebook, and GitHub for the QtMaster application.

## Prerequisites

- Access to the secure backend at `/Users/<USER>/Downloads/QtMaster.io/secure_backend/`
- Admin access to Google Cloud Console, Facebook Developers, and GitHub Developer Settings

## 🔧 Backend Configuration

The secure backend is already configured to support OAuth with the following providers:
- **Google OAuth 2.0**
- **Facebook Login**
- **GitHub OAuth**

### Environment Variables Required

Add the following variables to your `.env.development.local` file in the secure backend:

```bash
# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Facebook OAuth
FACEBOOK_APP_ID=your_facebook_app_id_here
FACEBOOK_APP_SECRET=your_facebook_app_secret_here

# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here

# OAuth Redirect URLs (already configured)
FRONTEND_URL=http://localhost:5174
BACKEND_URL=http://localhost:3001
```

## 🌐 Provider Setup Instructions

### 1. Google OAuth Setup

#### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API and Google OAuth2 API

#### Step 2: Configure OAuth Consent Screen
1. Navigate to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type
3. Fill in the required information:
   - **App name**: QtMaster
   - **User support email**: Your email
   - **Developer contact information**: Your email
4. Add scopes: `email`, `profile`, `openid`
5. Add test users if needed

#### Step 3: Create OAuth Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Choose "Web application"
4. Configure:
   - **Name**: QtMaster Web Client
   - **Authorized JavaScript origins**: `http://localhost:5174`
   - **Authorized redirect URIs**: `http://localhost:3001/api/v1/auth/oauth/google/callback`
5. Copy the **Client ID** and **Client Secret**

### 2. Facebook OAuth Setup

#### Step 1: Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click "Create App" > "Consumer" > "Next"
3. Enter app details:
   - **App name**: QtMaster
   - **Contact email**: Your email

#### Step 2: Configure Facebook Login
1. In your app dashboard, click "Add Product"
2. Find "Facebook Login" and click "Set Up"
3. Choose "Web" platform
4. Enter Site URL: `http://localhost:5174`

#### Step 3: Configure OAuth Settings
1. Go to "Facebook Login" > "Settings"
2. Add Valid OAuth Redirect URIs:
   - `http://localhost:3001/api/v1/auth/oauth/facebook/callback`
3. Go to "Settings" > "Basic"
4. Copy the **App ID** and **App Secret**

### 3. GitHub OAuth Setup

#### Step 1: Create GitHub OAuth App
1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in the application details:
   - **Application name**: QtMaster
   - **Homepage URL**: `http://localhost:5174`
   - **Authorization callback URL**: `http://localhost:3001/api/v1/auth/oauth/github/callback`

#### Step 2: Get Credentials
1. After creating the app, copy the **Client ID**
2. Generate a new **Client Secret** and copy it

## 🔐 Security Configuration

### Update Environment Variables

Edit `/Users/<USER>/Downloads/QtMaster.io/secure_backend/.env.development.local`:

```bash
# Add your OAuth credentials
GOOGLE_CLIENT_ID=your_actual_google_client_id
GOOGLE_CLIENT_SECRET=your_actual_google_client_secret

FACEBOOK_APP_ID=your_actual_facebook_app_id
FACEBOOK_APP_SECRET=your_actual_facebook_app_secret

GITHUB_CLIENT_ID=your_actual_github_client_id
GITHUB_CLIENT_SECRET=your_actual_github_client_secret
```

## 🚀 Testing the Integration

### 1. Start the Backend
```bash
cd /Users/<USER>/Downloads/QtMaster.io/secure_backend
npm run dev
```

### 2. Start the Frontend
```bash
cd /Users/<USER>/Downloads/QtMaster.io/frontend
npm run dev
```

### 3. Test OAuth Flow
1. Navigate to `http://localhost:5174/login`
2. Click on any OAuth provider button
3. Complete the OAuth flow
4. Verify successful authentication and redirect

## 🔍 Troubleshooting

### Common Issues

1. **"Redirect URI mismatch"**
   - Ensure callback URLs match exactly in provider settings
   - Check for trailing slashes

2. **"Invalid client"**
   - Verify Client ID and Secret are correct
   - Check environment variables are loaded

3. **CORS errors**
   - Ensure frontend URL is in authorized origins
   - Check CORS configuration in backend

### Debug Steps

1. Check browser network tab for failed requests
2. Verify environment variables are loaded:
   ```bash
   echo $GOOGLE_CLIENT_ID
   ```
3. Check backend logs for OAuth errors
4. Ensure all required scopes are requested

## 📝 Next Steps

After successful setup:
1. Test all three OAuth providers
2. Verify user creation in database
3. Test logout functionality
4. Configure production URLs for deployment

## 🔒 Production Considerations

For production deployment:
- Update redirect URLs to production domains
- Use environment-specific client IDs/secrets
- Enable additional security features (PKCE, state parameters)
- Configure proper CORS policies
- Use HTTPS for all OAuth flows
