# Security Implementation Tests

This document provides comprehensive testing guidance for all 6 critical and high-priority security implementations that were completed.

## Overview

The security test suite covers:
1. **CSRF Protection** - Token generation, validation, and rejection scenarios
2. **Redis Token Blacklist** - Distributed token invalidation and persistence
3. **Enhanced Input Sanitization** - XSS prevention and malicious input handling
4. **JWT Key Rotation** - Automatic rotation, multi-key verification, and fallback
5. **Session Versioning** - Session invalidation on privilege changes
6. **Redis Rate Limiting** - Distributed rate limiting across instances

## Running Security Tests

### All Security Tests
```bash
npm run test:security
```

### Security Tests with Coverage
```bash
npm run test:security:coverage
```

### Individual Test Suites
```bash
# CSRF Protection
npx jest src/tests/middlewares/csrf.middleware.test.ts

# Token Blacklist
npx jest src/tests/services/tokenBlacklist.service.test.ts

# Input Sanitization
npx jest src/tests/middlewares/inputSanitization.test.ts

# JWT Key Rotation
npx jest src/tests/services/keyRotation.service.test.ts

# Session Versioning
npx jest src/tests/services/sessionVersioning.service.test.ts
npx jest src/tests/middlewares/sessionVersioning.middleware.test.ts

# Redis Rate Limiting
npx jest src/tests/middlewares/redisRateLimit.test.ts
```

## Test Coverage Requirements

Each security implementation must maintain:
- **Unit Test Coverage**: ≥90% for service methods
- **Integration Test Coverage**: ≥85% for middleware functionality
- **Error Scenario Coverage**: ≥80% for error handling paths
- **Edge Case Coverage**: ≥75% for boundary conditions

## Test Categories

### 1. CSRF Protection Tests (`csrf.middleware.test.ts`)

**Success Scenarios:**
- ✅ Token generation on GET requests
- ✅ Token validation with valid header/body tokens
- ✅ Session persistence across requests
- ✅ API endpoint functionality

**Error/Edge Cases:**
- ✅ Rejection of missing CSRF tokens
- ✅ Rejection of invalid CSRF tokens
- ✅ Cross-session token validation
- ✅ Session error handling
- ✅ Different HTTP methods (POST, PUT, DELETE)

### 2. Redis Token Blacklist Tests (`tokenBlacklist.service.test.ts`)

**Success Scenarios:**
- ✅ Token blacklisting with automatic expiration
- ✅ Blacklist status checking
- ✅ Token removal from blacklist
- ✅ Bulk user token invalidation
- ✅ Statistics collection

**Error/Edge Cases:**
- ✅ Expired token handling
- ✅ Redis connection failures
- ✅ Invalid JSON data handling
- ✅ Partial operation failures
- ✅ Cleanup operations

### 3. Enhanced Input Sanitization Tests (`inputSanitization.test.ts`)

**Success Scenarios:**
- ✅ XSS prevention (script tags, HTML attributes)
- ✅ SQL injection prevention
- ✅ Unicode character handling
- ✅ Nested object/array sanitization

**Error/Edge Cases:**
- ✅ Input length limits
- ✅ Deeply nested objects (DoS prevention)
- ✅ Null/undefined value handling
- ✅ Circular reference handling
- ✅ Malformed JSON handling
- ✅ Performance under load

### 4. JWT Key Rotation Tests (`keyRotation.service.test.ts`)

**Success Scenarios:**
- ✅ Key generation and storage
- ✅ Current key retrieval
- ✅ Key retrieval by ID
- ✅ Force rotation scenarios
- ✅ Statistics collection

**Error/Edge Cases:**
- ✅ Redis connection failures
- ✅ Invalid JSON key data
- ✅ Concurrent rotation attempts
- ✅ Partial Redis failures
- ✅ Key cleanup operations
- ✅ Automatic rotation scheduling

### 5. Session Versioning Tests

**Service Tests (`sessionVersioning.service.test.ts`):**
- ✅ Version increment and session invalidation
- ✅ Version validation
- ✅ Session initialization
- ✅ Role/password change handling
- ✅ Statistics and cleanup

**Middleware Tests (`sessionVersioning.middleware.test.ts`):**
- ✅ Valid session version validation
- ✅ Outdated session rejection
- ✅ Session invalidation on version mismatch
- ✅ Anonymous user handling
- ✅ Concurrent request handling

### 6. Redis Rate Limiting Tests (`redisRateLimit.test.ts`)

**Success Scenarios:**
- ✅ Rate limit enforcement for different endpoints
- ✅ Distributed limiting across instances
- ✅ Statistics collection
- ✅ Different rate limit configurations

**Error/Edge Cases:**
- ✅ Rate limit exceeded scenarios
- ✅ Redis connection failures
- ✅ Missing IP address handling
- ✅ Malformed rate limiter responses
- ✅ High-frequency request handling
- ✅ Memory leak prevention

## Security Test Principles

### 1. Fail-Secure Testing
All tests verify that security measures fail securely when errors occur:
- Authentication failures should deny access, not grant it
- Rate limiting failures should block requests, not allow unlimited access
- Input sanitization failures should reject input, not pass it through

### 2. Distributed System Testing
Tests verify behavior in distributed environments:
- Redis-based solutions work across multiple instances
- Session state is properly synchronized
- Rate limits are enforced globally, not per-instance

### 3. Performance and DoS Testing
Tests include performance scenarios:
- High-frequency request handling
- Memory leak prevention
- Resource exhaustion protection

### 4. Error Recovery Testing
Tests verify graceful error handling:
- Redis connection failures
- Malformed data handling
- Partial operation failures
- Service unavailability scenarios

## Environment Setup for Testing

### Required Environment Variables
```bash
# Test environment
NODE_ENV=test

# Redis (use separate test instance)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=1  # Use different DB for tests

# JWT
JWT_SECRET=test-jwt-secret
JWT_KEY_ROTATION_ENABLED=true

# Session
SESSION_SECRET=test-session-secret

# Rate Limiting (lower limits for faster testing)
RATE_LIMIT_LOGIN_POINTS=3
RATE_LIMIT_REGISTER_POINTS=2
RATE_LIMIT_GENERAL_API_POINTS=50
```

### Test Database Setup
```bash
# Create test database
npm run db:migrate:test

# Reset test database before running tests
npm run db:reset
```

## Continuous Integration

### Pre-commit Hooks
```bash
# Run security tests before commit
npm run test:security

# Run with coverage check
npm run test:security:coverage
```

### CI Pipeline Requirements
1. All security tests must pass
2. Coverage thresholds must be met
3. No security vulnerabilities in dependencies
4. Static security analysis must pass

## Manual Security Testing

### 1. CSRF Protection
```bash
# Test token generation
curl -c cookies.txt http://localhost:3000/api/csrf-token

# Test protected endpoint with token
curl -b cookies.txt -H "x-csrf-token: TOKEN" -X POST http://localhost:3000/api/protected

# Test without token (should fail)
curl -b cookies.txt -X POST http://localhost:3000/api/protected
```

### 2. Rate Limiting
```bash
# Test login rate limiting
for i in {1..10}; do
  curl -X POST http://localhost:3000/api/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"wrong"}'
done
```

### 3. Input Sanitization
```bash
# Test XSS prevention
curl -X POST http://localhost:3000/api/test \
  -H "Content-Type: application/json" \
  -d '{"name":"<script>alert(\"xss\")</script>John"}'
```

## Security Test Maintenance

### Regular Updates
- Review and update test cases monthly
- Add new attack vectors as they emerge
- Update rate limiting thresholds based on usage patterns
- Verify Redis configuration changes don't break tests

### Performance Monitoring
- Monitor test execution time
- Check for memory leaks in long-running tests
- Verify Redis connection pooling efficiency
- Test under realistic load conditions

## Troubleshooting

### Common Issues
1. **Redis Connection Failures**: Ensure Redis is running and accessible
2. **Session Test Failures**: Check session store configuration
3. **Rate Limit Test Timing**: Adjust test timeouts for slower systems
4. **Coverage Gaps**: Add tests for uncovered error paths

### Debug Commands
```bash
# Run tests with debug output
DEBUG=* npm run test:security

# Run specific test with verbose output
npx jest --verbose src/tests/middlewares/csrf.middleware.test.ts

# Check Redis connection
redis-cli ping
```
