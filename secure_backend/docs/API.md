# 📚 Complete API Documentation

## Base URL
```
http://localhost:3000/api/v1
```

## Authentication
All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <jwt-token>
```

## Response Format
All API responses follow a consistent format:

**Success Response:**
```json
{
  "success": true,
  "message": "Operation successful",
  "data": { /* response data */ },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Error message",
  "statusCode": 400,
  "details": [ /* validation errors if applicable */ ],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## 🔑 Authentication Endpoints

### Register User
**POST** `/auth/register`

Register a new user account with comprehensive validation.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "testuser",
  "password": "SecurePassword123!",
  "name": "Test User"
}
```

**Validation Rules:**
- Email: Valid email format, unique
- Username: 3-30 characters, alphanumeric + underscore, unique
- Password: Min 8 chars, uppercase, lowercase, number, special char
- Name: Optional, max 100 characters

**Response (201):**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "cmdyftl8n00009khabaignxpl",
      "email": "<EMAIL>",
      "username": "testuser",
      "name": "Test User",
      "role": "USER",
      "emailVerified": null,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Responses:**
```json
// Validation Error (400)
{
  "success": false,
  "error": "Validation failed",
  "details": [
    {
      "field": "email",
      "message": "Email already exists"
    }
  ],
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z"
}

// Rate Limit Error (429)
{
  "success": false,
  "error": "Too many registration attempts. Try again in 3600 seconds.",
  "statusCode": 429,
  "retryAfter": 3600,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Login User
**POST** `/auth/login`

Authenticate user with enhanced security features including session management and MFA support.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response (200) - Without MFA:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "cmdyftl8n00009khabaignxpl",
      "email": "<EMAIL>",
      "username": "testuser",
      "name": "Test User",
      "role": "USER",
      "mfaEnabled": false,
      "emailVerified": "2024-01-01T00:00:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "cmdyftl8n00009khabaignxpl_refresh",
    "expiresIn": "7d",
    "requiresMFA": false,
    "sessionInfo": {
      "sessionId": "cmdyftl8n00009khabaignxpl_session",
      "deviceId": "5a29d59a698be0a38c1e81588402dfda",
      "deviceName": "Chrome on macOS",
      "isTrusted": false,
      "riskScore": 0.3
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Response (200) - With MFA Required:**
```json
{
  "success": true,
  "message": "MFA verification required",
  "data": {
    "requiresMFA": true,
    "mfaToken": "temp-mfa-token-for-verification",
    "expiresIn": "5m"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Responses:**
```json
// Invalid Credentials (401)
{
  "success": false,
  "error": "Invalid email or password",
  "statusCode": 401,
  "timestamp": "2024-01-01T00:00:00.000Z"
}

// Account Locked (423)
{
  "success": false,
  "error": "Account temporarily locked due to multiple failed login attempts",
  "statusCode": 423,
  "lockoutExpires": "2024-01-01T01:00:00.000Z",
  "timestamp": "2024-01-01T00:00:00.000Z"
}

// Rate Limited (429)
{
  "success": false,
  "error": "Too many login attempts. Try again in 900 seconds.",
  "statusCode": 429,
  "retryAfter": 900,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Refresh Token
**POST** `/auth/refresh`

Refresh JWT token using refresh token with automatic rotation.

**Request Body:**
```json
{
  "refreshToken": "cmdyftl8n00009khabaignxpl_refresh"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "new_refresh_token_uuid",
    "expiresIn": "7d"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Logout
**POST** `/auth/logout`

Logout user and invalidate all tokens with session cleanup.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "message": "Logout successful",
  "data": {
    "sessionTerminated": true,
    "tokensInvalidated": true
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## 🔐 Multi-Factor Authentication (MFA)

### Initialize MFA Setup
**POST** `/mfa/setup/initialize`

Start MFA setup process and get QR code for authenticator app.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "setupToken": "setup-token-uuid",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "manualEntryKey": "JBSWY3DPEHPK3PXP",
    "expiresAt": "2024-01-01T01:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Complete MFA Setup
**POST** `/mfa/setup/complete`

Complete MFA setup with TOTP verification and receive backup codes.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "setupToken": "setup-token-uuid",
  "totpCode": "123456"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "MFA setup completed successfully",
  "data": {
    "backupCodes": [
      "backup-code-1",
      "backup-code-2",
      "backup-code-3",
      "backup-code-4",
      "backup-code-5",
      "backup-code-6",
      "backup-code-7",
      "backup-code-8",
      "backup-code-9",
      "backup-code-10"
    ]
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Verify TOTP Code
**POST** `/mfa/verify/totp`

Verify TOTP code during login process.

**Headers:** `X-MFA-Token: <mfa-token>` (from login response)

**Request Body:**
```json
{
  "totpCode": "123456"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "TOTP verification successful",
  "data": {
    "token": "jwt-token-after-mfa",
    "refreshToken": "refresh-token-uuid",
    "expiresIn": "7d",
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "username": "testuser",
      "role": "USER"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Get MFA Status
**GET** `/mfa/status`

Get current MFA status and backup codes remaining.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "isEnabled": true,
    "backupCodesRemaining": 8,
    "setupDate": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## 📱 Session Management

### List User Sessions
**GET** `/sessions`

Get all sessions for the authenticated user with detailed information.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `limit` (optional): Number of sessions to return (default: 50, max: 100)
- `offset` (optional): Number of sessions to skip (default: 0)

**Response (200):**
```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "id": "session-uuid",
        "deviceId": "device-fingerprint-hash",
        "deviceName": "Chrome on macOS",
        "deviceType": "desktop",
        "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)...",
        "ipAddress": "*************",
        "location": "San Francisco, CA",
        "isActive": true,
        "isTrusted": true,
        "isCurrent": true,
        "lastActivity": "2024-01-01T00:00:00.000Z",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "expiresAt": "2024-01-08T00:00:00.000Z",
        "riskScore": 0.1,
        "suspiciousActivity": false,
        "recentActivity": [
          {
            "action": "login",
            "timestamp": "2024-01-01T00:00:00.000Z",
            "ipAddress": "*************"
          }
        ]
      }
    ],
    "totalSessions": 3,
    "activeSessions": 2,
    "trustedDevices": 1,
    "pagination": {
      "limit": 50,
      "offset": 0,
      "total": 3
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Terminate Session
**POST** `/sessions/terminate`

Terminate a specific session by ID.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "sessionId": "session-uuid"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "Session terminated successfully",
  "data": {
    "sessionId": "session-uuid",
    "terminatedAt": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## 🛡️ Security Management

### Get Security Status
**GET** `/security/status`

Get overall security system status and health metrics.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "status": "HEALTHY",
    "securityScore": 95,
    "metrics": {
      "activeAttacks": 0,
      "blockedIPs": 23,
      "totalAttacks": 156,
      "recentHighAlerts": 2
    },
    "systemHealth": {
      "redis": true,
      "rateLimiting": true
    },
    "lastUpdated": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Get DDoS Metrics
**GET** `/security/ddos/metrics`

Get comprehensive DDoS attack metrics and statistics.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `hours` (optional): Time range in hours (default: 24, max: 168)

**Response (200):**
```json
{
  "success": true,
  "data": {
    "metrics": {
      "totalAttacks": 45,
      "activeAttacks": 2,
      "blockedIPs": 23,
      "topAttackingIPs": [
        {
          "ip": "***********",
          "count": 15
        }
      ],
      "attackPatterns": [
        {
          "pattern": "rapid_sequential_requests",
          "count": 30
        }
      ],
      "timeSeriesData": [
        {
          "timestamp": 1704067200000,
          "attacks": 5
        }
      ]
    },
    "timeRange": "24 hours",
    "generatedAt": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## ❌ Error Responses

All endpoints may return the following error responses:

### Common Error Codes

**400 - Bad Request**
```json
{
  "success": false,
  "error": "Validation failed",
  "statusCode": 400,
  "details": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**401 - Unauthorized**
```json
{
  "success": false,
  "error": "Invalid or expired token",
  "statusCode": 401,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**403 - Forbidden**
```json
{
  "success": false,
  "error": "Insufficient permissions",
  "statusCode": 403,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**429 - Too Many Requests**
```json
{
  "success": false,
  "error": "Rate limit exceeded",
  "statusCode": 429,
  "retryAfter": 60,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**500 - Internal Server Error**
```json
{
  "success": false,
  "error": "Internal server error",
  "statusCode": 500,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

---

## 🔗 Rate Limiting Headers

All API responses include rate limiting headers:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 2024-01-01T01:00:00.000Z
```

When rate limited:
```
Retry-After: 60
```

---

## 📊 Interactive API Testing

Visit `http://localhost:3000/api-docs` for interactive API documentation with:
- Live endpoint testing
- Request/response examples
- Authentication flow testing
- Real-time API exploration

### `GET /api/v1/users/profile`

- **Description**: Retrieves the current user's profile.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: User profile retrieved successfully.
  - `401`: Unauthorized.

### `GET /api/v1/users/search`

- **Description**: Searches for users.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: Users found.
  - `401`: Unauthorized.

### `GET /api/v1/users/:id`

- **Description**: Retrieves a user by ID.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: User retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.
  - `404`: User not found.

### `PUT /api/v1/users/:id`

- **Description**: Updates a user.
- **Request Body**: `UpdateUserDto`
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: User updated successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `DELETE /api/v1/users/:id`

- **Description**: Deletes a user.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: User deleted successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/users`

- **Description**: Retrieves all users with filters and pagination (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.READ)`
- **Response**:
  - `200`: Users retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/users/role/:role`

- **Description**: Retrieves users by role (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.READ)`
- **Response**:
  - `200`: Users retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/users/admin/stats`

- **Description**: Retrieves user statistics (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.READ)`
- **Response**:
  - `200`: User statistics retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `PATCH /api/v1/users/:id/role`

- **Description**: Updates a user's role (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.UPDATE)`
- **Response**:
  - `200`: User role updated successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/users/email/:email`

- **Description**: Retrieves a user by email (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.READ)`
- **Response**:
  - `200`: User retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

## Authentication

### `POST /api/v1/auth/register`

- **Description**: Registers a new user.
- **Request Body**: `RegisterDto`
- **Middleware**: `detectSuspiciousActivity`, `registerRateLimit`, `validatePasswordStrength`
- **Response**:
  - `201`: User created successfully.
  - `400`: Invalid request body.
  - `429`: Too many requests.

### `POST /api/v1/auth/login`

- **Description**: Logs in a user.
- **Request Body**: `LoginDto`
- **Middleware**: `detectSuspiciousActivity`, `loginRateLimit`
- **Response**:
  - `200`: User logged in successfully.
  - `401`: Unauthorized.
  - `429`: Too many requests.

### `POST /api/v1/auth/refresh`

- **Description**: Refreshes a user's JWT token.
- **Middleware**: `rateLimiterMiddleware`
- **Response**:
  - `200`: Token refreshed successfully.
  - `401`: Unauthorized.

### `POST /api/v1/auth/logout`

- **Description**: Logs out a user.
- **Response**:
  - `200`: User logged out successfully.

### `POST /api/v1/auth/forgot-password`

- **Description**: Initiates password reset process by sending a reset token to user's email.
- **Request Body**: `ForgotPasswordDto` (email or username)
- **Middleware**: `detectSuspiciousActivity`, `rateLimiterMiddleware`
- **Response**:
  - `200`: Password reset email sent (or success message for security).
  - `429`: Too many requests (rate limited).

### `POST /api/v1/auth/reset-password`

- **Description**: Completes password reset using the token from email.
- **Request Body**: `ResetPasswordDto` (token and newPassword)
- **Middleware**: `detectSuspiciousActivity`, `rateLimiterMiddleware`, `validatePasswordStrength`
- **Response**:
  - `200`: Password reset successfully.
  - `400`: Invalid or expired token, or weak password.

### `GET /api/v1/auth/validate-reset-token`

- **Description**: Validates a password reset token (for frontend validation).
- **Query Parameters**: `token` (string, required)
- **Response**:
  - `200`: Token validation result with `valid` and `expired` flags.

### `POST /api/v1/auth/verify-email`

- **Description**: Verifies a user's email address.
- **Request Body**: `VerifyEmailDto`
- **Middleware**: `detectSuspiciousActivity`, `emailVerificationRateLimit`
- **Response**:
  - `200`: Email verified successfully.
  - `400`: Invalid or expired token.
  - `429`: Too many requests.

### `POST /api/v1/auth/resend-verification`

- **Description**: Resends the email verification link.
- **Request Body**: `ResendVerificationDto`
- **Middleware**: `detectSuspiciousActivity`, `emailVerificationRateLimit`
- **Response**:
  - `200`: Verification email sent successfully.
  - `400`: Bad request.
  - `429`: Too many requests.

### `GET /api/v1/auth/profile`

- **Description**: Retrieves the current user's profile.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: User profile retrieved successfully.
  - `401`: Unauthorized.

### `PATCH /api/v1/auth/profile`

- **Description**: Updates the current user's profile.
- **Request Body**: `UpdateUserDto`
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.PROFILE, Action.UPDATE)`
- **Response**:
  - `200`: User profile updated successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `PATCH /api/v1/auth/change-password`

- **Description**: Changes the current user's password.
- **Request Body**: `ChangePasswordDto`
- **Middleware**: `authMiddleware`, `detectSuspiciousActivity`, `passwordChangeRateLimit`, `validatePasswordStrength`
- **Response**:
  - `200`: Password changed successfully.
  - `401`: Unauthorized.
  - `429`: Too many requests.

### `POST /api/v1/auth/change-email`

- **Description**: Initiates an email address change.
- **Request Body**: `ChangeEmailDto`
- **Middleware**: `authMiddleware`, `detectSuspiciousActivity`, `emailVerificationRateLimit`
- **Response**:
  - `200`: Email change verification sent successfully.
  - `401`: Unauthorized.
  - `409`: Conflict.
  - `429`: Too many requests.

### `GET /api/v1/auth/permissions`

- **Description**: Retrieves the current user's permissions.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: Permissions retrieved successfully.
  - `401`: Unauthorized.

### `GET /api/v1/auth/check-permission/:resource/:action`

- **Description**: Checks if the current user has a specific permission.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: Permission check successful.
  - `401`: Unauthorized.

### `GET /api/v1/auth/users`

- **Description**: Retrieves all users (admin only).
- **Middleware**: `authMiddleware`, `authorizationMiddleware([Role.ADMIN])`
- **Response**:
  - `200`: Users retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/auth/users-rbac`

- **Description**: Retrieves all users (permission-based).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.READ)`
- **Response**:
  - `200`: Users retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `PATCH /api/v1/auth/users/:userId/role`

- **Description**: Updates a user's role (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.UPDATE)`
- **Response**:
  - `200`: User role updated successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `DELETE /api/v1/auth/users/:userId`

- **Description**: Deletes a user (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.DELETE)`
- **Response**:
  - `200`: User deleted successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/auth/can-access-users`

- **Description**: Checks if the current user can access user management.
- **Middleware**: `authMiddleware`, `resourceAccessMiddleware(Resource.USER)`
- **Response**:
  - `200`: Access check successful.
  - `401`: Unauthorized.

### `GET /api/v1/auth/can-access-analytics`

- **Description**: Checks if the current user can access analytics.
- **Middleware**: `authMiddleware`, `resourceAccessMiddleware(Resource.ANALYTICS)`
- **Response**:
  - `200`: Access check successful.
  - `401`: Unauthorized.

## OAuth Authentication

### `GET /api/v1/auth/oauth/:provider`

- **Description**: Initiates OAuth authentication with the specified provider.
- **Parameters**:
  - `provider`: OAuth provider (google, facebook, github)
- **Middleware**: `validateProviderParam`, `validateProvider`, `oauthInitiateRateLimitMiddleware`, `generateOAuthState`
- **Response**:
  - `302`: Redirect to OAuth provider
  - `400`: Invalid provider or configuration error
  - `429`: Rate limit exceeded

### `GET /api/v1/auth/oauth/:provider/callback`

- **Description**: Handles OAuth callback from the provider.
- **Parameters**:
  - `provider`: OAuth provider (google, facebook, github)
- **Query Parameters**:
  - `code`: Authorization code from OAuth provider
  - `state`: State parameter for CSRF protection
  - `error`: Error code from OAuth provider (optional)
- **Middleware**: `validateProviderParam`, `oauthCallbackRateLimitMiddleware`
- **Response**:
  - `302`: Redirect to success or failure URL
  - `400`: Invalid callback parameters
  - `429`: Rate limit exceeded

### `GET /api/v1/auth/oauth/accounts`

- **Description**: Retrieves user's linked OAuth accounts.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: List of linked OAuth accounts
  - `401`: Unauthorized

### `POST /api/v1/auth/oauth/link/:provider`

- **Description**: Links an OAuth account to the current user.
- **Parameters**:
  - `provider`: OAuth provider (google, facebook, github)
- **Middleware**: `authMiddleware`, `validateProviderParam`, `validateProvider`, `oauthLinkingRateLimitMiddleware`, `generateOAuthState`
- **Response**:
  - `302`: Redirect to OAuth provider for linking
  - `400`: Invalid provider or account already linked
  - `401`: Unauthorized
  - `429`: Rate limit exceeded

### `DELETE /api/v1/auth/oauth/unlink/:provider`

- **Description**: Unlinks an OAuth account from the current user.
- **Parameters**:
  - `provider`: OAuth provider (google, facebook, github)
- **Middleware**: `authMiddleware`, `validateProviderParam`, `validateProvider`, `oauthLinkingRateLimitMiddleware`
- **Response**:
  - `200`: OAuth account unlinked successfully
  - `400`: Cannot unlink the only authentication method
  - `401`: Unauthorized
  - `404`: OAuth account not found
