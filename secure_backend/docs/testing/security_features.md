# Security Features Testing Guide

**Title:** Security Features Testing Guide  
**Description:** Comprehensive testing documentation for security mechanisms including rate limiting, input validation, error handling, CSRF protection, and security headers validation.

## Overview

This guide provides practical testing procedures for validating security features implemented in the secure backend API. It covers automated testing techniques and manual verification methods for critical security controls.

## Prerequisites

> **Environment Setup:** See [Environment Configuration Guide](_env.md) for setting up testing variables.
> **Authentication Required:** Some tests require authentication tokens - see [Authentication Guide](authentication.md).

```bash
# Load environment variables from _env.md
source <(grep '^export' docs/testing/_env.md | sed 's/^export /export /')
```

- Backend server running (default: `http://localhost:3000`)
- curl command-line tool
- Basic understanding of HTTP methods and status codes
- Access to server logs for monitoring

## Testing Environment Setup

```bash
# Set base URL for testing
export BASE_URL="http://localhost:3000/api/v1"
export HEALTH_URL="http://localhost:3000/health"

# Test server availability
curl -s "$HEALTH_URL" | jq .
```

## 1. Rate Limiting Testing

### 1.1 General Rate Limit Testing

**Objective:** Trigger 429 (Too Many Requests) responses

#### Method 1: Loop with cURL
```bash
# Test general rate limiting (100 requests in quick succession)
for i in {1..150}; do
  echo "Request $i:"
  curl -w "Status: %{http_code}\n" -s -o /dev/null "$BASE_URL/auth/profile" -H "Authorization: Bearer invalid_token"
  sleep 0.1
done
```

#### Method 2: Parallel requests with seq and xargs
```bash
# Generate 200 parallel requests to trigger rate limiting
seq 1 200 | xargs -I{} -P 20 curl -w "Request {}: %{http_code}\n" -s -o /dev/null "$BASE_URL/"
```

### 1.2 Login Rate Limit Testing

```bash
# Test login rate limiting (should trigger after multiple failed attempts)
for i in {1..25}; do
  echo "Login attempt $i:"
  curl -X POST "$BASE_URL/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"wrongpassword"}' \
    -w "Status: %{http_code}\n" \
    -s -o /dev/null
  sleep 0.2
done
```

### 1.3 Registration Rate Limit Testing

```bash
# Test registration rate limiting
for i in {1..15}; do
  echo "Registration attempt $i:"
  curl -X POST "$BASE_URL/auth/register" \
    -H "Content-Type: application/json" \
    -d '{"email":"test'$i'@example.com","password":"TestPass123!","firstName":"Test","lastName":"User"}' \
    -w "Status: %{http_code}\n" \
    -s -o /dev/null
  sleep 0.1
done
```

### 1.4 Password Change Rate Limit Testing

```bash
# First, obtain a valid token (replace with actual login)
TOKEN="your_jwt_token_here"

# Test password change rate limiting
for i in {1..10}; do
  echo "Password change attempt $i:"
  curl -X PATCH "$BASE_URL/auth/change-password" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{"currentPassword":"currentpass","newPassword":"NewPass123!"}' \
    -w "Status: %{http_code}\n" \
    -s -o /dev/null
  sleep 0.2
done
```

**Expected Results:**
- Normal requests should return 200/201
- Rate-limited requests should return 429
- Response should include rate limit headers

## 2. Input Validation Testing

### 2.1 SQL Injection Testing

#### Authentication Endpoints
```bash
# Test SQL injection in login endpoint
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL> OR 1=1 --","password":"anything"}' \
  -v

# Test SQL injection with UNION attack
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>\' UNION SELECT 1,2,3,4,5 --","password":"test"}' \
  -v

# Test SQL injection in registration
curl -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>\'; DROP TABLE users; --","password":"TestPass123!","firstName":"Test","lastName":"User"}' \
  -v
```

#### Search and Query Endpoints
```bash
# Test SQL injection in user search
curl "$BASE_URL/users/search?q=test\' OR 1=1 --" \
  -H "Authorization: Bearer $TOKEN" \
  -v

# Test SQL injection in user ID parameter
curl "$BASE_URL/users/1\' OR 1=1 --" \
  -H "Authorization: Bearer $TOKEN" \
  -v
```

### 2.2 XSS (Cross-Site Scripting) Testing

```bash
# Test XSS in user profile fields
curl -X PATCH "$BASE_URL/auth/profile" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"firstName":"<script>alert(\"XSS\")</script>","lastName":"User"}' \
  -v

# Test XSS with different payloads
curl -X PATCH "$BASE_URL/auth/profile" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"firstName":"<img src=x onerror=alert(1)>","lastName":"javascript:alert(1)"}' \
  -v

# Test XSS in registration
curl -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123!","firstName":"<script>alert(1)</script>","lastName":"User"}' \
  -v
```

### 2.3 Oversized Payload Testing

```bash
# Generate large payload (1MB+)
LARGE_STRING=$(python3 -c "print('A' * 1000000)")

# Test oversized JSON payload
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"'$LARGE_STRING'"}' \
  -w "Status: %{http_code}, Size: %{size_request}\n" \
  -v

# Test oversized header
curl -X GET "$BASE_URL/" \
  -H "X-Large-Header: $LARGE_STRING" \
  -w "Status: %{http_code}\n" \
  -v
```

### 2.4 Malformed Request Testing

```bash
# Test invalid JSON
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test"' \
  -v

# Test missing required fields
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}' \
  -v

# Test invalid email format
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"invalid-email","password":"test"}' \
  -v
```

**Expected Results:**
- SQL injection attempts should be blocked/sanitized
- XSS payloads should be sanitized or rejected
- Oversized payloads should return 413 (Payload Too Large)
- Malformed requests should return 400 (Bad Request)

## 3. Error Handling Validation

### 3.1 Standardized Error Envelope Testing

```bash
# Test various error scenarios and verify response format

# 404 Not Found
curl -X GET "$BASE_URL/nonexistent" -v | jq .

# 401 Unauthorized
curl -X GET "$BASE_URL/auth/profile" -v | jq .

# 403 Forbidden
curl -X GET "$BASE_URL/auth/users" \
  -H "Authorization: Bearer invalid_token" -v | jq .

# 400 Bad Request
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"invalid":"data"}' -v | jq .

# 429 Rate Limited
# (Use rate limiting tests from section 1)
```

### 3.2 Correlation ID Verification

```bash
# Check for correlation IDs in error responses
curl -X GET "$BASE_URL/auth/profile" -v 2>&1 | grep -i "correlation\|request-id\|x-request-id"

# Check error response structure
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"invalid"}' | jq .
```

### 3.3 Error Information Disclosure Testing

```bash
# Test that errors don't reveal sensitive information
curl -X GET "$BASE_URL/users/99999" \
  -H "Authorization: Bearer $TOKEN" | jq .

# Test database error handling
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"wrongpassword"}' | jq .
```

**Expected Error Response Format:**
```json
{
  "success": false,
  "message": "Human-readable error message",
  "error": {
    "code": "ERROR_CODE",
    "details": "Additional context if appropriate"
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/v1/endpoint",
  "correlationId": "uuid-string"
}
```

## 4. CSRF Protection Testing

### 4.1 Missing CSRF Token Testing

```bash
# Test state-changing operations without proper headers
# This assumes CSRF protection is implemented

# Test POST without Origin header
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123!"}' \
  -v

# Test with suspicious Origin header
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -H "Origin: https://malicious-site.com" \
  -d '{"email":"<EMAIL>","password":"TestPass123!"}' \
  -v
```

### 4.2 Cross-Origin Request Testing

```bash
# Test CORS configuration
curl -X OPTIONS "$BASE_URL/auth/login" \
  -H "Origin: https://evil.com" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type" \
  -v

# Test with different origins
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -H "Origin: https://unauthorized-domain.com" \
  -d '{"email":"<EMAIL>","password":"TestPass123!"}' \
  -v
```

### 4.3 Referer Header Testing

```bash
# Test with suspicious Referer header
curl -X POST "$BASE_URL/auth/change-password" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Referer: https://malicious-site.com/csrf-attack" \
  -d '{"currentPassword":"current","newPassword":"NewPass123!"}' \
  -v
```

**Expected Results:**
- Requests from unauthorized origins should be blocked
- CSRF attacks should be prevented
- OPTIONS requests should return appropriate CORS headers

## 5. Security Headers Validation

### 5.1 Comprehensive Security Headers Check

```bash
# Function to check security headers
check_security_headers() {
  local url=$1
  echo "Checking security headers for: $url"
  
  curl -s -I "$url" | grep -i -E '(content-security-policy|strict-transport-security|x-frame-options|x-content-type-options|x-xss-protection|referrer-policy|permissions-policy)'
  echo "---"
}

# Check headers on various endpoints
check_security_headers "$BASE_URL/"
check_security_headers "$BASE_URL/auth/login"
check_security_headers "$BASE_URL/users"
check_security_headers "$HEALTH_URL"
```

### 5.2 Content Security Policy (CSP) Testing

```bash
# Check CSP header presence and configuration
curl -s -I "$BASE_URL/" | grep -i "content-security-policy"

# Test CSP with different content types
curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123!"}' \
  -I | grep -i "content-security-policy"
```

### 5.3 HTTP Strict Transport Security (HSTS) Testing

```bash
# Check HSTS header (should be present in production)
curl -s -I "$BASE_URL/" | grep -i "strict-transport-security"

# Check HSTS configuration details
curl -s -I "$BASE_URL/" | grep -i "strict-transport-security" | \
  sed 's/.*: //' | tr ';' '\n'
```

### 5.4 Additional Security Headers

```bash
# Check X-Frame-Options
curl -s -I "$BASE_URL/" | grep -i "x-frame-options"

# Check X-Content-Type-Options
curl -s -I "$BASE_URL/" | grep -i "x-content-type-options"

# Check X-XSS-Protection
curl -s -I "$BASE_URL/" | grep -i "x-xss-protection"

# Check Referrer-Policy
curl -s -I "$BASE_URL/" | grep -i "referrer-policy"

# Check Permissions-Policy (Feature-Policy)
curl -s -I "$BASE_URL/" | grep -i -E "permissions-policy|feature-policy"
```

### 5.5 Security Headers Summary Script

```bash
#!/bin/bash
# security_headers_check.sh

BASE_URL="http://localhost:3000/api/v1"

echo "=== Security Headers Analysis ==="
echo "Testing URL: $BASE_URL"
echo ""

headers_response=$(curl -s -I "$BASE_URL/")

# Check each security header
echo "🔐 Content-Security-Policy:"
echo "$headers_response" | grep -i "content-security-policy" || echo "❌ Missing"
echo ""

echo "🔒 Strict-Transport-Security:"
echo "$headers_response" | grep -i "strict-transport-security" || echo "❌ Missing"
echo ""

echo "🖼️ X-Frame-Options:"
echo "$headers_response" | grep -i "x-frame-options" || echo "❌ Missing"
echo ""

echo "📄 X-Content-Type-Options:"
echo "$headers_response" | grep -i "x-content-type-options" || echo "❌ Missing"
echo ""

echo "🛡️ X-XSS-Protection:"
echo "$headers_response" | grep -i "x-xss-protection" || echo "❌ Missing"
echo ""

echo "🔗 Referrer-Policy:"
echo "$headers_response" | grep -i "referrer-policy" || echo "❌ Missing"
echo ""

echo "⚙️ Permissions-Policy:"
echo "$headers_response" | grep -i -E "permissions-policy|feature-policy" || echo "❌ Missing"
echo ""
```

**Expected Security Headers:**
- `Content-Security-Policy`: Prevents XSS attacks
- `Strict-Transport-Security`: Forces HTTPS (production)
- `X-Frame-Options`: Prevents clickjacking
- `X-Content-Type-Options`: Prevents MIME sniffing
- `X-XSS-Protection`: Browser XSS protection
- `Referrer-Policy`: Controls referrer information
- `Permissions-Policy`: Controls browser features

## 6. Authentication and Authorization Testing

### 6.1 JWT Token Testing

```bash
# Test with invalid JWT token
curl -X GET "$BASE_URL/auth/profile" \
  -H "Authorization: Bearer invalid.jwt.token" \
  -v

# Test with expired token (if available)
curl -X GET "$BASE_URL/auth/profile" \
  -H "Authorization: Bearer expired_jwt_token_here" \
  -v

# Test with malformed Authorization header
curl -X GET "$BASE_URL/auth/profile" \
  -H "Authorization: Bearer" \
  -v

curl -X GET "$BASE_URL/auth/profile" \
  -H "Authorization: InvalidPrefix valid_token_here" \
  -v
```

### 6.2 Role-Based Access Control Testing

```bash
# Test admin-only endpoints with regular user token
USER_TOKEN="regular_user_token_here"

curl -X GET "$BASE_URL/auth/users" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -v

# Test resource-based permissions
curl -X DELETE "$BASE_URL/users/123" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -v
```

## 7. Complete Security Test Suite Script

```bash
#!/bin/bash
# complete_security_test.sh

set -e

BASE_URL="http://localhost:3000/api/v1"
HEALTH_URL="http://localhost:3000/health"

echo "🔒 Starting Comprehensive Security Test Suite"
echo "Testing against: $BASE_URL"
echo "======================================="

# Test 1: Server availability
echo "\n📡 Test 1: Server Availability"
if curl -s "$HEALTH_URL" > /dev/null; then
  echo "✅ Server is responding"
else
  echo "❌ Server is not responding"
  exit 1
fi

# Test 2: Rate limiting
echo "\n🚦 Test 2: Rate Limiting"
echo "Testing with 50 rapid requests..."
rate_limit_count=0
for i in {1..50}; do
  status_code=$(curl -w "%{http_code}" -s -o /dev/null "$BASE_URL/")
  if [ "$status_code" = "429" ]; then
    rate_limit_count=$((rate_limit_count + 1))
  fi
  sleep 0.05
done

if [ $rate_limit_count -gt 0 ]; then
  echo "✅ Rate limiting working ($rate_limit_count requests blocked)"
else
  echo "⚠️ Rate limiting not triggered (may need more requests)"
fi

# Test 3: SQL Injection protection
echo "\n🛡️ Test 3: SQL Injection Protection"
sql_inject_response=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL> OR 1=1 --","password":"anything"}')

if echo "$sql_inject_response" | grep -q "success.*false"; then
  echo "✅ SQL injection blocked"
else
  echo "❌ SQL injection may not be properly handled"
fi

# Test 4: Input validation
echo "\n✅ Test 4: Input Validation"
oversized_response=$(curl -w "%{http_code}" -s -o /dev/null -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"'$(python3 -c "print('A' * 10000)")'"}')

if [ "$oversized_response" = "400" ] || [ "$oversized_response" = "413" ]; then
  echo "✅ Oversized payload rejected (HTTP $oversized_response)"
else
  echo "⚠️ Oversized payload handling may need review (HTTP $oversized_response)"
fi

# Test 5: Security headers
echo "\n🔐 Test 5: Security Headers"
headers_response=$(curl -s -I "$BASE_URL/")

security_headers=("content-security-policy" "x-frame-options" "x-content-type-options")
header_count=0

for header in "${security_headers[@]}"; do
  if echo "$headers_response" | grep -qi "$header"; then
    header_count=$((header_count + 1))
  fi
done

echo "✅ Security headers present: $header_count/${#security_headers[@]}"

# Test 6: Error handling
echo "\n🚨 Test 6: Error Handling"
error_response=$(curl -s "$BASE_URL/nonexistent")

if echo "$error_response" | grep -q '"success".*false'; then
  echo "✅ Standardized error format detected"
else
  echo "⚠️ Error format may need review"
fi

echo "\n🔒 Security test suite completed!"
echo "Please review any warnings above and check server logs for detailed analysis."
```

## 8. Monitoring and Logging

### 8.1 Security Event Monitoring

```bash
# Monitor logs for security events while testing
tail -f logs/error.log | grep -i -E '(rate.limit|suspicious|attack|inject|xss|csrf)'

# Check for authentication failures
tail -f logs/combined.log | grep -i -E '(failed.login|unauthorized|forbidden)'
```

### 8.2 Performance Impact Testing

```bash
# Test response times with security middleware
time curl -s "$BASE_URL/" > /dev/null

# Load test with security enabled
ab -n 100 -c 10 "$BASE_URL/"
```

## 9. Expected Security Responses Summary

| Test Category | Expected Behavior | HTTP Status | Response Indicators |
|---------------|-------------------|-------------|---------------------|
| Rate Limiting | Block excessive requests | 429 | "Too Many Requests" message |
| SQL Injection | Sanitize/reject malicious input | 400/401 | Validation error, no data exposure |
| XSS Attempts | Sanitize script tags | 400 | Sanitized output, no script execution |
| Oversized Payload | Reject large requests | 413 | "Payload Too Large" |
| Invalid JWT | Deny access | 401 | "Unauthorized" message |
| Missing Authorization | Deny protected resources | 401 | Authentication required |
| Insufficient Permissions | Deny admin actions | 403 | "Forbidden" message |
| CSRF Attempts | Block cross-origin attacks | 403 | Origin/referrer validation |
| Invalid Input | Return validation errors | 400 | Structured error format |

## 10. Security Testing Checklist

### Pre-Testing
- [ ] Server is running and accessible
- [ ] Testing environment is isolated
- [ ] Log monitoring is enabled
- [ ] Backup/restore procedures tested

### During Testing
- [ ] Rate limiting triggers correctly
- [ ] Input validation blocks malicious payloads
- [ ] Authentication enforces access control
- [ ] Authorization prevents privilege escalation
- [ ] Error handling doesn't leak information
- [ ] Security headers are present
- [ ] CORS/CSRF protection works

### Post-Testing
- [ ] Review security event logs
- [ ] Document any vulnerabilities found
- [ ] Update security configurations if needed
- [ ] Plan remediation for any issues

## 11. Automated Security Testing Integration

### CI/CD Pipeline Integration

```yaml
# Example GitHub Actions security test
name: Security Tests
on: [push, pull_request]

jobs:
  security-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Start server
        run: npm start &
      - name: Wait for server
        run: sleep 10
      - name: Run security tests
        run: ./scripts/complete_security_test.sh
      - name: Check for vulnerabilities
        run: npm audit --audit-level=moderate
```

This comprehensive testing guide provides practical, executable tests for validating all major security features of your secure backend API. Regular execution of these tests helps ensure ongoing security posture and early detection of potential vulnerabilities.
