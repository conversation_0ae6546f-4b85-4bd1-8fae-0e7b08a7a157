# User Management Testing Guide

**Title:** User Management API Testing Guide  
**Description:** Comprehensive testing documentation for user lifecycle management, including profile operations, password management, admin operations, pagination, filtering, and validation error handling.

## Prerequisites

> **See Authentication Guide for obtaining tokens:** Refer to [Authentication Guide](authentication.md) for login procedures to obtain access tokens.
> **Environment Setup:** See [Environment Configuration Guide](_env.md) for setting up testing variables.

```bash
# Load environment variables from _env.md
source <(grep '^export' docs/testing/_env.md | sed 's/^export /export /')

# Ensure authentication tokens are set after logging in
# USER_JWT_TOKEN should be set from authentication guide
# ADMIN_JWT_TOKEN should be set from admin authentication
```

## Table of Contents

1. [Profile Fetch/Update Operations](#profile-fetchupdate-operations)
2. [Password Change Flow](#password-change-flow)
3. [Admin Operations](#admin-operations)
4. [Pagination and Filtering](#pagination-and-filtering)
5. [Expected <PERSON><PERSON><PERSON> Shapes](#expected-json-shapes)
6. [Validation Error Payloads](#validation-error-payloads)
7. [Testing Scenarios](#testing-scenarios)

## Profile Fetch/Update Operations

### Profile Fetch (GET)

#### 1. Get Current User Profile
```bash
# Endpoint: GET /api/v1/users/profile
# Authentication: Required (Bearer token or Cookie)

curl -X GET "$USERS_BASE_URL/profile" \
  -H "Authorization: Bearer $USER_JWT_TOKEN" \
  -H "Content-Type: $CONTENT_TYPE"

# Alternative with cookies (if using cookie-based auth)
curl -X GET "$USERS_BASE_URL/profile" \
  -H "Content-Type: $CONTENT_TYPE" \
  -b $COOKIE_JAR
```

**Expected Response (200 OK):**
```json
{
  "success": true,
  "message": "User retrieved successfully",
  "data": {
    "id": "clm2xyz123abc",
    "email": "<EMAIL>",
    "role": "USER",
    "createdAt": "2023-12-01T10:30:00Z",
    "updatedAt": "2023-12-01T10:30:00Z"
  }
}
```

#### 2. Get User by ID
```bash
# Endpoint: GET /api/v1/users/:id
# Authentication: Required
# Authorization: Self or Admin access only

curl -X GET "http://localhost:3000/api/v1/users/clm2xyz123abc" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Profile Update (PUT)

#### 1. Update Current User Profile
```bash
# Endpoint: PUT /api/v1/users/:id
# Authentication: Required
# Authorization: Self or Admin access only

curl -X PUT "http://localhost:3000/api/v1/users/clm2xyz123abc" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

**Expected Response (200 OK):**
```json
{
  "success": true,
  "message": "User updated successfully",
  "data": {
    "id": "clm2xyz123abc",
    "email": "<EMAIL>",
    "role": "USER",
    "createdAt": "2023-12-01T10:30:00Z",
    "updatedAt": "2023-12-01T15:45:00Z"
  }
}
```

#### 2. Update User Profile via Auth Endpoint
```bash
# Endpoint: PATCH /api/v1/auth/profile
# Authentication: Required

curl -X PATCH "http://localhost:3000/api/v1/auth/profile" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

## Password Change Flow

### 1. Change Password (Standard Flow)
```bash
# Endpoint: PATCH /api/v1/auth/change-password
# Authentication: Required
# Rate Limited: 5 attempts per 15 minutes

curl -X PATCH "http://localhost:3000/api/v1/auth/change-password" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "currentPassword": "CurrentPassword123!",
    "newPassword": "NewSecurePassword123!"
  }'
```

**Expected Success Response (200 OK):**
```json
{
  "success": true,
  "message": "Password changed successfully",
  "timestamp": "2023-12-01T16:30:00Z"
}
```

### 2. Password Change Error Cases

#### Invalid Current Password
```bash
curl -X PATCH "http://localhost:3000/api/v1/auth/change-password" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "currentPassword": "WrongPassword123!",
    "newPassword": "NewSecurePassword123!"
  }'
```

**Expected Error Response (400 Bad Request):**
```json
{
  "success": false,
  "error": "Current password is incorrect",
  "statusCode": 400,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

#### Weak New Password
```bash
curl -X PATCH "http://localhost:3000/api/v1/auth/change-password" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "currentPassword": "CurrentPassword123!",
    "newPassword": "weak"
  }'
```

**Expected Validation Error (400 Bad Request):**
```json
{
  "success": false,
  "error": "Validation failed",
  "message": [
    "New password must be at least 8 characters long"
  ],
  "statusCode": 400,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

#### Rate Limit Exceeded
```bash
# After 5 failed attempts within 15 minutes
curl -X PATCH "http://localhost:3000/api/v1/auth/change-password" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "currentPassword": "CurrentPassword123!",
    "newPassword": "NewSecurePassword123!"
  }'
```

**Expected Rate Limit Error (429 Too Many Requests):**
```json
{
  "success": false,
  "error": "Too many password change attempts. Please try again later.",
  "statusCode": 429,
  "retryAfter": 900,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

## Admin Operations

### 1. List Users with Pagination
```bash
# Endpoint: GET /api/v1/users
# Authentication: Required
# Authorization: Admin role + USER:READ permission

curl -X GET "http://localhost:3000/api/v1/users?page=1&limit=10" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response (200 OK):**
```json
{
  "success": true,
  "message": "Users retrieved successfully",
  "data": {
    "users": [
      {
        "id": "clm2xyz123abc",
        "email": "<EMAIL>",
        "role": "USER",
        "createdAt": "2023-12-01T10:30:00Z",
        "updatedAt": "2023-12-01T10:30:00Z"
      },
      {
        "id": "clm2xyz456def",
        "email": "<EMAIL>",
        "role": "MODERATOR",
        "createdAt": "2023-12-01T11:00:00Z",
        "updatedAt": "2023-12-01T11:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

### 2. Disable User (Update Role or Delete)
```bash
# Option 1: Update user role to restrict access
# Endpoint: PATCH /api/v1/users/:id/role

curl -X PATCH "http://localhost:3000/api/v1/users/clm2xyz123abc/role" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "DISABLED"
  }'

# Option 2: Delete user account
# Endpoint: DELETE /api/v1/users/:id

curl -X DELETE "http://localhost:3000/api/v1/users/clm2xyz123abc" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Delete User Success Response (200 OK):**
```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

### 3. Admin Reset User Password
```bash
# Endpoint: PATCH /api/v1/auth/users/:userId
# Authentication: Required
# Authorization: Admin role + USER:UPDATE permission

curl -X PATCH "http://localhost:3000/api/v1/auth/users/clm2xyz123abc" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "password": "AdminResetPassword123!"
  }'
```

### 4. Get User Statistics
```bash
# Endpoint: GET /api/v1/users/admin/stats
# Authentication: Required
# Authorization: Admin role + USER:READ permission

curl -X GET "http://localhost:3000/api/v1/users/admin/stats" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Response (200 OK):**
```json
{
  "success": true,
  "data": {
    "totalUsers": 150,
    "activeUsers": 142,
    "usersByRole": {
      "USER": 120,
      "MODERATOR": 20,
      "ADMIN": 10
    },
    "recentRegistrations": 25
  }
}
```

## Pagination and Filtering

### 1. Pagination Parameters
```bash
# Basic pagination
curl -X GET "http://localhost:3000/api/v1/users?page=2&limit=5" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"

# Large page size
curl -X GET "http://localhost:3000/api/v1/users?page=1&limit=50" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

### 2. Filtering Parameters
```bash
# Filter by email
curl -X GET "http://localhost:3000/api/v1/users?email=<EMAIL>" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"

# Filter by role
curl -X GET "http://localhost:3000/api/v1/users?role=ADMIN" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"

# Filter by creation date range
curl -X GET "http://localhost:3000/api/v1/users?createdAfter=2023-01-01&createdBefore=2023-12-31" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"

# Combined filters with pagination
curl -X GET "http://localhost:3000/api/v1/users?role=USER&page=1&limit=20&createdAfter=2023-06-01" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

### 3. Search Users
```bash
# Endpoint: GET /api/v1/users/search
# Search by email or other user attributes

curl -X GET "http://localhost:3000/api/v1/users/search?q=john&page=1&limit=10" \
  -H "Authorization: Bearer USER_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 4. Get Users by Role
```bash
# Endpoint: GET /api/v1/users/role/:role
curl -X GET "http://localhost:3000/api/v1/users/role/ADMIN" \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## Expected JSON Shapes

### User Object
```json
{
  "id": "clm2xyz123abc",
  "email": "<EMAIL>",
  "role": "USER",
  "createdAt": "2023-12-01T10:30:00Z",
  "updatedAt": "2023-12-01T10:30:00Z"
}
```

### Authentication Response
```json
{
  "success": true,
  "message": "Authentication successful",
  "data": {
    "user": {
      "id": "clm2xyz123abc",
      "email": "<EMAIL>",
      "role": "USER",
      "createdAt": "2023-12-01T10:30:00Z",
      "updatedAt": "2023-12-01T10:30:00Z"
    },
    "expiresIn": "7d",
    "tokenType": "Bearer"
  },
  "note": "Tokens are set as secure HTTP-only cookies"
}
```

### Paginated Users Response
```json
{
  "success": true,
  "message": "Users retrieved successfully",
  "data": {
    "users": [
      {
        "id": "clm2xyz123abc",
        "email": "<EMAIL>",
        "role": "USER",
        "createdAt": "2023-12-01T10:30:00Z",
        "updatedAt": "2023-12-01T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "totalPages": 5
    }
  }
}
```

## Validation Error Payloads

### 1. Missing Required Fields
```json
{
  "success": false,
  "error": "Validation failed",
  "message": [
    "email must be a valid email",
    "password is required"
  ],
  "statusCode": 400,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

### 2. Invalid Email Format
```json
{
  "success": false,
  "error": "Validation failed",
  "message": [
    "email must be a valid email"
  ],
  "statusCode": 400,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

### 3. Password Strength Validation
```json
{
  "success": false,
  "error": "Validation failed",
  "message": [
    "Password must be at least 8 characters long"
  ],
  "statusCode": 400,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

### 4. Invalid Role Enum
```json
{
  "success": false,
  "error": "Validation failed",
  "message": [
    "role must be one of the following values: USER, ADMIN, MODERATOR"
  ],
  "statusCode": 400,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

### 5. Authorization Error
```json
{
  "success": false,
  "error": "Access denied: You can only update your own profile",
  "statusCode": 403,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

### 6. Authentication Error
```json
{
  "success": false,
  "error": "User not authenticated",
  "statusCode": 401,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

### 7. Resource Not Found
```json
{
  "success": false,
  "error": "User not found",
  "statusCode": 404,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

### 8. Duplicate Email Error
```json
{
  "success": false,
  "error": "User with this email already exists",
  "statusCode": 409,
  "timestamp": "2023-12-01T16:30:00Z"
}
```

## Testing Scenarios

### Profile Operations Test Cases

1. **Successful Profile Fetch**
   - Verify authenticated user can fetch their own profile
   - Verify admin can fetch any user's profile
   - Confirm sensitive data (passwordHash) is excluded

2. **Profile Update Permissions**
   - User can update their own profile
   - User cannot update other user's profiles
   - Admin can update any user's profile
   - Verify only allowed fields are updated

3. **Profile Update Validation**
   - Email format validation
   - Email uniqueness validation
   - Role enum validation (if allowed)

### Password Change Test Cases

1. **Successful Password Change**
   - Valid current password + strong new password
   - Verify old password no longer works
   - Verify new password works for login

2. **Password Change Failures**
   - Invalid current password
   - Weak new password
   - Same password as current
   - Rate limiting after multiple failures

3. **Password Change Security**
   - Rate limiting enforcement
   - Password strength validation
   - Audit logging of password changes

### Admin Operations Test Cases

1. **User Listing**
   - Pagination functionality
   - Filtering by various criteria
   - Search functionality
   - Performance with large datasets

2. **User Management**
   - Update user roles
   - Disable/enable users
   - Delete users
   - Reset passwords

3. **Permission Validation**
   - Only admins can access admin endpoints
   - RBAC permission checks
   - Resource access validation

### Error Handling Test Cases

1. **Validation Errors**
   - Missing required fields
   - Invalid data formats
   - Enum validation
   - Custom validation rules

2. **Authentication/Authorization Errors**
   - Missing tokens
   - Invalid tokens
   - Expired tokens
   - Insufficient permissions

3. **Rate Limiting**
   - Various rate limit scenarios
   - Proper error messages
   - Retry-After headers

### Performance Test Cases

1. **Pagination Performance**
   - Large page sizes
   - Deep pagination
   - Complex filters

2. **Search Performance**
   - Search with various query lengths
   - Search result pagination

3. **Concurrent Operations**
   - Multiple simultaneous requests
   - Race condition handling

## Environment Configuration

### Test Environment Setup
```bash
# Set environment variables for testing
export NODE_ENV=test
export JWT_SECRET=test_jwt_secret
export DATABASE_URL="postgresql://test:test@localhost:5432/testdb"
export RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
export RATE_LIMIT_MAX_REQUESTS=5
```

### Test Data Setup
```sql
-- Create test users
INSERT INTO "User" (id, email, "passwordHash", role, "createdAt", "updatedAt") VALUES
('test_user_1', '<EMAIL>', '$2b$10$hashed_password', 'USER', NOW(), NOW()),
('test_admin_1', '<EMAIL>', '$2b$10$hashed_password', 'ADMIN', NOW(), NOW()),
('test_mod_1', '<EMAIL>', '$2b$10$hashed_password', 'MODERATOR', NOW(), NOW());
```

### Authentication Setup for Tests
```bash
# Login and extract token for subsequent requests
TOKEN=$(curl -s -X POST "http://localhost:3000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test_password"}' \
  | jq -r '.data.token')

# Use token in subsequent requests
curl -H "Authorization: Bearer $TOKEN" ...
```
