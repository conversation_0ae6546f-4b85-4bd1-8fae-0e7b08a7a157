{"name": "secure-backend-api", "version": "1.0.0", "description": "Production-ready Express.js backend with JWT authentication, RBAC, and layered architecture", "author": "", "license": "MIT", "scripts": {"start": "npm run build && dotenv -e .env.production.local -- node dist/server.js", "start:dev": "dotenv -e .env.development.local -- node dist/server.js", "dev": "dotenv -e .env.development.local -- nodemon", "dev:default": "dotenv -- nodemon", "build": "swc src -d dist --source-maps --copy-files --strip-leading-paths", "build:tsc": "tsc && tsc-alias", "test": "dotenv -e .env.test.local -- jest --forceExit --detectO<PERSON>Handles", "test:dev": "dotenv -e .env.development.local -- jest --forceExit --detectO<PERSON>Handles", "test:coverage": "dotenv -e .env.test.local -- jest --coverage", "test:security": "dotenv -e .env.test.local -- jest --testPathPatterns='(csrf|tokenBlacklist|inputSanitization|keyRotation|sessionVersioning|redisRateLimit)' --forceExit --detectOpenHandles", "test:security:coverage": "dotenv -e .env.test.local -- jest --testPathPatterns='(csrf|tokenBlacklist|inputSanitization|keyRotation|sessionVersioning|redisRateLimit)' --coverage --forceExit --detectOpenHandles", "test:watch": "dotenv -e .env.test.local -- jest --watch", "testAll": "dotenv -e .env.test.local -- jest --coverage", "lint": "eslint --ext .ts src/", "lint:fix": "eslint --ext .ts src/ --fix", "db:migrate": "dotenv -- prisma migrate dev", "db:migrate:dev": "dotenv -e .env.development.local -- prisma migrate dev", "db:migrate:test": "dotenv -e .env.test.local -- prisma migrate dev", "db:migrate:prod": "dotenv -e .env.production.local -- prisma migrate deploy", "db:generate": "dotenv -- prisma generate", "db:studio": "dotenv -- prisma studio", "db:reset": "dotenv -- prisma migrate reset", "db:reset:dev": "dotenv -e .env.development.local -- prisma migrate reset", "db:seed": "dotenv -- ts-node src/prisma/seed.ts", "migrate:verify-existing-emails": "dotenv -- ts-node src/scripts/verify-existing-emails.ts", "deploy:prod": "npm run build && pm2 start ecosystem.config.js --only prod", "deploy:dev": "npm run build && pm2 start ecosystem.config.js --only dev", "pm2:reload:prod": "pm2 reload ecosystem.config.js --only prod", "pm2:reload:dev": "pm2 reload ecosystem.config.js --only dev", "pm2:stop:prod": "pm2 stop ecosystem.config.js --only prod", "pm2:stop:dev": "pm2 stop ecosystem.config.js --only dev", "pm2:restart:prod": "pm2 restart ecosystem.config.js --only prod", "pm2:restart:dev": "pm2 restart ecosystem.config.js --only dev", "pm2:logs:prod": "pm2 logs prod", "pm2:logs:dev": "pm2 logs dev", "pm2:monitor": "pm2 monit", "pm2:status": "pm2 status", "pm2:flush": "pm2 flush", "prepare": "husky"}, "prisma": {"schema": "src/prisma/schema.prisma"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@prisma/client": "^6.11.1", "@types/express-session": "^1.18.2", "@types/passport": "^1.0.17", "@types/passport-facebook": "^3.0.3", "@types/passport-github2": "^1.2.9", "@types/passport-google-oauth20": "^2.0.16", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/uuid": "^10.0.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csrf": "^3.1.0", "cuid": "^3.0.0", "dompurify": "^3.2.6", "dotenv": "^17.0.1", "envalid": "^8.0.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "ioredis": "^5.7.0", "isomorphic-dompurify": "^2.26.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "node-mailjet": "^6.0.8", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "qrcode": "^1.5.4", "rate-limiter-flexible": "^7.2.0", "reflect-metadata": "^0.2.2", "sharp": "^0.34.3", "speakeasy": "^2.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "typedi": "^0.10.0", "uuid": "^11.1.0", "validator": "^13.15.15", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@swc/cli": "^0.7.7", "@swc/core": "^1.13.3", "@types/bcrypt": "^6.0.0", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/express-rate-limit": "^5.1.3", "@types/hpp": "^0.2.6", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.1.0", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "cross-env": "^10.0.0", "dotenv-cli": "^10.0.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "globals": "^16.3.0", "husky": "^9.1.7", "jest": "^30.0.4", "jest-circus": "^30.0.4", "jest-environment-jsdom": "^30.0.5", "lint-staged": "^16.1.2", "node-config": "^0.0.2", "node-gyp": "^11.3.0", "nodemailer": "^7.0.5", "nodemon": "^3.1.10", "pm2": "^6.0.8", "prettier": "^3.6.2", "prisma": "^6.11.1", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.9.2"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write"]}}