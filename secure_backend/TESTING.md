# Testing Guide

This project uses <PERSON><PERSON> with TypeScript for comprehensive testing of the backend API.

## Test Configuration

The project is configured with:

- **Testing Framework**: Jest with ts-jest preset for TypeScript support
- **Environment Variables**: Automatically loaded from `.env.test.local` and `.env.test`
- **Test Environment**: Node.js with test-specific configurations
- **Coverage**: Configured with 70% threshold for branches, functions, lines, and statements

## Configuration Files

### `jest.config.ts`
- TypeScript configuration for Jest
- ts-jest preset for TypeScript compilation
- Path alias support (`@/` mapping to `src/`)
- Coverage collection and thresholds
- Test environment setup

### `.env.test.local`
Test-specific environment variables:
- Lower BCRYPT_SALT_ROUNDS for faster tests
- Test database configuration
- Relaxed rate limiting for tests
- Minimal logging to reduce test noise

### `src/tests/setup.ts`
Global test setup file that:
- Loads test environment variables
- Sets up test database connections
- Configures global test utilities
- Suppresses console output during tests

## Running Tests

### Run All Tests
```bash
npm test
```

### Run Specific Test File
```bash
npm test -- src/tests/routes/health.test.ts
```

### Run Tests with Coverage
```bash
npm run testAll
```

### Run Tests in Development Mode
```bash
npm run test:dev
```

## Available Test Scripts

- `npm test` - Run tests with test environment variables
- `npm run test:dev` - Run tests with development environment variables
- `npm run testAll` - Run tests with coverage report

## Test Structure

### Health Check Tests (`src/tests/routes/health.test.ts`)
Comprehensive tests for the `/health` endpoint:
- Basic functionality testing
- Response format validation
- Timestamp format verification
- Performance testing
- Concurrent request handling
- Header validation

### Example Test Structure
```typescript
describe('Feature Name', () => {
  describe('Specific Functionality', () => {
    it('should perform expected behavior', async () => {
      // Test implementation
    });
  });
});
```

## Test Environment Setup

### Environment Variables
The test environment automatically sets:
- `NODE_ENV=test`
- `PORT=3001`
- `BCRYPT_SALT_ROUNDS=4` (faster hashing)
- `LOG_LEVEL=error` (reduced logging)
- `RATE_LIMIT_MAX=1000` (relaxed limits)

### Database Configuration
Tests use a separate test database:
- Database URL: `postgresql://testuser:testpass@localhost:5432/testdb`
- Isolated from development/production data
- Can be reset between test runs

## Coverage Configuration

The project enforces minimum coverage thresholds:
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

Files excluded from coverage:
- Type definition files (`*.d.ts`)
- Server entry point (`src/server.ts`)
- Type-only files (`src/types/**/*.ts`)
- Interface files (`src/**/*.interface.ts`)

## CI/CD Integration

### Requirements for CI
1. Set up test database
2. Install dependencies: `npm ci`
3. Run tests: `npm test`
4. Tests must pass for deployment

### Environment Setup for CI
Create `.env.test.local` with:
```env
NODE_ENV=test
DATABASE_URL=postgresql://testuser:testpass@localhost:5432/testdb
JWT_SECRET=test-jwt-secret-key-for-testing-purposes-only
BCRYPT_SALT_ROUNDS=4
LOG_LEVEL=error
```

### GitHub Actions Example
```yaml
- name: Run Tests
  run: |
    npm ci
    npm test
  env:
    NODE_ENV: test
    DATABASE_URL: postgresql://testuser:testpass@localhost:5432/testdb
```

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Descriptive Names**: Use clear, descriptive test names
3. **Setup/Teardown**: Use beforeAll/afterAll for setup/cleanup
4. **Mock External Dependencies**: Mock database calls, external APIs
5. **Environment Variables**: Use test-specific configurations
6. **Performance Tests**: Include performance checks for critical endpoints
7. **Error Testing**: Test both success and error scenarios

## Debugging Tests

### Enable Debug Logging
Set `LOG_LEVEL=debug` in test environment to see detailed logs.

### Run Single Test in Watch Mode
```bash
npx jest --watch src/tests/routes/health.test.ts
```

### Debug with Node Inspector
```bash
node --inspect-brk node_modules/.bin/jest --runInBand --no-cache src/tests/routes/health.test.ts
```

## Common Issues

### TypeScript Compilation Errors
- Ensure all imports use correct paths
- Check that all required dependencies are installed
- Verify tsconfig.json configuration

### Environment Variable Issues
- Check `.env.test.local` exists and is properly formatted
- Verify test setup file loads environment variables
- Ensure test database is accessible

### Timeout Issues
- Increase test timeout in jest.config.ts if needed
- Check for hanging promises or unclosed connections
- Use `--detectOpenHandles` flag to identify issues

## Adding New Tests

1. Create test files with `.test.ts` or `.spec.ts` extension
2. Place in `src/tests/` directory structure
3. Import required modules and test utilities
4. Follow existing test patterns
5. Add both positive and negative test cases
6. Include performance and edge case tests
