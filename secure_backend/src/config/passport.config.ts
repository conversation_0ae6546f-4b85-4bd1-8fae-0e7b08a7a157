import passport from 'passport';
import { Strategy as GoogleStrategy, Profile, VerifyCallback } from 'passport-google-oauth20';
import { Strategy as FacebookStrategy } from 'passport-facebook';
import { Strategy as GitHubStrategy } from 'passport-github2';
import { OAuthConfig, OAuthProvider } from './oauth.config';
import { OAuthProfile } from '../services/oauth.service';
import { logger } from '../utils/secureLogger';
import { sanitizeError } from '../utils/logSanitizer';
import { Request, Response, NextFunction } from 'express';

// Type definitions for Passport OAuth profiles
interface PassportUser {
  id: string;
  [key: string]: unknown;
}

interface PassportDoneFunction {
  (error: Error | null, user?: PassportUser | false, info?: unknown): void;
}

interface PassportOAuthProfile {
  id: string;
  displayName?: string;
  emails?: Array<{ value: string; verified?: boolean }>;
  photos?: Array<{ value: string }>;
  provider: string;
  _raw: string;
  _json: Record<string, unknown>;
}

interface PassportAuthenticateOptions {
  session?: boolean;
  scope?: string[];
  state?: string;
  [key: string]: unknown;
}

/**
 * Passport.js Configuration for OAuth Providers
 * Integrates with existing security infrastructure
 */
export class PassportConfig {
  /**
   * Initialize Passport.js with OAuth strategies
   */
  static initialize(): void {
    try {
      // Configure serialization (not used in stateless JWT setup, but required by Passport)
      passport.serializeUser((user: any, done) => {
        done(null, user.id);
      });

      passport.deserializeUser((id: string, done) => {
        // Not used in our JWT-based setup, but required by Passport
        done(null, { id } as any);
      });

      // Initialize enabled OAuth strategies
      const enabledProviders = OAuthConfig.getEnabledProviders();

      enabledProviders.forEach(provider => {
        this.initializeStrategy(provider);
      });

      logger.info('Passport OAuth strategies initialized', {
        enabledProviders,
        totalStrategies: enabledProviders.length,
      });
    } catch (error) {
      logger.error('Failed to initialize Passport OAuth strategies', {
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Initialize specific OAuth strategy
   */
  private static initializeStrategy(provider: OAuthProvider): void {
    switch (provider) {
      case 'google':
        this.initializeGoogleStrategy();
        break;
      case 'facebook':
        this.initializeFacebookStrategy();
        break;
      case 'github':
        this.initializeGitHubStrategy();
        break;
      default:
        logger.warn(`Unsupported OAuth provider: ${provider}`);
    }
  }

  /**
   * Initialize Google OAuth strategy
   */
  private static initializeGoogleStrategy(): void {
    const config = OAuthConfig.getGoogleConfig();

    passport.use(
      new GoogleStrategy(
        {
          clientID: config.clientID,
          clientSecret: config.clientSecret,
          callbackURL: config.callbackURL,
          scope: config.scope,
          passReqToCallback: true,
        } as any,
        async (_req: Request, accessToken: string, refreshToken: string, profile: Profile, done: VerifyCallback) => {
          try {
            const oauthProfile: OAuthProfile = {
              id: profile.id,
              provider: 'google',
              email: profile.emails?.[0]?.value,
              name: profile.displayName,
              picture: profile.photos?.[0]?.value,
              verified: profile.emails?.[0]?.verified || false,
            };

            // Store tokens and profile for later use in callback
            const result = {
              id: oauthProfile.id,
              profile: oauthProfile,
              accessToken,
              refreshToken,
            };

            done(null, result as any);
          } catch (error) {
            logger.error('Google OAuth strategy error', {
              profileId: profile.id,
              error: sanitizeError(error),
            });
            done(error as Error, false);
          }
        },
      ),
    );
  }

  /**
   * Initialize Facebook OAuth strategy
   */
  private static initializeFacebookStrategy(): void {
    const config = OAuthConfig.getFacebookConfig();

    passport.use(
      new FacebookStrategy(
        {
          clientID: config.clientID,
          clientSecret: config.clientSecret,
          callbackURL: config.callbackURL,
          profileFields: config.profileFields || ['id', 'emails', 'name', 'picture.type(large)'],
          passReqToCallback: true,
        } as any,
        async (
          _req: Request,
          accessToken: string,
          refreshToken: string,
          profile: PassportOAuthProfile,
          done: PassportDoneFunction,
        ) => {
          try {
            const oauthProfile: OAuthProfile = {
              id: profile.id,
              provider: 'facebook',
              email: profile.emails?.[0]?.value,
              name:
                profile.displayName ||
                `${(profile as any).name?.givenName} ${(profile as any).name?.familyName}`.trim(),
              picture: profile.photos?.[0]?.value,
              verified: true, // Facebook emails are generally verified
            };

            const result = {
              id: oauthProfile.id,
              profile: oauthProfile,
              accessToken,
              refreshToken,
            };

            done(null, result as any);
          } catch (error) {
            logger.error('Facebook OAuth strategy error', {
              profileId: profile.id,
              error: sanitizeError(error),
            });
            done(error as Error, false);
          }
        },
      ),
    );
  }

  /**
   * Initialize GitHub OAuth strategy
   */
  private static initializeGitHubStrategy(): void {
    const config = OAuthConfig.getGitHubConfig();

    passport.use(
      new GitHubStrategy(
        {
          clientID: config.clientID,
          clientSecret: config.clientSecret,
          callbackURL: config.callbackURL,
          scope: config.scope,
          passReqToCallback: true,
        } as any,
        async (
          _req: Request,
          accessToken: string,
          refreshToken: string,
          profile: PassportOAuthProfile,
          done: PassportDoneFunction,
        ) => {
          try {
            const oauthProfile: OAuthProfile = {
              id: profile.id,
              provider: 'github',
              email: profile.emails?.[0]?.value,
              name: profile.displayName || (profile as any).username,
              username: (profile as any).username,
              picture: profile.photos?.[0]?.value,
              verified: profile.emails?.[0]?.verified || false,
            };

            const result = {
              id: oauthProfile.id,
              profile: oauthProfile,
              accessToken,
              refreshToken,
            };

            done(null, result as any);
          } catch (error) {
            logger.error('GitHub OAuth strategy error', {
              profileId: profile.id,
              error: sanitizeError(error),
            });
            done(error as Error, false);
          }
        },
      ),
    );
  }

  /**
   * Get Passport middleware for authentication
   */
  static getAuthenticateMiddleware(provider: OAuthProvider, options: PassportAuthenticateOptions = {}) {
    const defaultOptions = {
      session: false, // We use JWT, not sessions
      ...options,
    };

    return passport.authenticate(provider, defaultOptions);
  }

  /**
   * Validate OAuth configuration on startup
   */
  static validateConfiguration(): { valid: boolean; errors: string[] } {
    const oauthValidation = OAuthConfig.validateConfiguration();
    const errors: string[] = [...oauthValidation.errors];

    // Additional Passport-specific validations can be added here

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

/**
 * OAuth State Middleware
 * Handles state parameter generation and validation for CSRF protection
 */
export class OAuthStateMiddleware {
  /**
   * Generate and attach state parameter to OAuth request
   */
  static generateState(req: Request, _res: Response, next: NextFunction): void {
    try {
      // Generate secure state parameter
      const state = this.createSecureState(req);

      // Store state in session for validation
      if (req.session) {
        req.session.oauthState = state;
      }

      // Attach state to request for use in OAuth URL
      (req as any).oauthState = state;

      next();
    } catch (error) {
      logger.error('Failed to generate OAuth state', {
        error: sanitizeError(error),
      });
      next(error);
    }
  }

  /**
   * Validate state parameter in OAuth callback
   */
  static validateState(req: Request, res: Response, next: NextFunction): void {
    try {
      const receivedState = req.query['state'];
      const sessionState = req.session?.oauthState;

      if (!receivedState || !sessionState) {
        logger.warn('OAuth callback missing state parameter', {
          hasReceivedState: !!receivedState,
          hasSessionState: !!sessionState,
          ip: req.ip,
        });
        return res.redirect(OAuthConfig.getRedirectUrls().failure + '&error=invalid_state');
      }

      if (receivedState !== sessionState) {
        logger.warn('OAuth state parameter mismatch', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        });
        return res.redirect(OAuthConfig.getRedirectUrls().failure + '&error=state_mismatch');
      }

      // Clear state from session after validation
      delete req.session.oauthState;

      next();
    } catch (error) {
      logger.error('OAuth state validation failed', {
        error: sanitizeError(error),
      });
      res.redirect(OAuthConfig.getRedirectUrls().failure + '&error=state_validation_failed');
    }
  }

  /**
   * Create secure state parameter
   */
  private static createSecureState(req: Request): string {
    const timestamp = Date.now().toString();
    const randomBytes = Math.random().toString(36).substring(2, 15);
    const ipHash = req.ip ? req.ip.split('.').join('') : 'unknown';
    return `${timestamp}_${randomBytes}_${ipHash}`;
  }
}

export default PassportConfig;
