import { Service } from 'typedi';
import { PrismaClient } from '@prisma/client';
import { 
  UniversityCourse, 
  UniversityCourseModule,
  UniversityCourseProgress,
  CreateUniversityCourseRequest,
  GenerateUniversityCourseRequest,
  UniversityCourseResponse,
  UniversityCoursesListResponse,
  GenerateUniversityCourseResponse,
  CourseAnalysis
} from '@/interfaces/learning.interface';
import { AIService } from './ai.service';
import { HttpException } from '@/exceptions/HttpException';

@Service()
export class UniversityCoursesService {
  private prisma = new PrismaClient();

  constructor(private aiService: AIService) {}

  async generateUniversityCourse(userId: string, requestData: GenerateUniversityCourseRequest): Promise<GenerateUniversityCourseResponse> {
    try {
      console.log(`Analyzing course sketch for user ${userId}...`);
      console.log(`User sketch length: ${requestData.userSketch.length} characters`);

      // Step 1: Analyze the user sketch to create course structure
      const courseAnalysis = await this.aiService.analyzeCourseSketch(
        requestData.userSketch, 
        requestData.language || 'English'
      );

      console.log(`Generated course: ${courseAnalysis.courseInfo.title}`);
      console.log(`Modules to generate: ${courseAnalysis.modules.length}`);

      // Step 2: Generate detailed curriculum for each module
      for (let i = 0; i < courseAnalysis.modules.length; i++) {
        const module = courseAnalysis.modules[i];
        console.log(`Generating curriculum for module: ${module.title}`);

        try {
          const moduleCurriculum = await this.aiService.generateModuleCurriculum(
            module.title,
            module.description,
            module.learningObjectives,
            courseAnalysis.courseInfo.title,
            requestData.language || 'English'
          );

          courseAnalysis.modules[i].curriculum = moduleCurriculum.curriculum || [];
        } catch (curriculumError) {
          console.error(`Failed to generate curriculum for module ${module.title}:`, curriculumError);
          // Provide fallback empty curriculum
          courseAnalysis.modules[i].curriculum = [];
        }
      }

      console.log('Course generation complete!');

      return {
        course: courseAnalysis
      };
    } catch (error) {
      console.error('Error generating university course:', error);
      throw new HttpException(500, 'Failed to generate university course. Please try again.');
    }
  }

  async createUniversityCourse(userId: string, courseData: CreateUniversityCourseRequest): Promise<UniversityCourseResponse> {
    try {
      // Create the course with modules in a transaction
      const result = await this.prisma.$transaction(async (prisma) => {
        // Create the main course
        const course = await prisma.universityCourse.create({
          data: {
            title: courseData.title,
            description: courseData.description,
            duration: courseData.duration,
            difficulty: courseData.difficulty,
            prerequisites: courseData.prerequisites || [],
            learningOutcomes: courseData.learningOutcomes,
            userSketch: courseData.userSketch,
            language: courseData.language || 'English',
            userId,
          },
        });

        // Create the modules
        const modules = await Promise.all(
          courseData.modules.map((moduleData, index) =>
            prisma.universityCourseModule.create({
              data: {
                courseId: course.id,
                title: moduleData.title,
                description: moduleData.description,
                duration: moduleData.duration,
                moduleOrder: moduleData.order || index + 1,
                prerequisites: moduleData.prerequisites || [],
                learningObjectives: moduleData.learningObjectives,
                curriculumStructure: moduleData.curriculum,
              },
            })
          )
        );

        return { course, modules };
      });

      return {
        ...result.course,
        modules: result.modules
      };
    } catch (error) {
      console.error('Error creating university course:', error);
      throw new HttpException(500, 'Failed to create university course');
    }
  }

  async getUniversityCourses(
    userId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
    difficulty?: string,
    language?: string
  ): Promise<UniversityCoursesListResponse> {
    try {
      const skip = (page - 1) * limit;
      
      const where: any = { userId };
      
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ];
      }
      
      if (difficulty) {
        where.difficulty = difficulty;
      }
      
      if (language) {
        where.language = language;
      }

      const [courses, total] = await Promise.all([
        this.prisma.universityCourse.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            modules: {
              orderBy: { moduleOrder: 'asc' }
            },
            progress: {
              where: { userId },
              orderBy: { lastAccessed: 'desc' }
            }
          }
        }),
        this.prisma.universityCourse.count({ where })
      ]);

      return {
        courses: courses.map(course => ({
          ...course,
          modules: course.modules,
          progress: course.progress
        })),
        total
      };
    } catch (error) {
      console.error('Error fetching university courses:', error);
      throw new HttpException(500, 'Failed to fetch university courses');
    }
  }

  async getUniversityCourseById(userId: string, courseId: string): Promise<UniversityCourseResponse> {
    try {
      const course = await this.prisma.universityCourse.findFirst({
        where: { 
          id: courseId,
          userId 
        },
        include: {
          modules: {
            orderBy: { moduleOrder: 'asc' }
          },
          progress: {
            where: { userId },
            orderBy: { lastAccessed: 'desc' }
          }
        }
      });

      if (!course) {
        throw new HttpException(404, 'University course not found');
      }

      return {
        ...course,
        modules: course.modules,
        progress: course.progress
      };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      console.error('Error fetching university course:', error);
      throw new HttpException(500, 'Failed to fetch university course');
    }
  }

  async updateUniversityCourse(userId: string, courseId: string, updateData: Partial<UniversityCourse>): Promise<UniversityCourse> {
    try {
      // Verify ownership
      const existingCourse = await this.prisma.universityCourse.findFirst({
        where: { id: courseId, userId }
      });

      if (!existingCourse) {
        throw new HttpException(404, 'University course not found');
      }

      const course = await this.prisma.universityCourse.update({
        where: { id: courseId },
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      });

      return course;
    } catch (error) {
      if (error instanceof HttpException) throw error;
      console.error('Error updating university course:', error);
      throw new HttpException(500, 'Failed to update university course');
    }
  }

  async deleteUniversityCourse(userId: string, courseId: string): Promise<void> {
    try {
      // Verify ownership
      const existingCourse = await this.prisma.universityCourse.findFirst({
        where: { id: courseId, userId }
      });

      if (!existingCourse) {
        throw new HttpException(404, 'University course not found');
      }

      await this.prisma.universityCourse.delete({
        where: { id: courseId }
      });
    } catch (error) {
      if (error instanceof HttpException) throw error;
      console.error('Error deleting university course:', error);
      throw new HttpException(500, 'Failed to delete university course');
    }
  }

  async updateProgress(userId: string, courseId: string, moduleId: string, topicId: string, completed: boolean): Promise<UniversityCourseProgress> {
    try {
      // Verify course ownership
      const existingCourse = await this.prisma.universityCourse.findFirst({
        where: { id: courseId, userId }
      });

      if (!existingCourse) {
        throw new HttpException(404, 'University course not found');
      }

      // Upsert progress record
      const progress = await this.prisma.universityCourseProgress.upsert({
        where: {
          courseId_userId_moduleId_topicId: {
            courseId,
            userId,
            moduleId,
            topicId
          }
        },
        update: {
          completed,
          lastAccessed: new Date(),
          updatedAt: new Date()
        },
        create: {
          courseId,
          userId,
          moduleId,
          topicId,
          completed,
          lastAccessed: new Date()
        }
      });

      return progress;
    } catch (error) {
      console.error('Error updating university course progress:', error);
      throw new HttpException(500, 'Failed to update progress');
    }
  }

  async getCourseStats(userId: string, courseId: string): Promise<{
    totalTopics: number;
    completedTopics: number;
    completionPercentage: number;
    moduleStats: Array<{
      moduleId: string;
      moduleTitle: string;
      totalTopics: number;
      completedTopics: number;
      completionPercentage: number;
    }>;
    lastAccessed?: Date;
  }> {
    try {
      // Verify course ownership and get course with modules
      const course = await this.prisma.universityCourse.findFirst({
        where: { id: courseId, userId },
        include: {
          modules: {
            orderBy: { moduleOrder: 'asc' }
          }
        }
      });

      if (!course) {
        throw new HttpException(404, 'University course not found');
      }

      const progress = await this.prisma.universityCourseProgress.findMany({
        where: { courseId, userId }
      });

      const completedTopics = progress.filter(p => p.completed).length;
      const totalTopics = progress.length;
      const completionPercentage = totalTopics > 0 ? (completedTopics / totalTopics) * 100 : 0;

      // Calculate module-specific stats
      const moduleStats = course.modules.map(module => {
        const moduleProgress = progress.filter(p => p.moduleId === module.id);
        const moduleCompleted = moduleProgress.filter(p => p.completed).length;
        const moduleTotal = moduleProgress.length;
        const modulePercentage = moduleTotal > 0 ? (moduleCompleted / moduleTotal) * 100 : 0;

        return {
          moduleId: module.id,
          moduleTitle: module.title,
          totalTopics: moduleTotal,
          completedTopics: moduleCompleted,
          completionPercentage: Math.round(modulePercentage * 100) / 100
        };
      });

      const lastAccessed = progress.length > 0 ? 
        progress.reduce((latest, current) => 
          current.lastAccessed > latest ? current.lastAccessed : latest, 
          progress[0].lastAccessed
        ) : undefined;

      return {
        totalTopics,
        completedTopics,
        completionPercentage: Math.round(completionPercentage * 100) / 100,
        moduleStats,
        lastAccessed
      };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      console.error('Error fetching university course stats:', error);
      throw new HttpException(500, 'Failed to fetch course statistics');
    }
  }
}
