import { UserSession, SessionActivity } from '@prisma/client';
import { prisma } from '../loaders/prisma';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';
import { sanitizeError, sanitizeAuthEvent } from '../utils/logSanitizer';
import { AuthEvent } from '../types/template';
import * as crypto from 'crypto';
import { Request } from 'express';

export interface SessionContext {
  ip?: string;
  userAgent?: string;
  deviceFingerprint?: string;
  location?: string;
}

export interface DeviceInfo {
  deviceId: string;
  deviceName?: string;
  deviceType?: string;
  userAgent: string;
  fingerprint: string;
}

export interface SessionInfo {
  id: string;
  deviceId: string;
  deviceName?: string;
  deviceType?: string;
  userAgent: string;
  ipAddress: string;
  location?: string;
  isActive: boolean;
  isTrusted: boolean;
  lastActivity: Date;
  createdAt: Date;
  expiresAt: Date;
  riskScore: number;
  suspiciousActivity: boolean;
}

export interface SessionListResponse {
  sessions: SessionInfo[];
  totalSessions: number;
  activeSessions: number;
  trustedDevices: number;
}

/**
 * Enhanced Session Management Service
 * Provides comprehensive session tracking, device management, and security monitoring
 */
export class SessionManagementService {
  private static readonly SESSION_EXPIRY_HOURS = 24 * 7; // 7 days
  private static readonly MAX_SESSIONS_PER_USER = 10;
  private static readonly SUSPICIOUS_RISK_THRESHOLD = 0.7;

  /**
   * Generate device fingerprint from request
   */
  private static generateDeviceFingerprint(userAgent: string, additionalData?: string): string {
    const data = `${userAgent}${additionalData || ''}`;
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  /**
   * Generate unique device ID
   */
  private static generateDeviceId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Extract device information from user agent
   */
  private static parseDeviceInfo(userAgent: string): { deviceType?: string; deviceName?: string } {
    const ua = userAgent.toLowerCase();

    let deviceType: string | undefined;
    let deviceName: string | undefined;

    // Device type detection
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      deviceType = 'mobile';
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      deviceType = 'tablet';
    } else {
      deviceType = 'desktop';
    }

    // Device name detection
    if (ua.includes('chrome')) {
      deviceName = 'Chrome Browser';
    } else if (ua.includes('firefox')) {
      deviceName = 'Firefox Browser';
    } else if (ua.includes('safari') && !ua.includes('chrome')) {
      deviceName = 'Safari Browser';
    } else if (ua.includes('edge')) {
      deviceName = 'Edge Browser';
    } else {
      deviceName = 'Unknown Browser';
    }

    return { deviceType, deviceName };
  }

  /**
   * Calculate risk score based on various factors
   */
  private static calculateRiskScore(
    ipAddress: string,
    userAgent: string,
    location?: string,
    isNewDevice: boolean = false,
    previousSessions: UserSession[] = [],
  ): number {
    let riskScore = 0.0;

    // New device increases risk
    if (isNewDevice) {
      riskScore += 0.3;
    }

    // Check for IP address changes
    const recentIPs = previousSessions
      .filter(s => s.createdAt > new Date(Date.now() - 24 * 60 * 60 * 1000))
      .map(s => s.ipAddress);

    if (recentIPs.length > 0 && !recentIPs.includes(ipAddress)) {
      riskScore += 0.2;
    }

    // Check for unusual user agent
    const recentUserAgents = previousSessions
      .filter(s => s.createdAt > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
      .map(s => s.userAgent);

    if (recentUserAgents.length > 0 && !recentUserAgents.includes(userAgent)) {
      riskScore += 0.1;
    }

    // Location-based risk (if available)
    if (location) {
      const recentLocations = previousSessions
        .filter(s => s.createdAt > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
        .map(s => s.location)
        .filter(Boolean);

      if (recentLocations.length > 0 && !recentLocations.includes(location)) {
        riskScore += 0.2;
      }
    }

    // Multiple concurrent sessions from different locations
    const activeSessions = previousSessions.filter(s => s.isActive);
    if (activeSessions.length > 3) {
      riskScore += 0.1;
    }

    return Math.min(riskScore, 1.0);
  }

  /**
   * Log session activity
   */
  private static async logSessionActivity(
    sessionId: string,
    userId: string,
    action: string,
    details: any,
    context: SessionContext,
  ): Promise<void> {
    try {
      const riskScore = this.calculateRiskScore(
        context.ip || 'unknown',
        context.userAgent || 'unknown',
        context.location,
      );

      await prisma.sessionActivity.create({
        data: {
          sessionId,
          userId,
          action,
          details,
          ipAddress: context.ip || 'unknown',
          userAgent: context.userAgent || 'unknown',
          riskScore,
        },
      });
    } catch (error) {
      logger.error('Failed to log session activity', {
        sessionId,
        userId,
        action,
        error: sanitizeError(error),
      });
    }
  }

  /**
   * Create a new user session
   */
  static async createSession(userId: string, sessionToken: string, context: SessionContext): Promise<UserSession> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          userSessions: {
            where: { isActive: true },
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      if (!user) {
        throw new HttpException(404, 'User not found');
      }

      // Parse device information
      const deviceInfo = this.parseDeviceInfo(context.userAgent || '');
      const fingerprint = this.generateDeviceFingerprint(context.userAgent || '', context.deviceFingerprint);

      // Check for existing device
      let deviceId: string;
      let isNewDevice = false;

      const existingDevice = await prisma.trustedDevice.findFirst({
        where: {
          userId,
          fingerprint,
        },
      });

      if (existingDevice) {
        deviceId = existingDevice.deviceId;

        // Update last seen
        await prisma.trustedDevice.update({
          where: { id: existingDevice.id },
          data: { lastSeen: new Date() },
        });
      } else {
        deviceId = this.generateDeviceId();
        isNewDevice = true;

        // Create new trusted device entry
        await prisma.trustedDevice.create({
          data: {
            userId,
            deviceId,
            deviceName: deviceInfo.deviceName || 'Unknown Device',
            deviceType: deviceInfo.deviceType,
            fingerprint,
            userAgent: context.userAgent || '',
            trustLevel: 'pending',
          },
        });
      }

      // Calculate risk score
      const riskScore = this.calculateRiskScore(
        context.ip || '',
        context.userAgent || '',
        context.location,
        isNewDevice,
        user.userSessions,
      );

      // Check session limits and cleanup old sessions
      if (user.userSessions.length >= this.MAX_SESSIONS_PER_USER) {
        const oldestSession = user.userSessions[user.userSessions.length - 1];
        await this.terminateSession(oldestSession!.id, 'system', 'session_limit_exceeded');
      }

      // Create new session
      const expiresAt = new Date(Date.now() + this.SESSION_EXPIRY_HOURS * 60 * 60 * 1000);

      const session = await prisma.userSession.create({
        data: {
          userId,
          sessionToken,
          deviceId,
          deviceName: deviceInfo.deviceName,
          deviceType: deviceInfo.deviceType,
          userAgent: context.userAgent || '',
          ipAddress: context.ip || '',
          location: context.location,
          expiresAt,
          riskScore,
          suspiciousActivity: riskScore >= this.SUSPICIOUS_RISK_THRESHOLD,
          isTrusted: !isNewDevice && existingDevice?.trustLevel === 'trusted',
        },
      });

      // Log session creation
      await this.logSessionActivity(
        session.id,
        userId,
        'login',
        {
          deviceId,
          isNewDevice,
          riskScore,
          location: context.location,
        },
        context,
      );

      // Log authentication event
      const authEvent: AuthEvent = {
        type: 'AUTH_LOGIN_SUCCESS' as any,
        timestamp: new Date(),
        userId: user.id,
        email: user.email!,
        metadata: {
          sessionId: session.id,
          deviceId,
          isNewDevice,
          riskScore,
          suspiciousActivity: session.suspiciousActivity,
          ip: context.ip,
          userAgent: context.userAgent,
          location: context.location,
        },
      };
      logger.info('User session created', sanitizeAuthEvent(authEvent));

      return session;
    } catch (error) {
      logger.error('Failed to create user session', {
        userId,
        error: sanitizeError(error),
        ip: context.ip,
      });
      throw error;
    }
  }

  /**
   * Update session activity
   */
  static async updateSessionActivity(sessionToken: string, context: SessionContext): Promise<void> {
    try {
      const session = await prisma.userSession.findUnique({
        where: { sessionToken },
      });

      if (!session || !session.isActive) {
        return; // Session not found or inactive
      }

      // Update last activity
      await prisma.userSession.update({
        where: { id: session.id },
        data: { lastActivity: new Date() },
      });

      // Log activity
      await this.logSessionActivity(
        session.id,
        session.userId,
        'activity',
        {
          endpoint: context.deviceFingerprint, // Can be used to pass endpoint info
        },
        context,
      );
    } catch (error) {
      logger.error('Failed to update session activity', {
        sessionToken: sessionToken.substring(0, 8) + '...',
        error: sanitizeError(error),
      });
    }
  }

  /**
   * Get user sessions
   */
  static async getUserSessions(userId: string): Promise<SessionListResponse> {
    try {
      const sessions = await prisma.userSession.findMany({
        where: { userId },
        orderBy: { lastActivity: 'desc' },
        include: {
          activities: {
            orderBy: { timestamp: 'desc' },
            take: 1,
          },
        },
      });

      const sessionInfos: SessionInfo[] = sessions.map(session => ({
        id: session.id,
        deviceId: session.deviceId,
        deviceName: session.deviceName || undefined,
        deviceType: session.deviceType || undefined,
        userAgent: session.userAgent,
        ipAddress: session.ipAddress,
        location: session.location || undefined,
        isActive: session.isActive,
        isTrusted: session.isTrusted,
        lastActivity: session.lastActivity,
        createdAt: session.createdAt,
        expiresAt: session.expiresAt,
        riskScore: session.riskScore,
        suspiciousActivity: session.suspiciousActivity,
      }));

      const activeSessions = sessions.filter(s => s.isActive).length;
      const trustedDevices = await prisma.trustedDevice.count({
        where: {
          userId,
          trustLevel: 'trusted',
          isActive: true,
        },
      });

      return {
        sessions: sessionInfos,
        totalSessions: sessions.length,
        activeSessions,
        trustedDevices,
      };
    } catch (error) {
      logger.error('Failed to get user sessions', {
        userId,
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Terminate a specific session
   */
  static async terminateSession(
    sessionId: string,
    terminatedBy: string = 'user',
    reason: string = 'logout',
    context?: SessionContext,
  ): Promise<void> {
    try {
      const session = await prisma.userSession.findUnique({
        where: { id: sessionId },
        include: { user: true },
      });

      if (!session) {
        throw new HttpException(404, 'Session not found');
      }

      // Update session
      await prisma.userSession.update({
        where: { id: sessionId },
        data: {
          isActive: false,
          terminatedAt: new Date(),
          terminatedBy,
          terminationReason: reason,
        },
      });

      // Log termination
      if (context) {
        await this.logSessionActivity(
          sessionId,
          session.userId,
          'terminated',
          {
            terminatedBy,
            reason,
          },
          context,
        );
      }

      // Log authentication event
      const authEvent: AuthEvent = {
        type: 'AUTH_SESSION_TERMINATED' as any,
        timestamp: new Date(),
        userId: session.userId,
        email: session.user.email!,
        metadata: {
          sessionId,
          deviceId: session.deviceId,
          terminatedBy,
          reason,
          ip: context?.ip,
          userAgent: context?.userAgent,
        },
      };
      logger.info('User session terminated', sanitizeAuthEvent(authEvent));
    } catch (error) {
      logger.error('Failed to terminate session', {
        sessionId,
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Terminate all sessions for a user except current
   */
  static async terminateAllOtherSessions(
    userId: string,
    currentSessionId: string,
    context?: SessionContext,
  ): Promise<number> {
    try {
      const sessions = await prisma.userSession.findMany({
        where: {
          userId,
          isActive: true,
          id: { not: currentSessionId },
        },
      });

      // Terminate all other sessions
      await prisma.userSession.updateMany({
        where: {
          userId,
          isActive: true,
          id: { not: currentSessionId },
        },
        data: {
          isActive: false,
          terminatedAt: new Date(),
          terminatedBy: 'user',
          terminationReason: 'logout_all_others',
        },
      });

      // Log terminations
      for (const session of sessions) {
        if (context) {
          await this.logSessionActivity(
            session.id,
            userId,
            'terminated',
            {
              terminatedBy: 'user',
              reason: 'logout_all_others',
            },
            context,
          );
        }
      }

      logger.info('All other user sessions terminated', {
        userId,
        terminatedCount: sessions.length,
        currentSessionId,
      });

      return sessions.length;
    } catch (error) {
      logger.error('Failed to terminate all other sessions', {
        userId,
        currentSessionId,
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Get session by token
   */
  static async getSessionByToken(sessionToken: string): Promise<UserSession | null> {
    try {
      return await prisma.userSession.findUnique({
        where: { sessionToken },
        include: { user: true },
      });
    } catch (error) {
      logger.error('Failed to get session by token', {
        error: sanitizeError(error),
      });
      return null;
    }
  }

  /**
   * Trust a device
   */
  static async trustDevice(userId: string, deviceId: string /*, _context?: SessionContext*/): Promise<void> {
    try {
      const device = await prisma.trustedDevice.findFirst({
        where: { userId, deviceId },
      });

      if (!device) {
        throw new HttpException(404, 'Device not found');
      }

      await prisma.trustedDevice.update({
        where: { id: device.id },
        data: {
          trustLevel: 'trusted',
          trustedAt: new Date(),
          trustedBy: 'user',
        },
      });

      // Update all sessions for this device
      await prisma.userSession.updateMany({
        where: { userId, deviceId },
        data: { isTrusted: true },
      });

      logger.info('Device trusted', {
        userId,
        deviceId,
        deviceName: device.deviceName,
      });
    } catch (error) {
      logger.error('Failed to trust device', {
        userId,
        deviceId,
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Cleanup expired sessions
   */
  static async cleanupExpiredSessions(): Promise<number> {
    try {
      const expiredSessions = await prisma.userSession.findMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            {
              lastActivity: {
                lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days inactive
              },
            },
          ],
          isActive: true,
        },
      });

      if (expiredSessions.length > 0) {
        await prisma.userSession.updateMany({
          where: {
            id: { in: expiredSessions.map(s => s.id) },
          },
          data: {
            isActive: false,
            terminatedAt: new Date(),
            terminatedBy: 'system',
            terminationReason: 'expired',
          },
        });

        logger.info('Expired sessions cleaned up', {
          count: expiredSessions.length,
        });
      }

      return expiredSessions.length;
    } catch (error) {
      logger.error('Failed to cleanup expired sessions', {
        error: sanitizeError(error),
      });
      return 0;
    }
  }

  /**
   * Get session activity for a user
   */
  static async getSessionActivity(userId: string, limit: number = 50, offset: number = 0): Promise<SessionActivity[]> {
    try {
      return await prisma.sessionActivity.findMany({
        where: { userId },
        orderBy: { timestamp: 'desc' },
        take: limit,
        skip: offset,
        include: {
          session: {
            select: {
              deviceId: true,
              deviceName: true,
              deviceType: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error('Failed to get session activity', {
        userId,
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Extract session context from request
   */
  static extractSessionContext(req: Request): SessionContext {
    return {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      deviceFingerprint: (req as any).deviceFingerprint,
      // Location would be extracted from IP geolocation service
    };
  }
}
