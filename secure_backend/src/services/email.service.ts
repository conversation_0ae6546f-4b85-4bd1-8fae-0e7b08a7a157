import { Service } from 'typedi';
import Mailjet from 'node-mailjet';
import * as fs from 'fs';
import * as path from 'path';
import { BaseService } from './base.service';
import { HttpException } from '../exceptions/HttpException';
import { sanitizeForLog, sanitizeError } from '../utils/logSanitizer';
import { env } from '../config';
import { BaseUserData } from '../types/template';

interface EmailTemplate {
  html: string;
  text: string;
}

interface TemplateVariables {
  APP_NAME: string;
  USER_EMAIL: string;
  VERIFICATION_URL?: string;
  VERIFICATION_TITLE?: string;
  VERIFICATION_MESSAGE?: string;
  RESET_URL?: string;
  RESET_TITLE?: string;
  RESET_MESSAGE?: string;
  EXPIRES_IN: string;
  CURRENT_YEAR: string;
}

interface EmailSendOptions {
  retries?: number;
  priority?: 'low' | 'normal' | 'high';
  trackOpens?: boolean;
  trackClicks?: boolean;
}

interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

@Service()
export class EmailService extends BaseService {
  private mailjet: any;
  private templatesCache: Map<string, EmailTemplate> = new Map();
  private readonly templateDir = path.join(__dirname, '../templates/email');
  private readonly defaultRetries = 3;
  private readonly retryDelay = 1000; // 1 second

  constructor() {
    super();
    this.initializeMailjet();
  }

  /**
   * Initialize Mailjet client
   */
  private initializeMailjet(): void {
    try {
      // Only validate configuration in production
      if (process.env['NODE_ENV'] !== 'test') {
        EmailService.validateConfiguration();
      }

      // For testing, use a simple mock object if Mailjet fails to initialize
      if (process.env['NODE_ENV'] === 'test') {
        this.mailjet = {
          post: () => ({
            request: () =>
              Promise.resolve({
                body: { Messages: [{ MessageID: 'test-message-id' }] },
              }),
          }),
          get: () => ({
            request: () => Promise.resolve({ body: { Data: [] } }),
          }),
        };
      } else {
        this.mailjet = new Mailjet({
          apiKey: env.MAILJET_API_KEY,
          apiSecret: env.MAILJET_API_SECRET,
          config: {
            host: 'api.mailjet.com',
            version: 'v3.1',
          },
        });
      }

      this.logInfo('Mailjet client initialized successfully');
    } catch (error) {
      this.logError('Failed to initialize Mailjet client', error);
      throw new HttpException(500, 'Email service initialization failed');
    }
  }

  /**
   * Send verification email (supports both new user verification and email change)
   */
  async sendVerificationEmail(
    user: BaseUserData,
    token: string,
    isEmailChange: boolean = false,
    options: EmailSendOptions = {},
  ): Promise<EmailResult> {
    const templateName = isEmailChange ? 'email-change' : 'verification';
    const subject = isEmailChange ? 'Verify Your New Email Address' : 'Verify Your Email Address';

    const verificationUrl = this.buildVerificationUrl(token, isEmailChange);

    const templateVariables: TemplateVariables = {
      APP_NAME: env.MAIL_FROM_NAME || 'Secure Backend API',
      USER_EMAIL: user.email,
      VERIFICATION_URL: verificationUrl,
      VERIFICATION_TITLE: isEmailChange ? 'Email Change Verification' : 'Email Verification',
      VERIFICATION_MESSAGE: isEmailChange
        ? 'You have requested to change your email address on your account. To complete this change, please verify your new email address.'
        : 'Thank you for registering! Please verify your email address to complete your account setup.',
      EXPIRES_IN: env.EMAIL_VERIFICATION_TOKEN_EXPIRES || '48 hours',
      CURRENT_YEAR: new Date().getFullYear().toString(),
    };

    return this.sendEmail({
      to: user.email,
      subject,
      templateName,
      templateVariables,
      userId: user.id,
      options,
    });
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(
    user: BaseUserData,
    token: string,
    options: EmailSendOptions = {},
  ): Promise<EmailResult> {
    const subject = 'Reset Your Password';
    const templateName = 'password-reset';

    const resetUrl = this.buildPasswordResetUrl(token);

    const templateVariables: TemplateVariables = {
      APP_NAME: env.MAIL_FROM_NAME || 'Secure Backend API',
      USER_EMAIL: user.email,
      RESET_URL: resetUrl,
      RESET_TITLE: 'Password Reset Request',
      RESET_MESSAGE:
        'You have requested to reset your password. Click the button below to set a new password for your account.',
      EXPIRES_IN: '30 minutes',
      CURRENT_YEAR: new Date().getFullYear().toString(),
    };

    return this.sendEmail({
      to: user.email,
      subject,
      templateName,
      templateVariables,
      userId: user.id,
      options,
    });
  }

  /**
   * Generic email sending method with template support
   */
  private async sendEmail({
    to,
    subject,
    templateName,
    templateVariables,
    userId,
    options = {},
  }: {
    to: string;
    subject: string;
    templateName: string;
    templateVariables: TemplateVariables;
    userId?: string;
    options?: EmailSendOptions;
  }): Promise<EmailResult> {
    const maxRetries = options.retries ?? this.defaultRetries;
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Load and process templates
        const template = await this.getTemplate(templateName);
        const htmlContent = this.processTemplate(template.html, templateVariables);
        const textContent = this.processTemplate(template.text, templateVariables);

        // Prepare email message
        const message = {
          From: {
            Email: env.MAIL_FROM,
            Name: env.MAIL_FROM_NAME,
          },
          To: [
            {
              Email: to,
              Name: to,
            },
          ],
          Subject: subject,
          TextPart: textContent,
          HTMLPart: htmlContent,
          CustomID: userId ? `user-${userId}-${Date.now()}` : `email-${Date.now()}`,
          TrackOpens: options.trackOpens !== false,
          TrackClicks: options.trackClicks !== false,
        };

        // Send email
        const result = await this.mailjet.post('send', { version: 'v3.1' }).request({
          Messages: [message],
        });

        const messageId = result.body?.Messages?.[0]?.MessageID;

        this.logInfo(
          'Email sent successfully',
          sanitizeForLog({
            to,
            subject,
            templateName,
            messageId,
            userId,
            attempt,
          }),
        );

        return {
          success: true,
          messageId: messageId?.toString(),
        };
      } catch (error) {
        lastError = error;

        this.logWarn(
          `Email send attempt ${attempt} failed`,
          sanitizeForLog({
            to,
            subject,
            templateName,
            userId,
            attempt,
            maxRetries,
            error: sanitizeError(error),
          }),
        );

        // If this is not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          await this.delay(this.retryDelay * attempt); // Exponential backoff
        }
      }
    }

    // All retries failed
    this.logError(
      'Email send failed after all retries',
      sanitizeForLog({
        to,
        subject,
        templateName,
        userId,
        maxRetries,
        error: sanitizeError(lastError),
      }),
    );

    return {
      success: false,
      error: lastError?.message || 'Email send failed',
    };
  }

  /**
   * Load email template from filesystem (with caching)
   */
  async getTemplate(templateName: string): Promise<EmailTemplate> {
    // Check cache first
    if (this.templatesCache.has(templateName)) {
      return this.templatesCache.get(templateName)!;
    }

    try {
      const htmlPath = path.join(this.templateDir, `${templateName}.html`);
      const textPath = path.join(this.templateDir, `${templateName}.txt`);

      // Check if template files exist
      if (!fs.existsSync(htmlPath)) {
        throw new Error(`HTML template not found: ${htmlPath}`);
      }
      if (!fs.existsSync(textPath)) {
        throw new Error(`Text template not found: ${textPath}`);
      }

      // Read template files
      const html = fs.readFileSync(htmlPath, 'utf-8');
      const text = fs.readFileSync(textPath, 'utf-8');

      const template: EmailTemplate = { html, text };

      // Cache the template
      this.templatesCache.set(templateName, template);

      this.logInfo(
        'Email template loaded successfully',
        sanitizeForLog({
          templateName,
          htmlLength: html.length,
          textLength: text.length,
        }),
      );

      return template;
    } catch (error) {
      this.logError(
        'Failed to load email template',
        sanitizeForLog({
          templateName,
          error: sanitizeError(error),
        }),
      );

      throw new HttpException(500, `Email template loading failed: ${templateName}`);
    }
  }

  /**
   * Process template by replacing variables
   */
  private processTemplate(template: string, variables: TemplateVariables): string {
    let processed = template;

    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      processed = processed.replace(new RegExp(placeholder, 'g'), value);
    });

    return processed;
  }

  /**
   * Build verification URL
   */
  private buildVerificationUrl(token: string, isEmailChange: boolean): string {
    const baseUrl = env.ORIGIN === '*' ? 'http://localhost:3000' : env.ORIGIN;
    const endpoint = isEmailChange ? 'verify-email-change' : 'verify-email';
    return `${baseUrl}/auth/${endpoint}?token=${encodeURIComponent(token)}`;
  }

  /**
   * Build password reset URL
   */
  private buildPasswordResetUrl(token: string): string {
    const baseUrl = env.ORIGIN === '*' ? 'http://localhost:3000' : env.ORIGIN;
    return `${baseUrl}/auth/reset-password?token=${encodeURIComponent(token)}`;
  }

  /**
   * Test email connectivity (useful for health checks)
   */
  async testConnection(): Promise<boolean> {
    try {
      // Use Mailjet's connection test endpoint
      await this.mailjet.get('sender').request();
      this.logInfo('Email service connection test successful');
      return true;
    } catch (error) {
      this.logError('Email service connection test failed', sanitizeError(error));
      return false;
    }
  }

  /**
   * Clear template cache (useful for development)
   */
  clearTemplateCache(): void {
    this.templatesCache.clear();
    this.logInfo('Email template cache cleared');
  }

  /**
   * Get template cache statistics
   */
  getTemplateCacheStats(): { cacheSize: number; cachedTemplates: string[] } {
    return {
      cacheSize: this.templatesCache.size,
      cachedTemplates: Array.from(this.templatesCache.keys()),
    };
  }

  /**
   * Utility method for delays (used in retry logic)
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate email configuration on startup
   */
  static validateConfiguration(): void {
    const requiredEnvVars = ['MAILJET_API_KEY', 'MAILJET_API_SECRET', 'MAIL_FROM', 'MAIL_FROM_NAME'];

    const missing = requiredEnvVars.filter(varName => !env[varName as keyof typeof env]);

    if (missing.length > 0) {
      throw new Error(`Missing required email configuration: ${missing.join(', ')}`);
    }
  }
}

// Export the class and allow users to create instances as needed
// For singleton usage, create instance in your application startup
export const createEmailService = () => new EmailService();
