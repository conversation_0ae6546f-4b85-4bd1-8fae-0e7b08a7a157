import { Service } from 'typedi';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { PersonaSelection, CourseAnalysis, Curriculum } from '@/interfaces/learning.interface';

@Service()
export class AIService {
  private genAI: GoogleGenerativeAI;

  constructor() {
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_AI_API_KEY environment variable is required');
    }
    this.genAI = new GoogleGenerativeAI(apiKey);
  }

  async generateCurriculum(subject: string, language: string = 'English'): Promise<Curriculum> {
    const model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const prompt = `Create a comprehensive learning curriculum for "${subject}" in ${language}.

Return a JSON object with this exact structure:
{
  "curriculum": [
    {
      "id": "unique-id",
      "title": "Category Title",
      "description": "Brief description",
      "topics": [
        {
          "id": "unique-topic-id",
          "title": "Topic Title",
          "description": "Topic description",
          "estimatedTime": "30 minutes",
          "difficulty": "beginner|intermediate|advanced",
          "resources": ["resource1", "resource2"]
        }
      ]
    }
  ]
}

Create 5-8 categories with 3-6 topics each. Make it comprehensive and practical.
Focus on hands-on learning and real-world applications.
All content should be in ${language}.`;

    try {
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      // Clean the response to extract JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in AI response');
      }

      const curriculum = JSON.parse(jsonMatch[0]);
      
      // Validate the structure
      if (!curriculum.curriculum || !Array.isArray(curriculum.curriculum)) {
        throw new Error('Invalid curriculum structure returned by AI');
      }

      return curriculum;
    } catch (error) {
      console.error('Error generating curriculum:', error);
      throw new Error('Failed to generate curriculum. Please try again.');
    }
  }

  async selectPersona(subject: string, language: string = 'English'): Promise<PersonaSelection> {
    const model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const personas = [
      {
        name: 'TechMentor',
        expertise: 'Programming, software development, computer science, web development, mobile development',
        prompt: 'You are TechMentor, an expert software engineer and computer science educator with 15+ years of experience in full-stack development, system architecture, and teaching programming. Create a comprehensive, hands-on curriculum for ${subject} that emphasizes practical coding skills, real-world projects, and industry best practices.'
      },
      {
        name: 'DataSage',
        expertise: 'Data science, machine learning, AI, statistics, data analysis, big data',
        prompt: 'You are DataSage, a senior data scientist and AI researcher with expertise in machine learning, statistical analysis, and data engineering. Create a curriculum for ${subject} that combines theoretical foundations with practical data projects, real datasets, and industry-standard tools.'
      },
      {
        name: 'CloudArchitect',
        expertise: 'Cloud computing, DevOps, system administration, infrastructure, cybersecurity',
        prompt: 'You are CloudArchitect, a cloud solutions architect and DevOps expert with deep knowledge of AWS, Azure, GCP, and modern infrastructure practices. Design a curriculum for ${subject} that covers both theoretical concepts and hands-on cloud implementations.'
      },
      {
        name: 'BusinessGuru',
        expertise: 'Business strategy, management, marketing, entrepreneurship, finance, economics',
        prompt: 'You are BusinessGuru, a seasoned business consultant and MBA educator with experience in strategy, operations, and leadership. Create a curriculum for ${subject} that combines business theory with practical case studies and real-world applications.'
      },
      {
        name: 'CreativeMaster',
        expertise: 'Design, art, creative writing, multimedia, user experience, digital media',
        prompt: 'You are CreativeMaster, a creative director and design educator with expertise in visual design, user experience, and creative processes. Develop a curriculum for ${subject} that balances creative theory with hands-on projects and portfolio development.'
      },
      {
        name: 'ScienceExplorer',
        expertise: 'Natural sciences, mathematics, physics, chemistry, biology, research methods',
        prompt: 'You are ScienceExplorer, a research scientist and academic with expertise in scientific methodology and education. Create a curriculum for ${subject} that emphasizes scientific thinking, experimental design, and practical applications.'
      },
      {
        name: 'LanguageMaster',
        expertise: 'Languages, linguistics, communication, writing, literature, cultural studies',
        prompt: 'You are LanguageMaster, a linguist and language educator with expertise in language acquisition and cross-cultural communication. Design a curriculum for ${subject} that focuses on practical communication skills and cultural understanding.'
      },
      {
        name: 'HealthWellnessCoach',
        expertise: 'Health, wellness, fitness, nutrition, psychology, personal development',
        prompt: 'You are HealthWellnessCoach, a certified health professional and wellness educator. Create a curriculum for ${subject} that combines evidence-based health information with practical lifestyle applications.'
      }
    ];

    const personaSelectionPrompt = `Analyze this learning topic: "${subject}"

Based on the subject matter, select the most appropriate persona from this list:
${personas.map(p => `- ${p.name}: ${p.expertise}`).join('\n')}

Return a JSON object with this exact structure:
{
  "persona": "PersonaName",
  "reasoning": "Detailed explanation of why this persona is the best fit for this subject",
  "curriculumPrompt": "The full curriculum generation prompt for this persona"
}

Consider the subject's domain, complexity, and learning objectives when making your selection.
All content should be in ${language}.`;

    try {
      const result = await model.generateContent(personaSelectionPrompt);
      const response = await result.response;
      const text = response.text();
      
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in persona selection response');
      }

      const selection = JSON.parse(jsonMatch[0]);
      
      // Find the selected persona and use its prompt
      const selectedPersona = personas.find(p => p.name === selection.persona);
      if (selectedPersona) {
        selection.curriculumPrompt = selectedPersona.prompt;
      }

      return selection;
    } catch (error) {
      console.error('Error selecting persona:', error);
      // Fallback to TechMentor for technical subjects
      return {
        persona: 'TechMentor',
        reasoning: 'Fallback selection due to AI error',
        curriculumPrompt: personas[0].prompt
      };
    }
  }

  async analyzeCourseSketch(userSketch: string, language: string = 'English'): Promise<CourseAnalysis> {
    const model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const analysisPrompt = `Analyze this user's learning sketch and create a comprehensive university-style course structure:

"${userSketch}"

Create a detailed course analysis with multiple specialized modules. Return a JSON object with this exact structure:

{
  "courseInfo": {
    "title": "Comprehensive Course Title",
    "description": "Detailed course description",
    "duration": "X weeks",
    "difficulty": "beginner|intermediate|advanced",
    "prerequisites": ["prerequisite1", "prerequisite2"],
    "learningOutcomes": ["outcome1", "outcome2", "outcome3"]
  },
  "modules": [
    {
      "id": "module-1",
      "title": "Module Title",
      "description": "Module description",
      "duration": "X weeks",
      "order": 1,
      "prerequisites": ["prerequisite1"],
      "learningObjectives": ["objective1", "objective2"],
      "curriculum": []
    }
  ],
  "reasoning": "Detailed explanation of the course structure and module organization"
}

Requirements:
- Create 5-8 specialized modules that cover all aspects mentioned in the sketch
- Each module should be 1-4 weeks long
- Total course duration should be 12-20 weeks (university semester length)
- Learning outcomes should be specific and measurable
- Modules should build upon each other logically
- All content should be in ${language}
- Focus on practical, hands-on learning with real-world applications`;

    try {
      const result = await model.generateContent(analysisPrompt);
      const response = await result.response;
      const text = response.text();
      
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in course analysis response');
      }

      const analysis = JSON.parse(jsonMatch[0]);
      
      // Validate the structure
      if (!analysis.courseInfo || !analysis.modules || !Array.isArray(analysis.modules)) {
        throw new Error('Invalid course analysis structure returned by AI');
      }

      return analysis;
    } catch (error) {
      console.error('Error analyzing course sketch:', error);
      throw new Error('Failed to analyze course sketch. Please try again.');
    }
  }

  async generateModuleCurriculum(
    moduleTitle: string,
    moduleDescription: string,
    learningObjectives: string[],
    courseContext: string,
    language: string = 'English'
  ): Promise<any> {
    // First select the appropriate persona for this module
    const personaSelection = await this.selectPersona(`${courseContext} - ${moduleTitle}`, language);
    
    const model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    const curriculumPrompt = `${personaSelection.curriculumPrompt.replace('${subject}', moduleTitle)}

**Module Context:**
- Course: ${courseContext}
- Module: ${moduleTitle}
- Description: ${moduleDescription}
- Learning Objectives: ${learningObjectives.join(', ')}

Create a detailed curriculum for this specific module. Return a JSON object with this structure:

{
  "curriculum": [
    {
      "id": "category-id",
      "title": "Category Title",
      "description": "Category description",
      "topics": [
        {
          "id": "topic-id",
          "title": "Topic Title",
          "description": "Detailed topic description",
          "estimatedTime": "X hours",
          "difficulty": "beginner|intermediate|advanced",
          "resources": ["resource1", "resource2"],
          "exercises": ["exercise1", "exercise2"]
        }
      ]
    }
  ]
}

Create 3-6 categories with 2-5 topics each. Focus on practical, hands-on learning.
All content should be in ${language}.`;

    try {
      const result = await model.generateContent(curriculumPrompt);
      const response = await result.response;
      const text = response.text();
      
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in module curriculum response');
      }

      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Error generating module curriculum:', error);
      throw new Error('Failed to generate module curriculum. Please try again.');
    }
  }
}
