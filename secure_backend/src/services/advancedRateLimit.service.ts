import { RateLimiterRedis, RateLimiterRes } from 'rate-limiter-flexible';
import { redis } from '../config/redis';
import { logger } from '../utils/secureLogger';
import { sanitizeError } from '../utils/logSanitizer';
import { Request } from 'express';

export interface RateLimitConfig {
  points: number;
  duration: number;
  blockDuration: number;
  execEvenly?: boolean;
  keyPrefix: string;
}

export interface AdaptiveRateLimitConfig extends RateLimitConfig {
  basePoints: number;
  maxPoints: number;
  adaptiveMultiplier: number;
  trustScoreThreshold: number;
}

export interface DDoSDetectionConfig {
  requestThreshold: number;
  timeWindow: number;
  blockDuration: number;
  suspiciousPatternThreshold: number;
}

export interface RateLimitResult {
  allowed: boolean;
  remainingPoints: number;
  resetTime: Date;
  retryAfter?: number;
  reason?: string;
}

export interface ThreatAnalysis {
  riskScore: number;
  threatLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  patterns: string[];
  recommendations: string[];
}

/**
 * Advanced Rate Limiting Service
 * Provides sophisticated rate limiting with adaptive algorithms, DDoS protection, and threat analysis
 */
export class AdvancedRateLimitService {
  private static readonly THREAT_PATTERNS = {
    RAPID_REQUESTS: 'rapid_sequential_requests',
    DISTRIBUTED_ATTACK: 'distributed_attack_pattern',
    CREDENTIAL_STUFFING: 'credential_stuffing_attempt',
    API_ABUSE: 'api_endpoint_abuse',
    SUSPICIOUS_USER_AGENT: 'suspicious_user_agent',
    GEO_ANOMALY: 'geographical_anomaly',
  };

  private static readonly DEFAULT_DDOS_CONFIG: DDoSDetectionConfig = {
    requestThreshold: 1000,
    timeWindow: 60, // 1 minute
    blockDuration: 300, // 5 minutes
    suspiciousPatternThreshold: 0.8,
  };

  // Cache for user trust scores and behavior patterns
  private static userTrustCache = new Map<string, { score: number; lastUpdate: Date }>();
  // private static ipBehaviorCache = new Map<string, { requests: number[]; patterns: string[] }>();

  /**
   * Create adaptive rate limiter based on user behavior and trust score
   */
  static createAdaptiveRateLimiter(config: AdaptiveRateLimitConfig): RateLimiterRedis {
    return new RateLimiterRedis({
      storeClient: redis,
      keyPrefix: config.keyPrefix,
      points: config.basePoints,
      duration: config.duration,
      blockDuration: config.blockDuration,
      execEvenly: config.execEvenly || true,
    });
  }

  /**
   * Calculate user trust score based on historical behavior
   */
  static async calculateUserTrustScore(userId: string, ip: string): Promise<number> {
    try {
      // Check cache first
      const cached = this.userTrustCache.get(userId);
      if (cached && Date.now() - cached.lastUpdate.getTime() < 300000) {
        // 5 minutes cache
        return cached.score;
      }

      // Base trust score
      let trustScore = 0.5; // Neutral starting point

      // Analyze historical behavior patterns
      const behaviorKey = `behavior:${userId}:${ip}`;
      const behaviorData = await redis.hgetall(behaviorKey);

      if (behaviorData) {
        // Positive factors
        const successfulRequests = parseInt(behaviorData['successful_requests'] || '0');
        const totalRequests = parseInt(behaviorData['total_requests'] || '1');
        const accountAge = parseInt(behaviorData['account_age_days'] || '0');
        const verifiedEmail = behaviorData['verified_email'] === 'true';
        const mfaEnabled = behaviorData['mfa_enabled'] === 'true';

        // Calculate success rate
        const successRate = successfulRequests / totalRequests;
        trustScore += successRate * 0.3;

        // Account age bonus (up to 30 days)
        trustScore += Math.min(accountAge / 30, 1) * 0.2;

        // Security features bonus
        if (verifiedEmail) trustScore += 0.1;
        if (mfaEnabled) trustScore += 0.15;

        // Negative factors
        const failedLogins = parseInt(behaviorData['failed_logins'] || '0');
        const suspiciousActivity = parseInt(behaviorData['suspicious_activity'] || '0');
        const rateLimitViolations = parseInt(behaviorData['rate_limit_violations'] || '0');

        // Penalties
        trustScore -= Math.min(failedLogins / 10, 0.3);
        trustScore -= Math.min(suspiciousActivity / 5, 0.2);
        trustScore -= Math.min(rateLimitViolations / 3, 0.25);
      }

      // Ensure score is between 0 and 1
      trustScore = Math.max(0, Math.min(1, trustScore));

      // Cache the result
      this.userTrustCache.set(userId, { score: trustScore, lastUpdate: new Date() });

      return trustScore;
    } catch (error) {
      logger.error('Failed to calculate user trust score', {
        userId,
        ip,
        error: sanitizeError(error),
      });
      return 0.5; // Default neutral score on error
    }
  }

  /**
   * Adaptive rate limiting based on user trust and behavior
   */
  static async adaptiveRateLimit(
    config: AdaptiveRateLimitConfig,
    userId: string,
    ip: string,
    identifier: string,
  ): Promise<RateLimitResult> {
    try {
      // Calculate user trust score
      const trustScore = await this.calculateUserTrustScore(userId, ip);

      // Adjust rate limit based on trust score
      let adjustedPoints = config.basePoints;

      if (trustScore >= config.trustScoreThreshold) {
        // Trusted users get more requests
        adjustedPoints = Math.min(
          config.maxPoints,
          Math.floor(config.basePoints * (1 + trustScore * config.adaptiveMultiplier)),
        );
      } else {
        // Less trusted users get fewer requests
        adjustedPoints = Math.max(Math.floor(config.basePoints * 0.5), Math.floor(config.basePoints * trustScore));
      }

      // Create rate limiter with adjusted points
      const rateLimiter = new RateLimiterRedis({
        storeClient: redis,
        keyPrefix: config.keyPrefix,
        points: adjustedPoints,
        duration: config.duration,
        blockDuration: config.blockDuration,
        execEvenly: config.execEvenly || true,
      });

      // Attempt to consume a point
      const result = await rateLimiter.consume(identifier);

      return {
        allowed: true,
        remainingPoints: result.remainingPoints || 0,
        resetTime: new Date(Date.now() + (result.msBeforeNext || 0)),
      };
    } catch (rateLimiterRes) {
      const rateLimiterError = rateLimiterRes as RateLimiterRes;

      if (rateLimiterError && typeof rateLimiterError.msBeforeNext === 'number') {
        // Update behavior tracking
        await this.updateBehaviorTracking(userId, ip, 'rate_limit_violation');

        return {
          allowed: false,
          remainingPoints: rateLimiterError.remainingPoints || 0,
          resetTime: new Date(Date.now() + rateLimiterError.msBeforeNext),
          retryAfter: Math.ceil(rateLimiterError.msBeforeNext / 1000),
          reason: 'Rate limit exceeded',
        };
      }

      throw rateLimiterRes;
    }
  }

  /**
   * DDoS detection and mitigation
   */
  static async detectDDoSAttack(
    ip: string,
    userAgent: string,
    endpoint: string,
    config: DDoSDetectionConfig = this.DEFAULT_DDOS_CONFIG,
  ): Promise<{ isDDoS: boolean; blockDuration?: number; reason?: string }> {
    try {
      const now = Date.now();
      const windowStart = now - config.timeWindow * 1000;

      // Track requests in time window
      const requestKey = `ddos:requests:${ip}`;
      const requests = await redis.zrangebyscore(requestKey, windowStart, now);

      // Add current request
      await redis.zadd(requestKey, now, `${now}:${endpoint}`);
      await redis.expire(requestKey, config.timeWindow * 2);

      // Check if threshold exceeded
      if (requests.length >= config.requestThreshold) {
        // Analyze request patterns for DDoS characteristics
        const threatAnalysis = await this.analyzeThreatPatterns(ip, userAgent, requests);

        if (threatAnalysis.riskScore >= config.suspiciousPatternThreshold) {
          // Block the IP
          const blockKey = `ddos:blocked:${ip}`;
          await redis.setex(
            blockKey,
            config.blockDuration,
            JSON.stringify({
              blockedAt: now,
              reason: 'DDoS attack detected',
              threatLevel: threatAnalysis.threatLevel,
              patterns: threatAnalysis.patterns,
            }),
          );

          logger.warn('DDoS attack detected and blocked', {
            ip,
            userAgent,
            requestCount: requests.length,
            threatLevel: threatAnalysis.threatLevel,
            riskScore: threatAnalysis.riskScore,
            patterns: threatAnalysis.patterns,
          });

          return {
            isDDoS: true,
            blockDuration: config.blockDuration,
            reason: `DDoS attack detected: ${threatAnalysis.patterns.join(', ')}`,
          };
        }
      }

      return { isDDoS: false };
    } catch (error) {
      logger.error('DDoS detection failed', {
        ip,
        userAgent,
        endpoint,
        error: sanitizeError(error),
      });
      return { isDDoS: false };
    }
  }

  /**
   * Analyze threat patterns in request data
   */
  static async analyzeThreatPatterns(ip: string, userAgent: string, requests: string[]): Promise<ThreatAnalysis> {
    let riskScore = 0;
    const patterns: string[] = [];
    const recommendations: string[] = [];

    try {
      // Analyze request timing patterns
      const timestamps = requests.map(req => {
        const parts = req?.split(':');
        return parts && parts[0] ? parseInt(parts[0]) : 0;
      });
      const intervals: number[] = [];

      for (let i = 1; i < timestamps.length; i++) {
        const current = timestamps[i];
        const previous = timestamps[i - 1];
        if (current !== undefined && previous !== undefined) {
          intervals.push(current - previous);
        }
      }

      // Check for rapid sequential requests (bot-like behavior)
      const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
      if (avgInterval < 100) {
        // Less than 100ms between requests
        riskScore += 0.4;
        patterns.push(this.THREAT_PATTERNS.RAPID_REQUESTS);
        recommendations.push('Implement CAPTCHA verification');
      }

      // Check for suspicious user agent patterns
      if (this.isSuspiciousUserAgent(userAgent)) {
        riskScore += 0.3;
        patterns.push(this.THREAT_PATTERNS.SUSPICIOUS_USER_AGENT);
        recommendations.push('Block suspicious user agents');
      }

      // Check for distributed attack patterns
      const distributedScore = await this.checkDistributedAttackPattern(ip);
      if (distributedScore > 0.5) {
        riskScore += 0.5;
        patterns.push(this.THREAT_PATTERNS.DISTRIBUTED_ATTACK);
        recommendations.push('Implement geographical IP filtering');
      }

      // Determine threat level
      let threatLevel: ThreatAnalysis['threatLevel'] = 'LOW';
      if (riskScore >= 0.8) threatLevel = 'CRITICAL';
      else if (riskScore >= 0.6) threatLevel = 'HIGH';
      else if (riskScore >= 0.4) threatLevel = 'MEDIUM';

      return {
        riskScore: Math.min(riskScore, 1),
        threatLevel,
        patterns,
        recommendations,
      };
    } catch (error) {
      logger.error('Threat pattern analysis failed', {
        ip,
        error: sanitizeError(error),
      });

      return {
        riskScore: 0.5,
        threatLevel: 'MEDIUM',
        patterns: ['analysis_error'],
        recommendations: ['Manual review required'],
      };
    }
  }

  /**
   * Check if user agent appears suspicious
   */
  private static isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i,
      /^$/,
      /test/i,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  /**
   * Check for distributed attack patterns across multiple IPs
   */
  private static async checkDistributedAttackPattern(currentIp: string): Promise<number> {
    try {
      // Get recent attack attempts from different IPs
      const attackKey = 'ddos:distributed_attacks';
      const now = Date.now();
      const windowStart = now - 300 * 1000; // 5 minutes

      // Get recent attacks
      const recentAttacks = await redis.zrangebyscore(attackKey, windowStart, now);

      // Add current IP to tracking
      await redis.zadd(attackKey, now, currentIp);
      await redis.expire(attackKey, 600); // 10 minutes

      // Analyze IP distribution
      const uniqueIPs = new Set(recentAttacks);
      const attackCount = recentAttacks.length;

      // High number of attacks from different IPs indicates distributed attack
      if (uniqueIPs.size > 10 && attackCount > 50) {
        return 0.8;
      } else if (uniqueIPs.size > 5 && attackCount > 20) {
        return 0.6;
      } else if (uniqueIPs.size > 3 && attackCount > 10) {
        return 0.4;
      }

      return 0.2;
    } catch (error) {
      logger.error('Distributed attack pattern check failed', {
        currentIp,
        error: sanitizeError(error),
      });
      return 0.3; // Default moderate score on error
    }
  }

  /**
   * Update behavior tracking for a user
   */
  private static async updateBehaviorTracking(
    userId: string,
    ip: string,
    event: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    try {
      const behaviorKey = `behavior:${userId}:${ip}`;
      const eventKey = `${event}_count`;

      // Increment event counter
      await redis.hincrby(behaviorKey, eventKey, 1);

      // Update last activity
      await redis.hset(behaviorKey, 'last_activity', Date.now().toString());

      // Set expiration (30 days)
      await redis.expire(behaviorKey, 30 * 24 * 60 * 60);

      // Store metadata if provided
      if (metadata) {
        await redis.hset(behaviorKey, 'last_metadata', JSON.stringify(metadata));
      }

      // Invalidate trust score cache
      this.userTrustCache.delete(userId);
    } catch (error) {
      logger.error('Failed to update behavior tracking', {
        userId,
        ip,
        event,
        error: sanitizeError(error),
      });
    }
  }

  /**
   * Check if IP is currently blocked for DDoS
   */
  static async isIPBlocked(ip: string): Promise<{ blocked: boolean; reason?: string; expiresAt?: Date }> {
    try {
      const blockKey = `ddos:blocked:${ip}`;
      const blockData = await redis.get(blockKey);

      if (blockData) {
        const blockInfo = JSON.parse(blockData);
        const ttl = await redis.ttl(blockKey);

        return {
          blocked: true,
          reason: blockInfo.reason,
          expiresAt: new Date(Date.now() + ttl * 1000),
        };
      }

      return { blocked: false };
    } catch (error) {
      logger.error('Failed to check IP block status', {
        ip,
        error: sanitizeError(error),
      });
      return { blocked: false };
    }
  }

  /**
   * Extract request context for rate limiting
   */
  static extractRequestContext(req: Request): {
    ip: string;
    userAgent: string;
    userId?: string;
    endpoint: string;
    method: string;
  } {
    return {
      ip: req.ip || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      userId: (req as any).user?.id,
      endpoint: req.path,
      method: req.method,
    };
  }
}
