import { Service } from 'typedi';
import { PrismaClient } from '@prisma/client';
import { 
  Subject, 
  SubjectProgress, 
  CreateSubjectRequest, 
  UpdateProgressRequest,
  SubjectResponse,
  SubjectsListResponse,
  GenerateCurriculumResponse
} from '@/interfaces/learning.interface';
import { AIService } from './ai.service';
import { HttpException } from '@/exceptions/HttpException';

@Service()
export class SubjectsService {
  private prisma = new PrismaClient();

  constructor(private aiService: AIService) {}

  async createSubject(userId: string, subjectData: CreateSubjectRequest): Promise<Subject> {
    try {
      const subject = await this.prisma.subject.create({
        data: {
          title: subjectData.title,
          description: subjectData.description,
          language: subjectData.language || 'English',
          userId,
        },
      });

      return subject;
    } catch (error) {
      console.error('Error creating subject:', error);
      throw new HttpException(500, 'Failed to create subject');
    }
  }

  async getSubjects(
    userId: string, 
    page: number = 1, 
    limit: number = 10,
    search?: string,
    language?: string
  ): Promise<SubjectsListResponse> {
    try {
      const skip = (page - 1) * limit;
      
      const where: any = { userId };
      
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ];
      }
      
      if (language) {
        where.language = language;
      }

      const [subjects, total] = await Promise.all([
        this.prisma.subject.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            progress: {
              where: { userId },
              orderBy: { lastAccessed: 'desc' }
            }
          }
        }),
        this.prisma.subject.count({ where })
      ]);

      return {
        subjects: subjects.map(subject => ({
          ...subject,
          progress: subject.progress
        })),
        total
      };
    } catch (error) {
      console.error('Error fetching subjects:', error);
      throw new HttpException(500, 'Failed to fetch subjects');
    }
  }

  async getSubjectById(userId: string, subjectId: string): Promise<SubjectResponse> {
    try {
      const subject = await this.prisma.subject.findFirst({
        where: { 
          id: subjectId,
          userId 
        },
        include: {
          progress: {
            where: { userId },
            orderBy: { lastAccessed: 'desc' }
          }
        }
      });

      if (!subject) {
        throw new HttpException(404, 'Subject not found');
      }

      return {
        ...subject,
        progress: subject.progress
      };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      console.error('Error fetching subject:', error);
      throw new HttpException(500, 'Failed to fetch subject');
    }
  }

  async updateSubject(userId: string, subjectId: string, updateData: Partial<Subject>): Promise<Subject> {
    try {
      // Verify ownership
      const existingSubject = await this.prisma.subject.findFirst({
        where: { id: subjectId, userId }
      });

      if (!existingSubject) {
        throw new HttpException(404, 'Subject not found');
      }

      const subject = await this.prisma.subject.update({
        where: { id: subjectId },
        data: {
          ...updateData,
          updatedAt: new Date()
        }
      });

      return subject;
    } catch (error) {
      if (error instanceof HttpException) throw error;
      console.error('Error updating subject:', error);
      throw new HttpException(500, 'Failed to update subject');
    }
  }

  async deleteSubject(userId: string, subjectId: string): Promise<void> {
    try {
      // Verify ownership
      const existingSubject = await this.prisma.subject.findFirst({
        where: { id: subjectId, userId }
      });

      if (!existingSubject) {
        throw new HttpException(404, 'Subject not found');
      }

      await this.prisma.subject.delete({
        where: { id: subjectId }
      });
    } catch (error) {
      if (error instanceof HttpException) throw error;
      console.error('Error deleting subject:', error);
      throw new HttpException(500, 'Failed to delete subject');
    }
  }

  async generateCurriculum(userId: string, subjectId: string, subject: string, language: string = 'English'): Promise<GenerateCurriculumResponse> {
    try {
      // Verify subject ownership
      const existingSubject = await this.prisma.subject.findFirst({
        where: { id: subjectId, userId }
      });

      if (!existingSubject) {
        throw new HttpException(404, 'Subject not found');
      }

      // Generate curriculum using AI
      const curriculum = await this.aiService.generateCurriculum(subject, language);

      // Update the subject with the generated curriculum
      await this.prisma.subject.update({
        where: { id: subjectId },
        data: {
          curriculum: curriculum,
          updatedAt: new Date()
        }
      });

      return {
        curriculum,
        subject,
        language
      };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      console.error('Error generating curriculum:', error);
      throw new HttpException(500, 'Failed to generate curriculum');
    }
  }

  async updateProgress(userId: string, subjectId: string, progressData: UpdateProgressRequest): Promise<SubjectProgress> {
    try {
      // Verify subject ownership
      const existingSubject = await this.prisma.subject.findFirst({
        where: { id: subjectId, userId }
      });

      if (!existingSubject) {
        throw new HttpException(404, 'Subject not found');
      }

      // Upsert progress record
      const progress = await this.prisma.subjectProgress.upsert({
        where: {
          subjectId_userId_topicId: {
            subjectId,
            userId,
            topicId: progressData.topicId
          }
        },
        update: {
          completed: progressData.completed,
          lastAccessed: new Date(),
          updatedAt: new Date()
        },
        create: {
          subjectId,
          userId,
          topicId: progressData.topicId,
          completed: progressData.completed,
          lastAccessed: new Date()
        }
      });

      return progress;
    } catch (error) {
      console.error('Error updating progress:', error);
      throw new HttpException(500, 'Failed to update progress');
    }
  }

  async getProgress(userId: string, subjectId: string): Promise<SubjectProgress[]> {
    try {
      // Verify subject ownership
      const existingSubject = await this.prisma.subject.findFirst({
        where: { id: subjectId, userId }
      });

      if (!existingSubject) {
        throw new HttpException(404, 'Subject not found');
      }

      const progress = await this.prisma.subjectProgress.findMany({
        where: {
          subjectId,
          userId
        },
        orderBy: { lastAccessed: 'desc' }
      });

      return progress;
    } catch (error) {
      if (error instanceof HttpException) throw error;
      console.error('Error fetching progress:', error);
      throw new HttpException(500, 'Failed to fetch progress');
    }
  }

  async getSubjectStats(userId: string, subjectId: string): Promise<{
    totalTopics: number;
    completedTopics: number;
    completionPercentage: number;
    lastAccessed?: Date;
  }> {
    try {
      // Verify subject ownership
      const subject = await this.prisma.subject.findFirst({
        where: { id: subjectId, userId }
      });

      if (!subject) {
        throw new HttpException(404, 'Subject not found');
      }

      const progress = await this.prisma.subjectProgress.findMany({
        where: { subjectId, userId }
      });

      const completedTopics = progress.filter(p => p.completed).length;
      const totalTopics = progress.length;
      const completionPercentage = totalTopics > 0 ? (completedTopics / totalTopics) * 100 : 0;
      const lastAccessed = progress.length > 0 ? 
        progress.reduce((latest, current) => 
          current.lastAccessed > latest ? current.lastAccessed : latest, 
          progress[0].lastAccessed
        ) : undefined;

      return {
        totalTopics,
        completedTopics,
        completionPercentage: Math.round(completionPercentage * 100) / 100,
        lastAccessed
      };
    } catch (error) {
      if (error instanceof HttpException) throw error;
      console.error('Error fetching subject stats:', error);
      throw new HttpException(500, 'Failed to fetch subject statistics');
    }
  }
}
