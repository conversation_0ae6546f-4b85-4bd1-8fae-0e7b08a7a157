import { Role, User } from '@prisma/client';
import { Service } from 'typedi';
import { BaseService } from './base.service';
import { UserRepository, UserFilters } from '../repositories/user.repository';
import { CreateUserDto, UpdateUserDto } from '../dtos/user.dto';
import { HttpException } from '../exceptions/HttpException';
import { PasswordUtils } from '../utils/password';
import { PaginationResult } from '../types/template';

export interface IUserSafe {
  id: string;
  email: string | null;
  role: Role;
  isEmailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

@Service()
export class UserService extends BaseService {
  async createUser(userData: CreateUserDto): Promise<IUserSafe> {
    this.logInfo('Creating new user', { email: userData.email, username: userData.username });

    if (userData.password) {
      const passwordValidation = PasswordUtils.validatePasswordStrength(userData.password);
      if (!passwordValidation.isValid) {
        throw new HttpException(
          400,
          `Password does not meet security requirements: ${passwordValidation.feedback.join(', ')}`,
        );
      }
    }

    if (userData.email) {
      const existingUser = await UserRepository.findByEmail(userData.email);
      if (existingUser) {
        throw new HttpException(409, 'User with this email already exists');
      }
    }
    if (userData.username) {
      const existingUser = await UserRepository.findByUsername(userData.username);
      if (existingUser) {
        throw new HttpException(409, 'User with this username already exists');
      }
    }

    const passwordHash = userData.password ? await PasswordUtils.hash(userData.password) : undefined;

    const user = await UserRepository.create({
      email: userData.email!,
      username: userData.username,
      passwordHash: passwordHash!,
    });

    this.logInfo('User created successfully', { id: user.id, email: user.email });
    return this.sanitizeUser(user);
  }

  async findUserById(id: string): Promise<IUserSafe | null> {
    this.logInfo('Finding user by ID', { id });
    const user = await UserRepository.findById(id);
    return user ? this.sanitizeUser(user) : null;
  }

  async findAllUser(filters: UserFilters, page: number, limit: number): Promise<PaginationResult<IUserSafe>> {
    this.logInfo('Finding all users', { filters, page, limit });
    const paginationResult = await UserRepository.findMany(filters, page, limit);
    return {
      ...paginationResult,
      data: paginationResult.data.map(this.sanitizeUser),
    };
  }

  async updateUser(id: string, userData: UpdateUserDto): Promise<IUserSafe> {
    this.logInfo('Updating user', { id, ...userData });

    const updatedUser = await UserRepository.update(id, userData);
    return this.sanitizeUser(updatedUser);
  }

  async deleteUser(id: string): Promise<User> {
    this.logInfo('Deleting user', { id });
    return UserRepository.delete(id);
  }

  private sanitizeUser(user: User): IUserSafe {
    const { id, email, role, emailVerified, createdAt, updatedAt } = user;
    return { id, email, role, isEmailVerified: !!emailVerified, createdAt, updatedAt };
  }
}
