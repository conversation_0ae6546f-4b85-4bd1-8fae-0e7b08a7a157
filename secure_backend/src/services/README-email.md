# Email Service Documentation

## Overview

The `EmailService` class provides a robust, reusable wrapper around **node-mailjet** for sending email verification messages. It includes centralized error handling, retries, template management, and comprehensive logging.

## Features

- ✅ **Mailjet Integration**: Secure email delivery via Mailjet API
- ✅ **HTML & Text Templates**: Supports both HTML and plain text email templates
- ✅ **Template Caching**: File-based template loading with in-memory caching
- ✅ **Retry Logic**: Configurable retry mechanism with exponential backoff
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Security**: Sanitized logging to prevent information leakage
- ✅ **Verification Emails**: Support for both user registration and email change verification
- ✅ **Health Checks**: Connection testing for monitoring

## Setup

### Environment Variables

Ensure the following environment variables are configured:

```bash
# Required
MAILJET_API_KEY=your_mailjet_api_key
MAILJET_API_SECRET=your_mailjet_api_secret
MAIL_FROM=<EMAIL>
MAIL_FROM_NAME="Your App Name"

# Optional
EMAIL_VERIFICATION_TOKEN_EXPIRES=48h
ORIGIN=https://yourapp.com
```

### Configuration Validation

The service validates required configuration on startup:

```typescript
import { EmailService } from './services/email.service';

// Validate configuration before starting your app
EmailService.validateConfiguration();
```

## Usage

### Basic Email Verification

```typescript
import { EmailService, createEmailService } from './services/email.service';

// Create service instance (or inject via TypeDI)
const emailService = createEmailService();

// Send verification email for new user registration
const user = {
  id: 'user-123',
  email: '<EMAIL>',
  role: 'USER',
  createdAt: new Date(),
  updatedAt: new Date(),
};

const verificationToken = 'your-jwt-token-here';

const result = await emailService.sendVerificationEmail(user, verificationToken);

if (result.success) {
  console.log(`Email sent successfully: ${result.messageId}`);
} else {
  console.error(`Email sending failed: ${result.error}`);
}
```

### Email Change Verification

```typescript
// Send verification email for email address change
const result = await emailService.sendVerificationEmail(
  user, 
  verificationToken, 
  true // isEmailChange = true
);
```

### Advanced Options

```typescript
// Send with custom options
const result = await emailService.sendVerificationEmail(
  user,
  verificationToken,
  false, // isEmailChange
  {
    retries: 5,              // Custom retry count (default: 3)
    trackOpens: true,        // Track email opens (default: true)
    trackClicks: true,       // Track link clicks (default: true)
  }
);
```

## Templates

### Template Structure

Templates are located in `src/emails/templates/` and support variable substitution:

- `verification.html` / `verification.txt` - New user email verification
- `email-change.html` / `email-change.txt` - Email address change verification

### Template Variables

All templates support these variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `{{APP_NAME}}` | Application name | "Your App Name" |
| `{{USER_EMAIL}}` | User's email address | "<EMAIL>" |
| `{{VERIFICATION_URL}}` | Complete verification URL | "https://app.com/auth/verify-email?token=..." |
| `{{VERIFICATION_TITLE}}` | Email subject/title | "Email Verification" |
| `{{VERIFICATION_MESSAGE}}` | Context-specific message | "Thank you for registering..." |
| `{{EXPIRES_IN}}` | Token expiration time | "48 hours" |
| `{{CURRENT_YEAR}}` | Current year | "2024" |

### Custom Templates

To add new templates:

1. Create `templatename.html` and `templatename.txt` files in `src/templates/email/`
2. Use `{{VARIABLE_NAME}}` syntax for substitutions
3. Load templates using `emailService.getTemplate('templatename')`

## API Reference

### EmailService Class

#### Methods

##### `sendVerificationEmail(user, token, isEmailChange?, options?)`

Sends a verification email to the user.

**Parameters:**
- `user: BaseUserData` - User object containing id, email, role, etc.
- `token: string` - JWT verification token
- `isEmailChange: boolean` - Whether this is for email change (default: false)
- `options: EmailSendOptions` - Optional sending configuration

**Returns:** `Promise<EmailResult>`

##### `testConnection()`

Tests the connection to Mailjet API.

**Returns:** `Promise<boolean>`

##### `clearTemplateCache()`

Clears the in-memory template cache.

##### `getTemplateCacheStats()`

Returns cache statistics.

**Returns:** `{ cacheSize: number; cachedTemplates: string[] }`

#### Static Methods

##### `validateConfiguration()`

Validates required environment variables. Throws error if configuration is invalid.

### Type Definitions

```typescript
interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

interface EmailSendOptions {
  retries?: number;
  priority?: 'low' | 'normal' | 'high';
  trackOpens?: boolean;
  trackClicks?: boolean;
}
```

## Error Handling

The service implements comprehensive error handling:

- **Configuration Errors**: Thrown at startup if required environment variables are missing
- **Template Errors**: Thrown if template files are missing or corrupted
- **Network Errors**: Handled with retry logic and eventual failure reporting
- **Validation Errors**: Thrown for invalid input parameters

All errors are logged using the secure logging system with sanitized output.

## Retry Logic

Failed email sends are automatically retried with:
- Default: 3 attempts
- Exponential backoff: 1s, 2s, 3s delays
- Configurable via `options.retries`

## Logging

All operations are logged with appropriate levels:
- **Info**: Successful operations, template loading
- **Warn**: Retry attempts, recoverable errors
- **Error**: Final failures, configuration issues

Logs are sanitized to prevent sensitive information leakage.

## Health Monitoring

Use `testConnection()` for health checks:

```typescript
// In your health check endpoint
app.get('/health/email', async (req, res) => {
  const isHealthy = await emailService.testConnection();
  res.status(isHealthy ? 200 : 503).json({
    service: 'email',
    status: isHealthy ? 'healthy' : 'unhealthy'
  });
});
```

## Development

### Testing

Run the email service tests:

```bash
npm test src/tests/services/email.service.test.ts
```

### Template Development

1. Clear template cache during development:
   ```typescript
   emailService.clearTemplateCache();
   ```

2. Check cache statistics:
   ```typescript
   console.log(emailService.getTemplateCacheStats());
   ```

## Security Considerations

- Environment variables are validated and required
- Email addresses and tokens are sanitized in logs
- Templates are loaded from filesystem (not user input)
- Mailjet API credentials are securely managed
- All user data is sanitized before logging

## Troubleshooting

### Common Issues

1. **"Missing required email configuration"**
   - Ensure all required environment variables are set
   - Run `EmailService.validateConfiguration()` to identify missing variables

2. **"Email template loading failed"**
   - Check that template files exist in `src/templates/email/`
   - Verify file permissions and paths

3. **"Email send failed after all retries"**
   - Check Mailjet API credentials
   - Verify network connectivity
   - Check Mailjet service status

4. **Template variables not replaced**
   - Ensure variable names match exactly (case-sensitive)
   - Check template syntax uses `{{VARIABLE_NAME}}` format

### Debug Mode

Enable detailed logging by setting log level to debug in your environment configuration.
