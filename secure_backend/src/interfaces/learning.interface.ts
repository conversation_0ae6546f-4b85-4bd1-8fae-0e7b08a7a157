export interface Subject {
  id: string;
  title: string;
  description: string;
  language: string;
  userId: string;
  curriculum?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface SubjectProgress {
  id: string;
  subjectId: string;
  userId: string;
  topicId: string;
  completed: boolean;
  lastAccessed: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Course {
  id: string;
  title: string;
  description: string;
  language: string;
  userId: string;
  difficulty: string;
  duration: string;
  tags: string[];
  content?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface CourseProgress {
  id: string;
  courseId: string;
  userId: string;
  moduleId: string;
  topicId: string;
  completed: boolean;
  lastAccessed: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UniversityCourse {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: string;
  prerequisites: string[];
  learningOutcomes: string[];
  userSketch: string;
  language: string;
  userId: string;
  modules?: UniversityCourseModule[];
  createdAt: Date;
  updatedAt: Date;
}

export interface UniversityCourseModule {
  id: string;
  courseId: string;
  title: string;
  description: string;
  duration: string;
  moduleOrder: number;
  prerequisites: string[];
  learningObjectives: string[];
  curriculumStructure: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface UniversityCourseProgress {
  id: string;
  courseId: string;
  userId: string;
  moduleId: string;
  topicId: string;
  completed: boolean;
  lastAccessed: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Request/Response interfaces
export interface CreateSubjectRequest {
  title: string;
  description: string;
  language?: string;
}

export interface GenerateCurriculumRequest {
  subject: string;
  language?: string;
}

export interface CreateCourseRequest {
  title: string;
  description: string;
  difficulty: string;
  duration: string;
  tags?: string[];
  language?: string;
}

export interface GenerateUniversityCourseRequest {
  userSketch: string;
  language?: string;
}

export interface CreateUniversityCourseRequest {
  title: string;
  description: string;
  duration: string;
  difficulty: string;
  prerequisites?: string[];
  learningOutcomes: string[];
  userSketch: string;
  modules: CreateUniversityCourseModuleRequest[];
  language?: string;
}

export interface CreateUniversityCourseModuleRequest {
  title: string;
  description: string;
  duration: string;
  order: number;
  prerequisites?: string[];
  learningObjectives: string[];
  curriculum: any;
}

export interface UpdateProgressRequest {
  topicId: string;
  completed: boolean;
}

// Curriculum structure interfaces
export interface CurriculumTopic {
  id: string;
  title: string;
  description?: string;
  estimatedTime?: string;
  difficulty?: string;
  resources?: string[];
}

export interface CurriculumCategory {
  id: string;
  title: string;
  description?: string;
  topics: CurriculumTopic[];
}

export interface Curriculum {
  curriculum: CurriculumCategory[];
}

// AI Generation interfaces
export interface PersonaSelection {
  persona: string;
  reasoning: string;
  curriculumPrompt: string;
}

export interface CourseAnalysis {
  courseInfo: {
    title: string;
    description: string;
    duration: string;
    difficulty: string;
    prerequisites?: string[];
    learningOutcomes: string[];
  };
  modules: {
    id: string;
    title: string;
    description: string;
    duration: string;
    order: number;
    prerequisites?: string[];
    learningObjectives: string[];
    curriculum: CurriculumCategory[];
  }[];
  reasoning: string;
}

// Response interfaces
export interface SubjectResponse extends Subject {
  progress?: SubjectProgress[];
}

export interface CourseResponse extends Course {
  progress?: CourseProgress[];
}

export interface UniversityCourseResponse extends UniversityCourse {
  modules: UniversityCourseModule[];
  progress?: UniversityCourseProgress[];
}

export interface GenerateCurriculumResponse {
  curriculum: Curriculum;
  subject: string;
  language: string;
}

export interface GenerateUniversityCourseResponse {
  course: CourseAnalysis;
}

export interface SubjectsListResponse {
  subjects: SubjectResponse[];
  total: number;
}

export interface CoursesListResponse {
  courses: CourseResponse[];
  total: number;
}

export interface UniversityCoursesListResponse {
  courses: UniversityCourseResponse[];
  total: number;
}
