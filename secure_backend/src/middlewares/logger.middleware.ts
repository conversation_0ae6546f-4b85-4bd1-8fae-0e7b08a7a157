import morgan from 'morgan';
import { stream } from '../utils/secureLogger';
import { NODE_ENV } from '../config';

// Secure Morgan configuration that doesn't log response bodies
const secureFormat =
  NODE_ENV === 'production'
    ? 'combined' // Standard Apache combined format for production
    : ':method :url :status :res[content-length] - :response-time ms'; // Minimal dev format

// Custom morgan configuration with security considerations
const loggerMiddleware = morgan(secureFormat, {
  stream,
  // Skip logging for sensitive endpoints in production
  skip: (req, res) => {
    if (NODE_ENV === 'production' && req.url && req.url.includes('/auth/')) {
      // Only log failed auth attempts in production
      return res.statusCode < 400;
    }
    return false;
  },
});

export default loggerMiddleware;
