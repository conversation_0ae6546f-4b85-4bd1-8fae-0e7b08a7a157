import { Request, Response, NextFunction } from 'express';
import { logger, redactSensitiveData } from '../utils/secureLogger';
import { NODE_ENV } from '../config';

/**
 * Middleware to safely log HTTP responses without exposing sensitive data
 */
export const responseLoggerMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Skip response logging in production to avoid any potential leaks
  if (NODE_ENV === 'production') {
    return next();
  }

  // Store original send method
  const originalSend = res.send;

  // Override send method to intercept and sanitize response data
  res.send = function (this: Response, body?: unknown): Response {
    // Only log response bodies for non-auth endpoints or safe methods
    const isAuthEndpoint = req.path.includes('/auth/');
    const isSafeMethod = ['GET', 'HEAD', 'OPTIONS'].includes(req.method);

    if (isAuthEndpoint && !isSafeMethod) {
      // For auth endpoints, log minimal safe information
      logger.info(`Auth response sent`, {
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        contentType: res.get('Content-Type'),
        contentLength: body ? JSON.stringify(body).length : 0,
        // Don't log the actual response body for auth endpoints
      });
    } else if (NODE_ENV === 'development') {
      // For non-auth endpoints in development, log sanitized response
      try {
        const sanitizedBody =
          typeof body === 'string' ? redactSensitiveData(body) : redactSensitiveData(JSON.stringify(body));

        logger.info(`Response sent`, {
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
          body: sanitizedBody.substring(0, 500), // Limit log length
        });
      } catch {
        logger.info(`Response sent (body not loggable)`, {
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
        });
      }
    }

    // Call original send method
    return originalSend.call(this, body);
  };

  next();
};

export default responseLoggerMiddleware;
