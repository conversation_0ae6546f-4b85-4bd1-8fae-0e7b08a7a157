import { Request, Response, NextFunction } from 'express';
import csrf from 'csrf';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';
import { NODE_ENV } from '../config';

/**
 * CSRF Protection Middleware
 * Implements double-submit cookie pattern for CSRF protection
 * More secure and modern alternative to deprecated csurf package
 */
export class CSRFProtection {
  private static tokens = new csrf();
  private static readonly CSRF_COOKIE_NAME = '_csrf';
  private static readonly CSRF_HEADER_NAME = 'x-csrf-token';
  private static readonly CSRF_BODY_FIELD = '_csrf';

  /**
   * Generate CSRF token and set secure cookie
   */
  static generateToken(req: Request, res: Response): string {
    try {
      // Generate a secret for this session if not exists
      if (!req.session?.csrfSecret) {
        if (!req.session) {
          throw new HttpException(500, 'Session not initialized');
        }
        req.session.csrfSecret = this.tokens.secretSync();
      }

      // Generate token using the secret
      const token = this.tokens.create(req.session.csrfSecret);

      // Set secure cookie with the token
      res.cookie(this.CSRF_COOKIE_NAME, token, {
        httpOnly: false, // Must be accessible to JavaScript for AJAX requests
        secure: NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        path: '/',
      });

      return token;
    } catch (error) {
      logger.error('Failed to generate CSRF token', { error });
      throw new HttpException(500, 'Failed to generate CSRF token');
    }
  }

  /**
   * Verify CSRF token from request
   */
  static verifyToken(req: Request): boolean {
    try {
      const secret = req.session?.csrfSecret;
      if (!secret) {
        return false;
      }

      // Get token from header, body, or query parameter
      const token =
        (req.headers[this.CSRF_HEADER_NAME] as string) ||
        req.body?.[this.CSRF_BODY_FIELD] ||
        (req.query?.[this.CSRF_BODY_FIELD] as string);

      if (!token) {
        return false;
      }

      // Verify token against secret
      return this.tokens.verify(secret, token);
    } catch (error) {
      logger.warn('CSRF token verification failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      return false;
    }
  }

  /**
   * Middleware to generate CSRF token for GET requests
   */
  static generateMiddleware = (req: Request, res: Response, next: NextFunction): void => {
    if (req.method === 'GET') {
      try {
        const token = CSRFProtection.generateToken(req, res);
        // Add token to response locals for template rendering
        res.locals['csrfToken'] = token;
      } catch (error) {
        return next(error);
      }
    }
    next();
  };

  /**
   * Middleware to verify CSRF token for state-changing requests
   */
  static verifyMiddleware = (req: Request, _res: Response, next: NextFunction): void => {
    // Skip verification for safe methods
    if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      return next();
    }

    // Skip verification for API endpoints that use Bearer tokens (already protected)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return next();
    }

    // Verify CSRF token
    if (!CSRFProtection.verifyToken(req)) {
      logger.warn('CSRF token verification failed', {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        hasToken: !!(req.headers[this.CSRF_HEADER_NAME] || req.body?.[this.CSRF_BODY_FIELD]),
        hasSecret: !!req.session?.csrfSecret,
      });

      return next(new HttpException(403, 'Invalid CSRF token'));
    }

    next();
  };

  /**
   * API endpoint to get CSRF token
   */
  static getTokenEndpoint = (req: Request, res: Response, next: NextFunction): void => {
    try {
      const token = CSRFProtection.generateToken(req, res);
      res.json({
        success: true,
        csrfToken: token,
        message: 'CSRF token generated successfully',
      });
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Express session configuration for CSRF
 */
export const sessionConfig = {
  secret: process.env['SESSION_SECRET'] || 'your-session-secret-change-in-production',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    sameSite: 'strict' as const,
  },
};

// Export middleware functions
export const generateCSRFToken = CSRFProtection.generateMiddleware;
export const verifyCSRFToken = CSRFProtection.verifyMiddleware;
export const getCSRFToken = CSRFProtection.getTokenEndpoint;

export default CSRFProtection;
