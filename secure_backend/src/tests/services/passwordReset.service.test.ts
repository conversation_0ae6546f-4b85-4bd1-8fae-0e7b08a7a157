import { PasswordResetService } from '../../services/passwordReset.service';
import { UserRepository } from '../../repositories/user.repository';
import { EmailService } from '../../services/email.service';
import { PasswordUtils } from '../../utils/password';
import { HttpException } from '../../exceptions/HttpException';

// Mock dependencies
jest.mock('../../repositories/user.repository');
jest.mock('../../services/email.service');
jest.mock('../../utils/password');
jest.mock('../../loaders/prisma', () => ({
  prisma: {
    passwordResetToken: {
      updateMany: jest.fn(),
      create: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
    },
  },
}));

const mockUserRepository = UserRepository as jest.Mocked<typeof UserRepository>;
const mockEmailService = EmailService as jest.MockedClass<typeof EmailService>;
const mockPasswordUtils = PasswordUtils as jest.Mocked<typeof PasswordUtils>;

// Import the mocked prisma
import { prisma } from '../../loaders/prisma';
const mockPrismaPasswordResetToken = prisma.passwordResetToken as jest.Mocked<typeof prisma.passwordResetToken>;

describe('PasswordResetService', () => {
  let passwordResetService: PasswordResetService;
  let mockEmailServiceInstance: jest.Mocked<EmailService>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock EmailService instance
    mockEmailServiceInstance = {
      sendPasswordResetEmail: jest.fn(),
    } as any;

    mockEmailService.mockImplementation(() => mockEmailServiceInstance);

    passwordResetService = new PasswordResetService();

    // Set NODE_ENV to test to skip email sending
    process.env['NODE_ENV'] = 'test';
  });

  afterEach(() => {
    delete process.env['NODE_ENV'];
  });

  describe('initiatePasswordReset', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      passwordHash: 'hashed-password',
      username: 'testuser',
    };

    it('should successfully initiate password reset for valid email', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(mockUser as any);
      mockPrismaPasswordResetToken.updateMany.mockResolvedValue({ count: 0 });
      mockPrismaPasswordResetToken.create.mockResolvedValue({
        id: 'token-123',
        userId: mockUser.id,
        token: 'hashed-token',
        expires: new Date(),
        isUsed: false,
        requestedAt: new Date(),
        usedAt: null,
        ipAddress: '127.0.0.1',
        userAgent: 'test',
      });

      // Act
      const result = await passwordResetService.initiatePasswordReset('<EMAIL>', {
        ip: '127.0.0.1',
        userAgent: 'test',
      });

      // Assert
      expect(result.success).toBe(true);
      expect(result.message).toBe('If an account with that email exists, you will receive a password reset link.');
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockPrismaPasswordResetToken.updateMany).toHaveBeenCalled();
      expect(mockPrismaPasswordResetToken.create).toHaveBeenCalled();
    });

    it('should successfully initiate password reset for valid username', async () => {
      // Arrange
      mockUserRepository.findByUsername.mockResolvedValue(mockUser as any);
      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockPrismaPasswordResetToken.updateMany.mockResolvedValue({ count: 0 });
      mockPrismaPasswordResetToken.create.mockResolvedValue({
        id: 'token-123',
        userId: mockUser.id,
        token: 'hashed-token',
        expires: new Date(),
        isUsed: false,
        requestedAt: new Date(),
        usedAt: null,
        ipAddress: '127.0.0.1',
        userAgent: 'test',
      });

      // Act
      const result = await passwordResetService.initiatePasswordReset('testuser', {
        ip: '127.0.0.1',
        userAgent: 'test',
      });

      // Assert
      expect(result.success).toBe(true);
      expect(mockUserRepository.findByUsername).toHaveBeenCalledWith('testuser');
      expect(mockPrismaPasswordResetToken.create).toHaveBeenCalled();
    });

    it('should return success message even for non-existent user (prevent user enumeration)', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null);

      // Act
      const result = await passwordResetService.initiatePasswordReset('<EMAIL>');

      // Assert
      expect(result.success).toBe(true);
      expect(result.message).toBe('If an account with that email exists, you will receive a password reset link.');
      expect(mockPrismaPasswordResetToken.create).not.toHaveBeenCalled();
    });

    it('should return success message for OAuth-only users', async () => {
      // Arrange
      const oauthUser = { ...mockUser, email: '<EMAIL>', passwordHash: null };
      mockUserRepository.findByEmail.mockResolvedValue(oauthUser as any);

      // Act
      const result = await passwordResetService.initiatePasswordReset('<EMAIL>');

      // Assert
      expect(result.success).toBe(true);
      expect(result.message).toBe('If an account with that email exists, you will receive a password reset link.');
      expect(mockPrismaPasswordResetToken.create).not.toHaveBeenCalled();
    });
  });

  describe('resetPassword', () => {
    const mockResetToken = {
      id: 'token-123',
      userId: 'user-123',
      token: 'hashed-token',
      expires: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
      isUsed: false,
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        passwordHash: 'old-hashed-password',
      },
    };

    it('should successfully reset password with valid token', async () => {
      // Arrange
      mockPrismaPasswordResetToken.findFirst.mockResolvedValue(mockResetToken as any);
      mockPasswordUtils.compare.mockResolvedValue(false); // New password is different
      mockPasswordUtils.hash.mockResolvedValue('new-hashed-password');
      mockUserRepository.update.mockResolvedValue({} as any);
      mockPrismaPasswordResetToken.update.mockResolvedValue({} as any);

      // Act
      const result = await passwordResetService.resetPassword('valid-token', 'NewPassword123!');

      // Assert
      expect(result.success).toBe(true);
      expect(result.message).toBe('Password has been reset successfully. You can now log in with your new password.');
      expect(mockPasswordUtils.hash).toHaveBeenCalledWith('NewPassword123!');
      expect(mockUserRepository.update).toHaveBeenCalledWith('user-123', {
        passwordHash: 'new-hashed-password',
        failedLoginAttempts: 0,
        lockoutExpires: null,
      });
      expect(mockPrismaPasswordResetToken.update).toHaveBeenCalledWith({
        where: { id: 'token-123' },
        data: { isUsed: true, usedAt: expect.any(Date) },
      });
    });

    it('should throw error for invalid token', async () => {
      // Arrange
      mockPrismaPasswordResetToken.findFirst.mockResolvedValue(null);

      // Act & Assert
      await expect(passwordResetService.resetPassword('invalid-token', 'NewPassword123!')).rejects.toThrow(
        HttpException,
      );
    });

    it('should throw error for expired token', async () => {
      // Arrange
      // Mock findFirst to return null for expired tokens (this is how the service filters them)
      mockPrismaPasswordResetToken.findFirst.mockResolvedValue(null);

      // Act & Assert
      await expect(passwordResetService.resetPassword('expired-token', 'NewPassword123!')).rejects.toThrow(
        HttpException,
      );
    });

    it('should throw error for used token', async () => {
      // Arrange
      // Mock findFirst to return null for used tokens (this is how the service filters them)
      mockPrismaPasswordResetToken.findFirst.mockResolvedValue(null);

      // Act & Assert
      await expect(passwordResetService.resetPassword('used-token', 'NewPassword123!')).rejects.toThrow(HttpException);
    });

    it('should throw error if new password is same as current', async () => {
      // Arrange
      mockPrismaPasswordResetToken.findFirst.mockResolvedValue(mockResetToken as any);
      mockPasswordUtils.compare.mockResolvedValue(true); // Same password

      // Act & Assert
      await expect(passwordResetService.resetPassword('valid-token', 'SamePassword123!')).rejects.toThrow(
        HttpException,
      );
    });

    it('should throw error for weak password', async () => {
      // Arrange
      mockPrismaPasswordResetToken.findFirst.mockResolvedValue(mockResetToken as any);

      // Act & Assert
      await expect(passwordResetService.resetPassword('valid-token', 'weak')).rejects.toThrow(HttpException);
    });
  });

  describe('validateResetToken', () => {
    it('should return valid true for valid token', async () => {
      // Arrange
      const validToken = {
        id: 'token-123',
        expires: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
        isUsed: false,
      };
      mockPrismaPasswordResetToken.findFirst.mockResolvedValue(validToken as any);

      // Act
      const result = await passwordResetService.validateResetToken('valid-token');

      // Assert
      expect(result.valid).toBe(true);
      expect(result.expired).toBeUndefined();
    });

    it('should return valid false for non-existent token', async () => {
      // Arrange
      mockPrismaPasswordResetToken.findFirst.mockResolvedValue(null);

      // Act
      const result = await passwordResetService.validateResetToken('invalid-token');

      // Assert
      expect(result.valid).toBe(false);
    });

    it('should return valid false and expired true for expired token', async () => {
      // Arrange
      const expiredToken = {
        id: 'token-123',
        expires: new Date(Date.now() - 60 * 1000), // 1 minute ago
        isUsed: false,
      };
      mockPrismaPasswordResetToken.findFirst.mockResolvedValue(expiredToken as any);

      // Act
      const result = await passwordResetService.validateResetToken('expired-token');

      // Assert
      expect(result.valid).toBe(false);
      expect(result.expired).toBe(true);
    });
  });
});
