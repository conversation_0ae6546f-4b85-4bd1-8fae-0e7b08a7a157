import 'reflect-metadata';
import dotenv from 'dotenv';
import path from 'path';

// Load test environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env.test.local') });
dotenv.config({ path: path.resolve(process.cwd(), '.env.test') });

// Set default test environment variables
process.env['NODE_ENV'] = 'test';
process.env['PORT'] = '3001';
process.env['JWT_SECRET'] = 'test_jwt_secret_key_for_testing_only';
process.env['JWT_EXPIRES_IN'] = '1h';
process.env['BCRYPT_SALT_ROUNDS'] = '4'; // Lower for faster tests
process.env['DATABASE_URL'] = process.env['DATABASE_URL'] || 'postgresql://test:test@localhost:5432/test_db';
process.env['LOG_LEVEL'] = 'error'; // Reduce logging noise in tests
process.env['RATE_LIMIT_MAX'] = '1000'; // Higher limit for tests
process.env['RATE_LIMIT_WINDOW_MS'] = '60000';
process.env['ORIGIN'] = 'http://localhost:3001';
process.env['CREDENTIALS'] = 'true';

// Email service test environment variables
process.env['MAILJET_API_KEY'] = 'test_mailjet_api_key';
process.env['MAILJET_API_SECRET'] = 'test_mailjet_api_secret';
process.env['MAIL_FROM'] = '<EMAIL>';
process.env['MAIL_FROM_NAME'] = 'Test App';
process.env['EMAIL_VERIFICATION_TOKEN_EXPIRES'] = '48h';

// OAuth test environment variables
process.env['OAUTH_GOOGLE_CLIENT_ID'] = 'test_google_client_id';
process.env['OAUTH_GOOGLE_CLIENT_SECRET'] = 'test_google_client_secret';
process.env['OAUTH_FACEBOOK_CLIENT_ID'] = 'test_facebook_client_id';
process.env['OAUTH_FACEBOOK_CLIENT_SECRET'] = 'test_facebook_client_secret';
process.env['OAUTH_GITHUB_CLIENT_ID'] = 'test_github_client_id';
process.env['OAUTH_GITHUB_CLIENT_SECRET'] = 'test_github_client_secret';
process.env['OAUTH_REDIRECT_BASE_URL'] = 'http://localhost:3001';
process.env['OAUTH_SUCCESS_REDIRECT'] = '/dashboard';
process.env['OAUTH_FAILURE_REDIRECT'] = '/login?error=oauth_failed';

// Global test setup
beforeAll(async () => {
  // Setup database connection or mock services
  // Initialize test database if needed
});

afterAll(async () => {
  // Cleanup resources
  // Close database connections
  // Clear test data
});

// Mock Redis globally for all tests
jest.mock('../config/redis', () => ({
  redis: {
    setex: jest.fn().mockResolvedValue('OK'),
    get: jest.fn().mockResolvedValue(null),
    set: jest.fn().mockResolvedValue('OK'),
    del: jest.fn().mockResolvedValue(1),
    keys: jest.fn().mockResolvedValue([]),
    incr: jest.fn().mockResolvedValue(1),
    expire: jest.fn().mockResolvedValue(1),
    ping: jest.fn().mockResolvedValue('PONG'),
    exists: jest.fn().mockResolvedValue(0),
    ttl: jest.fn().mockResolvedValue(-1),
    scan: jest.fn().mockResolvedValue(['0', []]),
    hgetall: jest.fn().mockResolvedValue({}),
    hset: jest.fn().mockResolvedValue(1),
    hdel: jest.fn().mockResolvedValue(1),
    quit: jest.fn().mockResolvedValue('OK'),
    flushall: jest.fn().mockResolvedValue('OK'),
    flushdb: jest.fn().mockResolvedValue('OK'),
    pipeline: jest.fn(() => ({
      del: jest.fn(),
      exec: jest.fn().mockResolvedValue([]),
    })),
  },
  RedisConfig: {
    getInstance: jest.fn(() => ({
      setex: jest.fn().mockResolvedValue('OK'),
      get: jest.fn().mockResolvedValue(null),
      set: jest.fn().mockResolvedValue('OK'),
      del: jest.fn().mockResolvedValue(1),
      keys: jest.fn().mockResolvedValue([]),
      incr: jest.fn().mockResolvedValue(1),
      expire: jest.fn().mockResolvedValue(1),
      ping: jest.fn().mockResolvedValue('PONG'),
      exists: jest.fn().mockResolvedValue(0),
      ttl: jest.fn().mockResolvedValue(-1),
      scan: jest.fn().mockResolvedValue(['0', []]),
      hgetall: jest.fn().mockResolvedValue({}),
      hset: jest.fn().mockResolvedValue(1),
      hdel: jest.fn().mockResolvedValue(1),
      quit: jest.fn().mockResolvedValue('OK'),
      flushall: jest.fn().mockResolvedValue('OK'),
      flushdb: jest.fn().mockResolvedValue('OK'),
      pipeline: jest.fn(() => ({
        del: jest.fn(),
        exec: jest.fn().mockResolvedValue([]),
      })),
    })),
    isHealthy: jest.fn().mockResolvedValue(true),
    close: jest.fn().mockResolvedValue(undefined),
    getConnectionStatus: jest.fn().mockReturnValue({ connected: true, instance: true }),
  },
}));

// Mock rate-limiter-flexible globally for all tests
jest.mock('rate-limiter-flexible', () => ({
  RateLimiterRedis: jest.fn().mockImplementation(() => ({
    consume: jest.fn().mockResolvedValue({}),
    get: jest.fn().mockResolvedValue({ remainingPoints: 10, msBeforeNext: 0 }),
  })),
}));

// Mock Mailjet globally for all tests
jest.mock('node-mailjet', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      post: jest.fn().mockReturnValue({
        request: jest.fn().mockResolvedValue({
          body: {
            Messages: [{ MessageID: 'test-message-id-123' }],
          },
        }),
      }),
      get: jest.fn().mockReturnValue({
        request: jest.fn().mockResolvedValue({ body: { Data: [] } }),
      }),
    })),
  };
});

// Mock Prisma globally for all tests
jest.mock('../loaders/prisma', () => ({
  __esModule: true,
  prisma: {
    $connect: jest.fn().mockResolvedValue(undefined),
    $disconnect: jest.fn().mockResolvedValue(undefined),
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    account: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    session: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    permission: {
      findMany: jest.fn().mockResolvedValue([]),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    role: {
      findMany: jest.fn().mockResolvedValue([]),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    userPermission: {
      findMany: jest.fn().mockResolvedValue([]),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    rolePermission: {
      findMany: jest.fn().mockResolvedValue([]),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  },
}));

// Mock secure logger globally for all tests
jest.mock('../utils/secureLogger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  },
}));

// Mock CSRF middleware globally for all tests
jest.mock('../middlewares/csrf.middleware', () => ({
  generateCSRFToken: jest.fn((req: any, _res: any, next: any) => {
    req.csrfToken = () => 'test-csrf-token';
    next();
  }),
  verifyCSRFToken: jest.fn((_req: any, _res: any, next: any) => next()),
  getCSRFToken: jest.fn((_req: any, res: any) => {
    res.json({ csrfToken: 'test-csrf-token' });
  }),
  sessionConfig: {
    secret: 'test-session-secret',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, httpOnly: true, maxAge: 24 * 60 * 60 * 1000 },
  },
}));

// Mock session versioning middleware globally for all tests
jest.mock('../middlewares/sessionVersioning.middleware', () => ({
  validateSessionVersion: jest.fn((_req: any, _res: any, next: any) => next()),
}));

// Global test utilities
beforeEach(() => {
  // Reset any mocks or test state
  jest.clearAllMocks();
});

// Suppress console logs during tests unless LOG_LEVEL=debug
// Temporarily disabled for debugging
// if (process.env.LOG_LEVEL !== 'debug') {
//   global.console = {
//     ...console,
//     log: jest.fn(),
//     info: jest.fn(),
//     warn: jest.fn(),
//     error: jest.fn(),
//   };
// }
