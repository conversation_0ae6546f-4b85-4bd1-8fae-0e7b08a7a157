import request from 'supertest';
import express from 'express';

// Reset modules to ensure clean mocking
jest.resetModules();

// Mock the logger first
jest.mock('../../utils/logger', () => ({
  logger: {
    warn: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock isomorphic-dompurify with a more comprehensive mock
jest.mock('isomorphic-dompurify', () => ({
  __esModule: true,
  default: {
    sanitize: jest.fn((input: string, options?: any) => {
      // console.log('DEBUG: Mock DOMPurify.sanitize called with:', input);

      // If ALLOWED_TAGS is empty and KEEP_CONTENT is true, remove all HTML tags but keep text content
      if (options && options.ALLOWED_TAGS && options.ALLOWED_TAGS.length === 0 && options.KEEP_CONTENT) {
        // Remove script tags entirely (including content) for security
        let sanitized = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        // Remove other HTML tags but keep their text content
        sanitized = sanitized.replace(/<[^>]*>/g, '');
        // Remove javascript: URLs
        sanitized = sanitized.replace(/javascript:/gi, '');
        return sanitized.trim();
      }

      // Default behavior - just remove HTML tags
      return input.replace(/<[^>]*>/g, '').trim();
    }),
  },
}));

jest.mock('validator', () => ({
  escape: jest.fn((input: string) => {
    // console.log('DEBUG: Mock validator.escape called with:', input);
    // Mock that actually escapes HTML entities
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  }),
}));

// Now import the middleware after mocks are set up
import { sanitizeInput } from '../../middlewares/security.middleware';
import { HttpException } from '../../exceptions/HttpException';

describe('Input Sanitization Middleware', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    // Test routes with sanitization middleware applied to each route
    app.post('/api/test', sanitizeInput, (req, res) => {
      res.json({
        success: true,
        body: req.body,
        query: req.query,
        params: req.params,
      });
    });

    app.get('/api/test/:id', sanitizeInput, (req, res) => {
      res.json({
        success: true,
        params: req.params,
        query: req.query,
      });
    });

    // Error handler
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    app.use((err: any, _req: any, res: any, _next: any) => {
      if (err instanceof HttpException) {
        res.status(err.status).json({ error: err.message });
      } else if (err.type === 'entity.parse.failed' || err.name === 'SyntaxError') {
        // Handle JSON parsing errors
        res.status(400).json({ error: 'Invalid JSON format' });
      } else {
        res.status(500).json({ error: 'Internal server error' });
      }
    });
  });

  describe('XSS Prevention', () => {
    it('should reject script tags in request body (security-first approach)', async () => {
      const maliciousInput = {
        name: '<script>alert("xss")</script>John',
        description: 'Hello <script>alert("xss")</script> World',
      };

      // The middleware should reject malicious input with 400 error (more secure)
      const response = await request(app).post('/api/test').send(maliciousInput).expect(400);

      // Should return error message indicating input was rejected
      expect(response.body.error).toBeDefined();
    });

    it('should reject HTML tags in request body (security-first approach)', async () => {
      const maliciousInput = {
        content: '<img src="x" onerror="alert(1)">',
        title: '<div onclick="alert(1)">Title</div>',
      };

      // The middleware should reject malicious HTML with 400 error (more secure)
      const response = await request(app).post('/api/test').send(maliciousInput).expect(400);

      expect(response.body.error).toBeDefined();
    });

    it('should reject JavaScript URLs (security-first approach)', async () => {
      const maliciousInput = {
        link: 'javascript:alert("xss")',
        href: 'javascript:void(0)',
      };

      // The middleware should reject JavaScript URLs with 400 error (more secure)
      const response = await request(app).post('/api/test').send(maliciousInput).expect(400);

      expect(response.body.error).toBeDefined();
    });

    it('should reject malicious query parameters (security-first approach)', async () => {
      const response = await request(app)
        .get('/api/test/123')
        .query({
          search: '<script>alert("xss")</script>',
          filter: '<img src="x" onerror="alert(1)">',
        })
        .expect(400);

      expect(response.body.error).toBeDefined();
    });

    it('should reject malicious URL parameters (security-first approach)', async () => {
      const maliciousId = '<script>alert("xss")</script>';
      const response = await request(app)
        .get(`/api/test/${encodeURIComponent(maliciousId)}`)
        .expect(400);

      expect(response.body.error).toBeDefined();
    });
  });

  describe('SQL Injection Prevention', () => {
    it('should escape SQL injection attempts in strings', async () => {
      const maliciousInput = {
        username: "admin'; DROP TABLE users; --",
        email: "<EMAIL>' OR '1'='1",
      };

      const response = await request(app).post('/api/test').send(maliciousInput).expect(200);

      expect(response.body.body.username).not.toContain("'; DROP TABLE");
      expect(response.body.body.email).not.toContain("' OR '1'='1");
    });

    it('should handle UNION SELECT attacks', async () => {
      const maliciousInput = {
        id: '1 UNION SELECT * FROM users',
        search: "' UNION SELECT password FROM users WHERE '1'='1",
      };

      const response = await request(app).post('/api/test').send(maliciousInput).expect(200);

      expect(response.body.body.id).not.toContain('UNION SELECT');
      expect(response.body.body.search).not.toContain('UNION SELECT');
    });
  });

  describe('Input Length Limits', () => {
    it('should reject extremely long input strings', async () => {
      const longString = 'a'.repeat(15000); // Exceeds typical limits
      const maliciousInput = {
        content: longString,
      };

      const response = await request(app).post('/api/test').send(maliciousInput).expect(400);

      expect(response.body.error).toContain('Input too long');
    });

    it('should allow reasonable length inputs', async () => {
      const reasonableString = 'a'.repeat(1000);
      const validInput = {
        content: reasonableString,
      };

      const response = await request(app).post('/api/test').send(validInput).expect(200);

      expect(response.body.body.content).toBe(reasonableString);
    });
  });

  describe('Special Characters and Encoding', () => {
    it('should handle Unicode characters properly', async () => {
      const unicodeInput = {
        name: 'José María',
        emoji: '🚀 Hello 世界',
        special: 'café naïve résumé',
      };

      const response = await request(app).post('/api/test').send(unicodeInput).expect(200);

      expect(response.body.body.name).toBe('José María');
      expect(response.body.body.emoji).toContain('🚀');
      expect(response.body.body.special).toContain('café');
    });

    it('should escape HTML entities', async () => {
      const htmlInput = {
        content: 'Price: $100 & tax',
        description: 'A < B > C',
      };

      const response = await request(app).post('/api/test').send(htmlInput).expect(200);

      // Should escape HTML entities
      expect(response.body.body.content).toContain('&amp;');
      expect(response.body.body.description).toContain('&lt;');
      expect(response.body.body.description).toContain('&gt;');
    });
  });

  describe('Nested Object Sanitization', () => {
    it('should reject nested objects with malicious content (security-first approach)', async () => {
      const nestedInput = {
        user: {
          name: '<script>alert("xss")</script>John',
          profile: {
            bio: '<img src="x" onerror="alert(1)">',
            settings: {
              theme: '<div onclick="alert(1)">dark</div>',
            },
          },
        },
      };

      // The middleware should reject malicious nested content with 400 error
      const response = await request(app).post('/api/test').send(nestedInput).expect(400);

      expect(response.body.error).toBeDefined();
    });

    it('should reject arrays with malicious content (security-first approach)', async () => {
      const arrayInput = {
        tags: ['<script>alert("xss")</script>', '<img src="x" onerror="alert(1)">', 'normal tag'],
        comments: [{ text: '<script>alert("xss")</script>' }, { text: 'normal comment' }],
      };

      // The middleware should reject arrays containing malicious content with 400 error
      const response = await request(app).post('/api/test').send(arrayInput).expect(400);

      expect(response.body.error).toBeDefined();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null and undefined values', async () => {
      const edgeCaseInput = {
        nullValue: null,
        undefinedValue: undefined,
        emptyString: '',
        zeroValue: 0,
        falseValue: false,
      };

      const response = await request(app).post('/api/test').send(edgeCaseInput).expect(200);

      expect(response.body.body.nullValue).toBeNull();
      expect(response.body.body.emptyString).toBe('');
      expect(response.body.body.zeroValue).toBe(0);
      expect(response.body.body.falseValue).toBe(false);
    });

    it('should handle circular references gracefully', async () => {
      // This test ensures the middleware doesn't crash on circular references
      // Note: Express.js typically handles this, but we test for robustness
      const response = await request(app).post('/api/test').send({ name: 'test' }).expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should preserve valid HTML entities', async () => {
      const validEntities = {
        content: '&amp; &lt; &gt; &quot; &#39;',
      };

      const response = await request(app).post('/api/test').send(validEntities).expect(200);

      // Should preserve already encoded entities
      expect(response.body.body.content).toContain('&amp;');
    });
  });

  describe('Performance and DoS Prevention', () => {
    it('should consistently reject malicious content in multiple requests', async () => {
      const requests = Array(10)
        .fill(null)
        .map(() => request(app).post('/api/test').send({ content: '<script>alert("test")</script>' }));

      const responses = await Promise.all(requests);

      responses.forEach(response => {
        expect(response.status).toBe(400);
        expect(response.body.error).toBeDefined();
      });
    });

    it('should reject deeply nested objects to prevent DoS', async () => {
      // Create deeply nested object
      let deepObject: any = { value: '<script>alert("xss")</script>' };
      for (let i = 0; i < 100; i++) {
        deepObject = { nested: deepObject };
      }

      const response = await request(app).post('/api/test').send(deepObject).expect(400);

      expect(response.body.error).toContain('too deep');
    });
  });

  describe('Content-Type Handling', () => {
    it('should reject malicious form-encoded data (security-first approach)', async () => {
      const response = await request(app)
        .post('/api/test')
        .type('form')
        .send('name=<script>alert("xss")</script>&email=<EMAIL>')
        .expect(400);

      expect(response.body.error).toBeDefined();
    });

    it('should allow safe form-encoded data', async () => {
      const response = await request(app)
        .post('/api/test')
        .type('form')
        .send('name=John Doe&email=<EMAIL>')
        .expect(200);

      expect(response.body.body.name).toBe('John Doe');
      expect(response.body.body.email).toBe('<EMAIL>');
    });

    it('should handle malformed JSON gracefully', async () => {
      const response = await request(app)
        .post('/api/test')
        .type('json')
        .send('{"name": "<script>alert("xss")</script>"}') // Malformed JSON
        .expect(400);

      // Should return appropriate error for malformed JSON
      expect(response.status).toBe(400);
    });
  });
});
