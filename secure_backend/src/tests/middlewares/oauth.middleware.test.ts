import { Request, Response, NextFunction } from 'express';
import { OAuthSecurityMiddleware } from '../../middlewares/oauth.middleware';
import { OAuthConfig, OAuthStateManager } from '../../config/oauth.config';
import { HttpException } from '../../exceptions/HttpException';

// Mock dependencies
jest.mock('../../config/oauth.config');
jest.mock('../../config/redis');
jest.mock('../../utils/secureLogger');

const mockOAuthConfig = OAuthConfig as jest.Mocked<typeof OAuthConfig>;
const mockOAuthStateManager = OAuthStateManager as jest.Mocked<typeof OAuthStateManager>;

describe('OAuthSecurityMiddleware', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockReq = {
      params: {},
      query: {},
      session: {} as any,
      ip: '127.0.0.1',
      get: jest.fn(),
    };
    mockRes = {
      redirect: jest.fn(),
      set: jest.fn(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('validateProvider', () => {
    it('should validate enabled OAuth provider', () => {
      mockReq.params = { provider: 'google' };
      mockOAuthConfig.isProviderEnabled.mockReturnValue(true);

      OAuthSecurityMiddleware.validateProvider(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect(mockOAuthConfig.isProviderEnabled).toHaveBeenCalledWith('google');
    });

    it('should reject missing provider', () => {
      mockReq.params = {};

      OAuthSecurityMiddleware.validateProvider(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 400,
          message: 'OAuth provider is required',
        }),
      );
    });

    it('should reject invalid provider', () => {
      mockReq.params = { provider: 'invalid' };

      OAuthSecurityMiddleware.validateProvider(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 400,
          message: "OAuth provider 'invalid' is not enabled or configured",
        }),
      );
    });

    it('should reject disabled provider', () => {
      mockReq.params = { provider: 'google' };
      mockOAuthConfig.isProviderEnabled.mockReturnValue(false);

      OAuthSecurityMiddleware.validateProvider(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 400,
          message: "OAuth provider 'google' is not enabled or configured",
        }),
      );
    });
  });

  describe('generateOAuthState', () => {
    it('should generate OAuth state for anonymous user', () => {
      mockOAuthStateManager.generateState.mockReturnValue('generated_state_123');

      OAuthSecurityMiddleware.generateOAuthState(mockReq as Request, mockRes as Response, mockNext);

      expect((mockReq.session as any).oauthState).toBe('generated_state_123');
      expect((mockReq.session as any).oauthStateTimestamp).toBeGreaterThan(Date.now() - 1000);
      expect((mockReq as any).oauthState).toBe('generated_state_123');
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should generate OAuth state for authenticated user', () => {
      mockReq.user = { id: 'user123' } as any;
      mockOAuthStateManager.generateState.mockReturnValue('generated_state_user123');

      OAuthSecurityMiddleware.generateOAuthState(mockReq as Request, mockRes as Response, mockNext);

      expect(mockOAuthStateManager.generateState).toHaveBeenCalledWith('user123');
      expect((mockReq.session as any).oauthState).toBe('generated_state_user123');
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should handle state generation errors', () => {
      mockOAuthStateManager.generateState.mockImplementation(() => {
        throw new Error('State generation failed');
      });

      OAuthSecurityMiddleware.generateOAuthState(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 500,
          message: 'Failed to generate OAuth state',
        }),
      );
    });
  });

  describe('validateOAuthState', () => {
    it('should validate matching OAuth state', () => {
      const testState = 'valid_state_123';
      mockReq.query = { state: testState };
      mockReq.session = {
        oauthState: testState,
        oauthStateTimestamp: Date.now() - 5000, // 5 seconds ago
      } as any;
      mockOAuthStateManager.validateState.mockReturnValue(true);

      OAuthSecurityMiddleware.validateOAuthState(mockReq as Request, mockRes as Response, mockNext);

      expect(mockOAuthStateManager.validateState).toHaveBeenCalledWith(testState);
      expect((mockReq.session as any).oauthState).toBeUndefined();
      expect((mockReq.session as any).oauthStateTimestamp).toBeUndefined();
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should reject missing state parameter', () => {
      mockReq.query = {};
      mockReq.session = { oauthState: 'session_state' } as any;

      OAuthSecurityMiddleware.validateOAuthState(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 400,
          message: 'Invalid OAuth state parameter',
        }),
      );
    });

    it('should reject missing session state', () => {
      mockReq.query = { state: 'received_state' };
      mockReq.session = {} as any;

      OAuthSecurityMiddleware.validateOAuthState(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 400,
          message: 'Invalid OAuth state parameter',
        }),
      );
    });

    it('should reject invalid state format', () => {
      const testState = 'invalid_state';
      mockReq.query = { state: testState };
      mockReq.session = { oauthState: testState } as any;
      mockOAuthStateManager.validateState.mockReturnValue(false);

      OAuthSecurityMiddleware.validateOAuthState(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 400,
          message: 'Invalid or expired OAuth state parameter',
        }),
      );
    });

    it('should reject state parameter mismatch', () => {
      mockReq.query = { state: 'received_state' };
      mockReq.session = { oauthState: 'different_state' } as any;
      mockOAuthStateManager.validateState.mockReturnValue(true);

      OAuthSecurityMiddleware.validateOAuthState(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 400,
          message: 'OAuth state parameter mismatch',
        }),
      );
    });

    it('should reject expired state', () => {
      const testState = 'valid_state_123';
      mockReq.query = { state: testState };
      mockReq.session = {
        oauthState: testState,
        oauthStateTimestamp: Date.now() - 15 * 60 * 1000, // 15 minutes ago (expired)
      } as any;
      mockOAuthStateManager.validateState.mockReturnValue(true);

      OAuthSecurityMiddleware.validateOAuthState(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 400,
          message: 'OAuth state parameter expired',
        }),
      );
    });

    it('should clear session state on validation failure', () => {
      mockReq.query = { state: 'invalid_state' };
      mockReq.session = { oauthState: 'session_state' } as any;
      mockOAuthStateManager.validateState.mockReturnValue(false);

      OAuthSecurityMiddleware.validateOAuthState(mockReq as Request, mockRes as Response, mockNext);

      expect((mockReq.session as any).oauthState).toBeUndefined();
      expect((mockReq.session as any).oauthStateTimestamp).toBeUndefined();
    });
  });

  describe('detectSuspiciousOAuthActivity', () => {
    it('should pass normal OAuth requests', () => {
      mockReq.get = jest.fn((header: string) => {
        if (header === 'User-Agent') return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
        if (header === 'Referer') return 'http://localhost:3000/login';
        if (header === 'host') return 'localhost:3000';
        return undefined;
      }) as any;
      (mockReq as any).deviceFingerprint = 'valid_fingerprint';

      OAuthSecurityMiddleware.detectSuspiciousOAuthActivity(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should detect missing User-Agent', () => {
      mockReq.get = jest.fn((header: string) => {
        if (header === 'User-Agent') return undefined;
        if (header === 'Referer') return 'http://localhost:3000/login';
        if (header === 'host') return 'localhost:3000';
        return undefined;
      }) as any;
      (mockReq as any).deviceFingerprint = 'valid_fingerprint';

      OAuthSecurityMiddleware.detectSuspiciousOAuthActivity(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      // Should log warning but not block
    });

    it('should detect short User-Agent', () => {
      mockReq.get = jest.fn((header: string) => {
        if (header === 'User-Agent') return 'short';
        if (header === 'Referer') return 'http://localhost:3000/login';
        if (header === 'host') return 'localhost:3000';
        return undefined;
      }) as any;
      (mockReq as any).deviceFingerprint = 'valid_fingerprint';

      OAuthSecurityMiddleware.detectSuspiciousOAuthActivity(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      // Should log warning but not block
    });

    it('should detect external referrer', () => {
      mockReq.get = jest.fn((header: string) => {
        if (header === 'User-Agent') return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
        if (header === 'Referer') return 'http://malicious-site.com/attack';
        if (header === 'host') return 'localhost:3000';
        return undefined;
      }) as any;
      (mockReq as any).deviceFingerprint = 'valid_fingerprint';

      OAuthSecurityMiddleware.detectSuspiciousOAuthActivity(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      // Should log warning but not block
    });

    it('should detect missing device fingerprint', () => {
      mockReq.get = jest.fn((header: string) => {
        if (header === 'User-Agent') return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
        if (header === 'Referer') return 'http://localhost:3000/login';
        if (header === 'host') return 'localhost:3000';
        return undefined;
      }) as any;
      (mockReq as any).deviceFingerprint = undefined;

      OAuthSecurityMiddleware.detectSuspiciousOAuthActivity(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      // Should log warning but not block
    });

    it('should handle detection errors gracefully', () => {
      mockReq.get = jest.fn(() => {
        throw new Error('Header access error');
      });

      OAuthSecurityMiddleware.detectSuspiciousOAuthActivity(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      // Should not block on detection errors
    });
  });

  describe('validateOAuthSession', () => {
    it('should validate valid OAuth session', () => {
      mockReq.session = {} as any;
      mockReq.sessionID = 'valid_session_id';

      OAuthSecurityMiddleware.validateOAuthSession(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should reject missing session', () => {
      mockReq.session = undefined;

      OAuthSecurityMiddleware.validateOAuthSession(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 500,
          message: 'Session required for OAuth authentication',
        }),
      );
    });

    it('should reject invalid session ID', () => {
      mockReq.session = {} as any;
      mockReq.sessionID = undefined;

      OAuthSecurityMiddleware.validateOAuthSession(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(HttpException));
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 500,
          message: 'Invalid session for OAuth authentication',
        }),
      );
    });
  });
});
