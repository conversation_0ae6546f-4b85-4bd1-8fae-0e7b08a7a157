import { Request, Response, NextFunction } from 'express';
import authMiddleware from '../../middlewares/auth.middleware';
import { HttpException } from '../../exceptions/HttpException';
import { JwtUtils } from '../../utils/jwt';
import { UserRepository } from '../../repositories/user.repository';
import { TokenBlacklistService } from '../../services/tokenBlacklist.service';

jest.mock('../../utils/jwt');
jest.mock('../../repositories/user.repository');
jest.mock('../../services/tokenBlacklist.service');

describe('Authentication Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction = jest.fn();

  beforeEach(() => {
    mockRequest = {
      cookies: {},
      headers: {},
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call next with HttpException if no token is provided', async () => {
    await authMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
    expect(nextFunction).toHaveBeenCalledWith(new HttpException(401, 'Authentication token missing'));
  });

  it('should extract token from cookies if available', async () => {
    mockRequest.cookies = { accessToken: 'test_token' };
    (JwtUtils.verify as jest.Mock).mockReturnValue({ id: 'user_id', role: 'USER' });
    (UserRepository.findById as jest.Mock).mockResolvedValue({ id: 'user_id', role: 'USER' });
    (TokenBlacklistService.isTokenBlacklisted as jest.Mock).mockReturnValue(false);

    await authMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
    expect(JwtUtils.verify).toHaveBeenCalledWith('test_token');
    expect(nextFunction).toHaveBeenCalledWith();
  });

  it('should fall back to Authorization header if no cookie is present', async () => {
    mockRequest.headers = { authorization: 'Bearer test_token_header' };
    (JwtUtils.verify as jest.Mock).mockReturnValue({ id: 'user_id', role: 'USER' });
    (UserRepository.findById as jest.Mock).mockResolvedValue({ id: 'user_id', role: 'USER' });
    (TokenBlacklistService.isTokenBlacklisted as jest.Mock).mockReturnValue(false);

    await authMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
    expect(JwtUtils.verify).toHaveBeenCalledWith('test_token_header');
    expect(nextFunction).toHaveBeenCalledWith();
  });

  it('should call next with HttpException if token is blacklisted', async () => {
    mockRequest.cookies = { accessToken: 'blacklisted_token' };
    (JwtUtils.getTokenId as jest.Mock).mockReturnValue('token_id');
    (TokenBlacklistService.isTokenBlacklisted as jest.Mock).mockReturnValue(true);

    await authMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
    expect(nextFunction).toHaveBeenCalledWith(new HttpException(401, 'Token has been invalidated'));
  });

  it('should call next with HttpException if token is invalid', async () => {
    mockRequest.cookies = { accessToken: 'invalid_token' };
    (JwtUtils.verify as jest.Mock).mockImplementation(() => {
      throw new Error('Invalid token');
    });

    await authMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
    expect(nextFunction).toHaveBeenCalledWith(new HttpException(401, 'Token has been invalidated'));
  });

  it('should call next with HttpException if user is not found', async () => {
    mockRequest.cookies = { accessToken: 'valid_token' };
    (JwtUtils.verify as jest.Mock).mockReturnValue({ id: 'user_id', role: 'USER' });
    (UserRepository.findById as jest.Mock).mockResolvedValue(null);
    (TokenBlacklistService.isTokenBlacklisted as jest.Mock).mockReturnValue(false);

    await authMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
    expect(nextFunction).toHaveBeenCalledWith(new HttpException(401, 'Invalid authentication token'));
  });

  it('should call next with HttpException if token role does not match user role', async () => {
    mockRequest.cookies = { accessToken: 'valid_token' };
    (JwtUtils.verify as jest.Mock).mockReturnValue({ id: 'user_id', role: 'USER' });
    (UserRepository.findById as jest.Mock).mockResolvedValue({ id: 'user_id', role: 'ADMIN' });
    (TokenBlacklistService.isTokenBlacklisted as jest.Mock).mockReturnValue(false);

    await authMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
    expect(nextFunction).toHaveBeenCalledWith(new HttpException(401, 'Token role mismatch - please re-authenticate'));
  });

  it('should attach user to request and call next if token is valid', async () => {
    const user = { id: 'user_id', role: 'USER' };
    mockRequest.cookies = { accessToken: 'valid_token' };
    (JwtUtils.verify as jest.Mock).mockReturnValue({ id: 'user_id', role: 'USER' });
    (UserRepository.findById as jest.Mock).mockResolvedValue(user);
    (TokenBlacklistService.isTokenBlacklisted as jest.Mock).mockReturnValue(false);

    await authMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);
    expect(mockRequest.user).toEqual(user);
    expect(nextFunction).toHaveBeenCalledWith();
  });
});
