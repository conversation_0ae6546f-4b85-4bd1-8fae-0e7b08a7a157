import request from 'supertest';
import app from '@/app';

describe('Health Check Route', () => {
  describe('GET /health', () => {
    it('should return health status successfully', async () => {
      const response = await request(app).get('/health').expect(200);

      expect(response.body).toEqual({
        status: 'OK',
        message: 'Secure Backend Server is running',
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        features: {
          authentication: 'JWT-based with OAuth 2.0',
          authorization: 'RBAC with granular permissions',
          validation: 'class-validator with DTOs',
          rateLimit: 'express-rate-limit',
          logging: 'Winston with morgan',
          swagger: 'Available at /api-docs',
          oauth: 'Google, Facebook, GitHub',
        },
      });
    });

    it('should return valid timestamp format', async () => {
      const response = await request(app).get('/health').expect(200);

      const timestamp = response.body.timestamp;
      expect(timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(new Date(timestamp).getTime()).toBeGreaterThan(0);
    });

    it('should return positive uptime', async () => {
      const response = await request(app).get('/health').expect(200);

      expect(response.body.uptime).toBeGreaterThanOrEqual(0);
      expect(typeof response.body.uptime).toBe('number');
    });

    it('should include all required features', async () => {
      const response = await request(app).get('/health').expect(200);

      const features = response.body.features;
      const expectedFeatures = [
        'authentication',
        'authorization',
        'validation',
        'rateLimit',
        'logging',
        'swagger',
        'oauth',
      ];

      expectedFeatures.forEach(feature => {
        expect(features).toHaveProperty(feature);
        expect(typeof features[feature]).toBe('string');
        expect(features[feature]).toBeTruthy();
      });
    });

    it('should set correct content type header', async () => {
      const response = await request(app).get('/health').expect(200);

      expect(response.headers['content-type']).toMatch(/application\/json/);
    });

    it('should respond quickly (performance check)', async () => {
      const startTime = Date.now();

      await request(app).get('/health').expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(1000); // Should respond within 1 second
    });

    it('should handle multiple concurrent requests', async () => {
      const requests = Array(5)
        .fill(null)
        .map(() => request(app).get('/health').expect(200));

      const responses = await Promise.all(requests);

      responses.forEach(response => {
        expect(response.body.status).toBe('OK');
        expect(response.body.message).toBe('Secure Backend Server is running');
      });
    });
  });
});
