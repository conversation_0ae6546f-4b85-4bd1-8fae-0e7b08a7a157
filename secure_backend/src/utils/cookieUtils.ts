import { Response } from 'express';
import { NODE_ENV } from '../config';
import { JwtUtils } from './jwt';

/**
 * Cookie names for secure token storage
 */
export const COOKIE_NAMES = Object.freeze({
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
} as const);

/**
 * Secure cookie configuration interface
 */
interface SecureCookieOptions {
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  maxAge?: number;
  path?: string;
  domain?: string;
}

/**
 * Cookie utility service for secure token management
 */
export class CookieUtils {
  /**
   * Get default secure cookie options
   */
  private static getDefaultCookieOptions(): SecureCookieOptions {
    return {
      httpOnly: true,
      secure: NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
    };
  }

  /**
   * Set access token cookie with short TTL (15 minutes)
   * @param res - Express response object
   * @param accessToken - JWT access token
   */
  static setAccessTokenCookie(res: Response, accessToken: string): void {
    const tokenExpiry = JwtUtils.getTokenExpiryMs();
    const options: SecureCookieOptions = {
      ...this.getDefaultCookieOptions(),
      maxAge: tokenExpiry.access,
    };

    res.cookie(COOKIE_NAMES.ACCESS_TOKEN, accessToken, options);
  }

  /**
   * Set refresh token cookie with long TTL (30 days)
   * @param res - Express response object
   * @param refreshToken - JWT refresh token
   */
  static setRefreshTokenCookie(res: Response, refreshToken: string): void {
    const tokenExpiry = JwtUtils.getTokenExpiryMs();
    const options: SecureCookieOptions = {
      ...this.getDefaultCookieOptions(),
      maxAge: tokenExpiry.refresh,
    };

    res.cookie(COOKIE_NAMES.REFRESH_TOKEN, refreshToken, options);
  }

  /**
   * Set both access and refresh token cookies
   * @param res - Express response object
   * @param accessToken - JWT access token
   * @param refreshToken - JWT refresh token
   */
  static setTokenCookies(res: Response, accessToken: string, refreshToken: string): void {
    this.setAccessTokenCookie(res, accessToken);
    this.setRefreshTokenCookie(res, refreshToken);
  }

  /**
   * Clear access token cookie
   * @param res - Express response object
   */
  static clearAccessTokenCookie(res: Response): void {
    const options: SecureCookieOptions = {
      ...this.getDefaultCookieOptions(),
      maxAge: 0,
    };

    res.clearCookie(COOKIE_NAMES.ACCESS_TOKEN, options);
  }

  /**
   * Clear refresh token cookie
   * @param res - Express response object
   */
  static clearRefreshTokenCookie(res: Response): void {
    const options: SecureCookieOptions = {
      ...this.getDefaultCookieOptions(),
      maxAge: 0,
    };

    res.clearCookie(COOKIE_NAMES.REFRESH_TOKEN, options);
  }

  /**
   * Clear both access and refresh token cookies
   * @param res - Express response object
   */
  static clearTokenCookies(res: Response): void {
    this.clearAccessTokenCookie(res);
    this.clearRefreshTokenCookie(res);
  }

  /**
   * Extract access token from request cookies
   * @param req - Express request object
   * @returns Access token or null if not found
   */
  static getAccessTokenFromCookies(req: any): string | null {
    return req.cookies?.[COOKIE_NAMES.ACCESS_TOKEN] || null;
  }

  /**
   * Extract refresh token from request cookies
   * @param req - Express request object
   * @returns Refresh token or null if not found
   */
  static getRefreshTokenFromCookies(req: any): string | null {
    return req.cookies?.[COOKIE_NAMES.REFRESH_TOKEN] || null;
  }

  /**
   * Validate cookie security settings for production
   * @returns Validation results
   */
  static validateCookieSecurity(): { isSecure: boolean; warnings: string[] } {
    const warnings: string[] = [];
    let isSecure = true;

    if (NODE_ENV === 'production') {
      const defaultOptions = this.getDefaultCookieOptions();

      if (!defaultOptions.secure) {
        warnings.push('Cookies should be secure in production');
        isSecure = false;
      }

      if (!defaultOptions.httpOnly) {
        warnings.push('Cookies should be HTTP-only to prevent XSS attacks');
        isSecure = false;
      }

      if (defaultOptions.sameSite !== 'strict') {
        warnings.push('Cookies should use SameSite=Strict for maximum CSRF protection');
      }
    }

    return { isSecure, warnings };
  }
}

export default CookieUtils;
