import { PrismaClient } from '@prisma/client';

async function verifyExistingEmails() {
  const prisma = new PrismaClient();

  try {
    console.log('Starting migration: Verifying existing user emails...');

    // Get all users where emailVerified is null (not verified)
    const unverifiedUsers = await prisma.user.findMany({
      where: {
        emailVerified: null,
      },
      select: {
        id: true,
        email: true,
      },
    });

    console.log(`Found ${unverifiedUsers.length} unverified users`);

    // Update all unverified users to be verified
    const updatePromises = unverifiedUsers.map(user =>
      prisma.user.update({
        where: { id: user.id },
        data: {
          emailVerified: new Date(),
        },
      }),
    );

    // Execute all updates
    const results = await Promise.all(updatePromises);

    console.log(`Successfully verified ${results.length} users`);
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the migration
verifyExistingEmails()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  });
