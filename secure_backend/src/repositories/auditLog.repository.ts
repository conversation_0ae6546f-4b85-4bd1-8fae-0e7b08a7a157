import { AuditLog, Prisma, AuditAction } from '@prisma/client';
import { prisma, DAL } from '../loaders';
import { logger } from '../utils/logger';
import { sanitizeForLog, sanitizeError } from '../utils/logSanitizer';
import { PaginationResult, RepositoryFilters } from '../types/template';

export interface CreateAuditLogData {
  userId: string;
  action: AuditAction;
  ipAddress?: string | null;
  userAgent?: string | null;
}

export interface AuditLogFilters extends RepositoryFilters {
  userId?: string;
  action?: AuditAction;
  ipAddress?: string;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * AuditLog Repository - Data Access Layer for AuditLog operations
 */
export class AuditLogRepository {
  /**
   * Create a new audit log entry
   */
  static async create(data: CreateAuditLogData): Promise<AuditLog> {
    try {
      logger.debug('Creating new audit log', sanitizeForLog({ userId: data.userId, action: data.action }));

      const auditLog = await prisma.auditLog.create({
        data: {
          userId: data.userId,
          action: data.action,
          ipAddress: data.ipAddress ?? null, // Convert undefined to null
          userAgent: data.userAgent ?? null, // Convert undefined to null
        },
      });

      logger.info(
        'Audit log created successfully',
        sanitizeForLog({ id: auditLog.id, userId: auditLog.userId, action: auditLog.action }),
      );
      return auditLog;
    } catch (error) {
      logger.error('Failed to create audit log', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Find audit logs with filters and pagination
   */
  static async findMany(
    filters: AuditLogFilters = {},
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginationResult<AuditLog>> {
    try {
      const where: Prisma.AuditLogWhereInput = {};

      if (filters.userId) {
        where.userId = filters.userId;
      }
      if (filters.action) {
        where.action = filters.action;
      }
      if (filters.ipAddress) {
        where.ipAddress = {
          contains: filters.ipAddress,
          mode: 'insensitive',
        };
      }

      if (filters.createdAfter || filters.createdBefore) {
        where.createdAt = {};
        if (filters.createdAfter) {
          where.createdAt.gte = filters.createdAfter;
        }
        if (filters.createdBefore) {
          where.createdAt.lte = filters.createdBefore;
        }
      }

      const result = await DAL.paginate<AuditLog, Prisma.AuditLogFindManyArgs>(
        prisma.auditLog,
        {
          where,
          orderBy: { createdAt: 'desc' },
        },
        page,
        limit,
      );

      logger.debug(
        'Audit logs retrieved successfully',
        sanitizeForLog({
          total: result.total,
          page: result.page,
          limit: result.limit,
        }),
      );

      return {
        data: result.data,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          pages: result.totalPages,
          hasNext: result.hasNext,
          hasPrev: result.hasPrev,
        },
      };
    } catch (error) {
      logger.error('Failed to find audit logs', sanitizeError(error));
      throw error;
    }
  }

  /**
   * Get audit log count
   */
  static async count(filters: AuditLogFilters = {}): Promise<number> {
    try {
      const where: Prisma.AuditLogWhereInput = {};

      if (filters.userId) {
        where.userId = filters.userId;
      }
      if (filters.action) {
        where.action = filters.action;
      }
      if (filters.ipAddress) {
        where.ipAddress = {
          contains: filters.ipAddress,
          mode: 'insensitive',
        };
      }

      if (filters.createdAfter || filters.createdBefore) {
        where.createdAt = {};
        if (filters.createdAfter) {
          where.createdAt.gte = filters.createdAfter;
        }
        if (filters.createdBefore) {
          where.createdAt.lte = filters.createdBefore;
        }
      }

      const count = await prisma.auditLog.count({ where });
      return count;
    } catch (error) {
      logger.error('Failed to count audit logs', sanitizeError(error));
      throw error;
    }
  }
}
