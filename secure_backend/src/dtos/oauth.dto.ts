import { IsString, IsEmail, IsO<PERSON>al, IsBoolean, IsIn, IsUrl, Length, Matches } from 'class-validator';
import { Transform } from 'class-transformer';
import { OAuthProvider } from '../config/oauth.config';

/**
 * OAuth Provider Validation DTO
 */
export class OAuthProviderDto {
  @IsString({ message: 'Provider must be a string' })
  @IsIn(['google', 'facebook', 'github'], { message: 'Provider must be one of: google, facebook, github' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  provider!: OAuthProvider;
}

/**
 * OAuth Callback Query Parameters DTO
 */
export class OAuthCallbackDto {
  @IsOptional()
  @IsString({ message: 'Authorization code must be a string' })
  @Length(1, 2000, { message: 'Authorization code must be between 1 and 2000 characters' })
  code?: string;

  @IsOptional()
  @IsString({ message: 'State parameter must be a string' })
  @Length(1, 500, { message: 'State parameter must be between 1 and 500 characters' })
  @Matches(/^[a-zA-Z0-9_-]+$/, { message: 'State parameter contains invalid characters' })
  state?: string;

  @IsOptional()
  @IsString({ message: 'Error parameter must be a string' })
  @Length(1, 200, { message: 'Error parameter must be between 1 and 200 characters' })
  error?: string;

  @IsOptional()
  @IsString({ message: 'Error description must be a string' })
  @Length(1, 500, { message: 'Error description must be between 1 and 500 characters' })
  error_description?: string;

  @IsOptional()
  @IsString({ message: 'Error URI must be a string' })
  @IsUrl({}, { message: 'Error URI must be a valid URL' })
  error_uri?: string;
}

/**
 * OAuth Profile DTO for internal validation
 */
export class OAuthProfileDto {
  @IsString({ message: 'Provider ID must be a string' })
  @Length(1, 255, { message: 'Provider ID must be between 1 and 255 characters' })
  id!: string;

  @IsString({ message: 'Provider must be a string' })
  @IsIn(['google', 'facebook', 'github'], { message: 'Provider must be one of: google, facebook, github' })
  provider!: OAuthProvider;

  @IsOptional()
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @Length(1, 255, { message: 'Email must be between 1 and 255 characters' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  email?: string;

  @IsOptional()
  @IsString({ message: 'Name must be a string' })
  @Length(1, 255, { message: 'Name must be between 1 and 255 characters' })
  @Transform(({ value }) => value?.trim())
  name?: string;

  @IsOptional()
  @IsString({ message: 'Username must be a string' })
  @Length(1, 50, { message: 'Username must be between 1 and 50 characters' })
  @Matches(/^[a-zA-Z0-9_.-]+$/, {
    message: 'Username can only contain letters, numbers, dots, hyphens, and underscores',
  })
  @Transform(({ value }) => value?.toLowerCase().trim())
  username?: string;

  @IsOptional()
  @IsUrl({}, { message: 'Picture must be a valid URL' })
  @Length(1, 2000, { message: 'Picture URL must be between 1 and 2000 characters' })
  picture?: string;

  @IsOptional()
  @IsBoolean({ message: 'Verified must be a boolean' })
  verified?: boolean;
}

/**
 * OAuth Account Linking Request DTO
 */
export class OAuthAccountLinkDto {
  @IsString({ message: 'User ID must be a string' })
  @Length(1, 255, { message: 'User ID must be between 1 and 255 characters' })
  userId!: string;

  @IsString({ message: 'Provider must be a string' })
  @IsIn(['google', 'facebook', 'github'], { message: 'Provider must be one of: google, facebook, github' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  provider!: OAuthProvider;

  @IsString({ message: 'Provider account ID must be a string' })
  @Length(1, 255, { message: 'Provider account ID must be between 1 and 255 characters' })
  providerAccountId!: string;

  @IsString({ message: 'Access token must be a string' })
  @Length(1, 4000, { message: 'Access token must be between 1 and 4000 characters' })
  accessToken!: string;

  @IsOptional()
  @IsString({ message: 'Refresh token must be a string' })
  @Length(1, 4000, { message: 'Refresh token must be between 1 and 4000 characters' })
  refreshToken?: string;
}

/**
 * OAuth State Parameter DTO
 */
export class OAuthStateDto {
  @IsString({ message: 'State must be a string' })
  @Length(10, 500, { message: 'State must be between 10 and 500 characters' })
  @Matches(/^[a-zA-Z0-9_-]+$/, { message: 'State contains invalid characters' })
  state!: string;
}

/**
 * OAuth Authorization Request DTO
 */
export class OAuthAuthRequestDto {
  @IsString({ message: 'Provider must be a string' })
  @IsIn(['google', 'facebook', 'github'], { message: 'Provider must be one of: google, facebook, github' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  provider!: OAuthProvider;

  @IsOptional()
  @IsString({ message: 'Redirect URI must be a string' })
  @IsUrl({}, { message: 'Redirect URI must be a valid URL' })
  @Length(1, 2000, { message: 'Redirect URI must be between 1 and 2000 characters' })
  redirect_uri?: string;

  @IsOptional()
  @IsString({ message: 'State must be a string' })
  @Length(1, 500, { message: 'State must be between 1 and 500 characters' })
  @Matches(/^[a-zA-Z0-9_-]+$/, { message: 'State contains invalid characters' })
  state?: string;

  @IsOptional()
  @IsString({ message: 'Scope must be a string' })
  @Length(1, 500, { message: 'Scope must be between 1 and 500 characters' })
  @Matches(/^[a-zA-Z0-9:._\s-]+$/, { message: 'Scope contains invalid characters' })
  scope?: string;
}

/**
 * OAuth Token Response DTO
 */
export class OAuthTokenResponseDto {
  @IsString({ message: 'Access token must be a string' })
  @Length(1, 4000, { message: 'Access token must be between 1 and 4000 characters' })
  access_token!: string;

  @IsOptional()
  @IsString({ message: 'Refresh token must be a string' })
  @Length(1, 4000, { message: 'Refresh token must be between 1 and 4000 characters' })
  refresh_token?: string;

  @IsOptional()
  @IsString({ message: 'Token type must be a string' })
  @IsIn(['Bearer', 'bearer'], { message: 'Token type must be Bearer' })
  token_type?: string;

  @IsOptional()
  @IsString({ message: 'Scope must be a string' })
  @Length(1, 500, { message: 'Scope must be between 1 and 500 characters' })
  scope?: string;

  @IsOptional()
  @IsString({ message: 'ID token must be a string' })
  @Length(1, 4000, { message: 'ID token must be between 1 and 4000 characters' })
  id_token?: string;
}

/**
 * OAuth Account Unlink Request DTO
 */
export class OAuthAccountUnlinkDto {
  @IsString({ message: 'Provider must be a string' })
  @IsIn(['google', 'facebook', 'github'], { message: 'Provider must be one of: google, facebook, github' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  provider!: OAuthProvider;

  @IsOptional()
  @IsString({ message: 'Confirmation must be a string' })
  @IsIn(['yes', 'confirm'], { message: 'Confirmation must be "yes" or "confirm"' })
  @Transform(({ value }) => value?.toLowerCase().trim())
  confirm?: string;
}

/**
 * OAuth Provider Configuration Response DTO
 */
export class OAuthProviderConfigDto {
  @IsString({ message: 'Provider name must be a string' })
  @IsIn(['google', 'facebook', 'github'], { message: 'Provider must be one of: google, facebook, github' })
  name!: OAuthProvider;

  @IsString({ message: 'Display name must be a string' })
  @Length(1, 50, { message: 'Display name must be between 1 and 50 characters' })
  displayName!: string;

  @IsString({ message: 'Auth URL must be a string' })
  @Length(1, 500, { message: 'Auth URL must be between 1 and 500 characters' })
  authUrl!: string;

  @IsBoolean({ message: 'Enabled must be a boolean' })
  enabled!: boolean;
}

/**
 * OAuth Error Response DTO
 */
export class OAuthErrorDto {
  @IsString({ message: 'Error code must be a string' })
  @Length(1, 100, { message: 'Error code must be between 1 and 100 characters' })
  error!: string;

  @IsOptional()
  @IsString({ message: 'Error description must be a string' })
  @Length(1, 500, { message: 'Error description must be between 1 and 500 characters' })
  error_description?: string;

  @IsOptional()
  @IsUrl({}, { message: 'Error URI must be a valid URL' })
  error_uri?: string;

  @IsOptional()
  @IsString({ message: 'State must be a string' })
  @Length(1, 500, { message: 'State must be between 1 and 500 characters' })
  state?: string;
}
