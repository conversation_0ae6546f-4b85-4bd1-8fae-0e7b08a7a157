import { IsString, IsOptional, IsBoolean, IsDateString, IsN<PERSON>ber, Min, Max } from 'class-validator';

export class TerminateSessionDto {
  @IsString()
  sessionId!: string;

  @IsOptional()
  @IsString()
  reason?: string;
}

export class TrustDeviceDto {
  @IsString()
  deviceId!: string;

  @IsOptional()
  @IsString()
  deviceName?: string;

  @IsOptional()
  @IsBoolean()
  trusted?: boolean;
}

export class SessionActivityQueryDto {
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  limit?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsString()
  sessionId?: string;

  @IsOptional()
  @IsString()
  deviceId?: string;
}

export class TerminateAllSessionsDto {
  @IsOptional()
  @IsBoolean()
  excludeCurrent?: boolean;

  @IsOptional()
  @IsString()
  reason?: string;
}
