import { IsString, IsOptional, IsA<PERSON>y, IsBoolean, IsInt, Min, Max, Length, IsIn } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateSubjectDto {
  @IsString()
  @Length(1, 200)
  title: string;

  @IsString()
  @Length(1, 1000)
  description: string;

  @IsOptional()
  @IsString()
  @Length(1, 50)
  language?: string = 'English';
}

export class GenerateCurriculumDto {
  @IsString()
  @Length(1, 200)
  subject: string;

  @IsOptional()
  @IsString()
  @Length(1, 50)
  language?: string = 'English';
}

export class CreateCourseDto {
  @IsString()
  @Length(1, 200)
  title: string;

  @IsString()
  @Length(1, 1000)
  description: string;

  @IsString()
  @IsIn(['beginner', 'intermediate', 'advanced'])
  difficulty: string;

  @IsString()
  @Length(1, 50)
  duration: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  @Length(1, 50)
  language?: string = 'English';
}

export class GenerateUniversityCourseDto {
  @IsString()
  @Length(10, 10000)
  userSketch: string;

  @IsOptional()
  @IsString()
  @Length(1, 50)
  language?: string = 'English';
}

export class CreateUniversityCourseModuleDto {
  @IsString()
  @Length(1, 200)
  title: string;

  @IsString()
  @Length(1, 1000)
  description: string;

  @IsString()
  @Length(1, 50)
  duration: string;

  @IsInt()
  @Min(1)
  @Max(50)
  order: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  prerequisites?: string[];

  @IsArray()
  @IsString({ each: true })
  learningObjectives: string[];

  curriculum: any; // JSON object for curriculum structure
}

export class CreateUniversityCourseDto {
  @IsString()
  @Length(1, 200)
  title: string;

  @IsString()
  @Length(1, 1000)
  description: string;

  @IsString()
  @Length(1, 50)
  duration: string;

  @IsString()
  @IsIn(['beginner', 'intermediate', 'advanced'])
  difficulty: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  prerequisites?: string[];

  @IsArray()
  @IsString({ each: true })
  learningOutcomes: string[];

  @IsString()
  @Length(10, 10000)
  userSketch: string;

  @IsArray()
  modules: CreateUniversityCourseModuleDto[];

  @IsOptional()
  @IsString()
  @Length(1, 50)
  language?: string = 'English';
}

export class UpdateProgressDto {
  @IsString()
  topicId: string;

  @IsBoolean()
  completed: boolean;
}

export class UpdateSubjectDto {
  @IsOptional()
  @IsString()
  @Length(1, 200)
  title?: string;

  @IsOptional()
  @IsString()
  @Length(1, 1000)
  description?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50)
  language?: string;

  @IsOptional()
  curriculum?: any; // JSON object for curriculum
}

export class UpdateCourseDto {
  @IsOptional()
  @IsString()
  @Length(1, 200)
  title?: string;

  @IsOptional()
  @IsString()
  @Length(1, 1000)
  description?: string;

  @IsOptional()
  @IsString()
  @IsIn(['beginner', 'intermediate', 'advanced'])
  difficulty?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50)
  duration?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  @Length(1, 50)
  language?: string;

  @IsOptional()
  content?: any; // JSON object for course content
}

export class UpdateUniversityCourseDto {
  @IsOptional()
  @IsString()
  @Length(1, 200)
  title?: string;

  @IsOptional()
  @IsString()
  @Length(1, 1000)
  description?: string;

  @IsOptional()
  @IsString()
  @Length(1, 50)
  duration?: string;

  @IsOptional()
  @IsString()
  @IsIn(['beginner', 'intermediate', 'advanced'])
  difficulty?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  prerequisites?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  learningOutcomes?: string[];

  @IsOptional()
  @IsString()
  @Length(1, 50)
  language?: string;
}

// Query DTOs for filtering and pagination
export class GetSubjectsQueryDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  language?: string;
}

export class GetCoursesQueryDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  difficulty?: string;

  @IsOptional()
  @IsString()
  language?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export class GetUniversityCoursesQueryDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsString()
  difficulty?: string;

  @IsOptional()
  @IsString()
  language?: string;
}
