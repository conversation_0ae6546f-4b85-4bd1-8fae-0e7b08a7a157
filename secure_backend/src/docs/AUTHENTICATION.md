# Authentication Flow

This document outlines the authentication process, detailing the steps involved in user registration, login, and token verification. It covers both traditional email/password authentication and OAuth 2.0 integration, ensuring a clear understanding of how users are authenticated and sessions are managed.

## Email/Password Authentication

### 1. User Registration

- **Endpoint**: `POST /api/v1/auth/register`
- **Description**: New users are registered with their email and a hashed password. Upon successful registration, an email verification token is generated and sent to the user's email address.

### 2. Email Verification

- **Endpoint**: `GET /api/v1/auth/verify-email`
- **Description**: The user clicks a link in the verification email, which directs them to this endpoint. The token is validated, and the user's email is marked as verified.

### 3. User Login

- **Endpoint**: `POST /api/v1/auth/login`
- **Description**: Users log in with their email and password. If the credentials are correct, a JSON Web Token (JWT) is generated and returned to the user.

## OAuth 2.0 Authentication

### 1. Initiate OAuth Flow

- **Endpoint**: `GET /api/v1/oauth/:provider`
- **Description**: Users initiate the OAuth flow by accessing this endpoint, where `:provider` can be `google`, `github`, etc. The user is redirected to the respective OAuth provider's authorization screen.

### 2. OAuth Callback

- **Endpoint**: `GET /api/v1/oauth/:provider/callback`
- **Description**: After the user grants permission, the OAuth provider redirects them to this callback endpoint. The application receives an authorization code, which is then exchanged for an access token.

### 3. Token Exchange and Session Creation

- **Description**: The access token is used to retrieve the user's profile information from the OAuth provider. A new user account is created if one does not already exist. A JWT is then generated and returned to the user, establishing a session.

## Token Management

- **JWTs**: All authenticated endpoints are secured with JWTs, which are sent in the `Authorization` header.
- **Token Expiration**: Tokens have a defined expiration time, after which they are no longer valid.
- **Token Refresh**: The application can be configured to support token refresh, allowing users to extend their sessions without re-authenticating.