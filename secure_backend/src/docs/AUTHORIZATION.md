# Authorization and RBAC

This document provides an overview of the authorization mechanisms and Role-Based Access Control (RBAC) implemented in the application. It details how permissions are managed and enforced, ensuring that users can only access the resources and perform the actions that are appropriate for their assigned roles.

## Roles

- **SUPER_ADMIN**: Has unrestricted access to all resources and administrative functionalities.
- **ADMIN**: Can manage users, and other critical resources, but with certain limitations.
- **MODERATOR**: Has permissions to manage user-generated content and other specific resources.
- **USER**: The default role for all new users, with limited access to resources.

## Permissions

Permissions are defined based on roles and are checked at the middleware level before allowing access to a route. The RBAC system is designed to be flexible, allowing for the addition of new roles and permissions as the application grows.

## Middleware

- **`authorizationMiddleware`**: This middleware is responsible for checking the user's role and permissions against the required permissions for a given route. If the user does not have the required permissions, an `HTTP 403 Forbidden` error is returned.

## Implementation

The RBAC implementation is centralized in the `rbac.service.ts`, which is responsible for loading role definitions and checking permissions. This service is used by the `authorization.middleware.ts` to enforce access control.