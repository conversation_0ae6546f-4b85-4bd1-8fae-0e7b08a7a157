import { Router } from 'express';
import authRoutes from './auth.routes';
import userRoutes from './user.route';
import emailVerificationRoutes from './emailVerification.routes';
import oauthRoutes from './oauth.routes';
import mfaRoutes from './mfa.routes';
import sessionRoutes from './session.routes';
import securityRoutes from './security.routes';
import subjectsRoutes from './subjects.routes';
import universityCoursesRoutes from './university-courses.routes';

const router = Router();

// Health check route
router.get('/', (_req, res) => {
  res.json({
    message: 'Secure Backend API with RBAC',
    version: '1.0.0',
    features: [
      'JWT Authentication',
      'OAuth 2.0 Integration',
      'Role-Based Access Control (RBAC)',
      'Permission-based Authorization',
      'Request Rate Limiting',
      'Data Validation',
      'Comprehensive Logging',
      'Email Verification',
      'Multi-Factor Authentication (MFA)',
      'Enhanced Session Management',
      'Advanced Rate Limiting & DDoS Protection',
      'Password Reset Flow',
      'AI-Powered Learning Platform',
      'University Course Generation',
      'Progress Tracking',
    ],
  });
});

// Authentication and user management routes
router.use('/auth', authRoutes);

// OAuth authentication routes
router.use('/auth/oauth', oauthRoutes);

// User management routes
router.use('/users', userRoutes);

// Email verification routes
router.use('/email-verification', emailVerificationRoutes);

// Multi-Factor Authentication routes
router.use('/mfa', mfaRoutes);

// Session Management routes
router.use('/sessions', sessionRoutes);

// Security Management routes
router.use('/security', securityRoutes);

// Learning Platform routes
router.use('/subjects', subjectsRoutes);
router.use('/university-courses', universityCoursesRoutes);

export default router;
