import { Router } from 'express';
import { SubjectsController } from '@/controllers/subjects.controller';
import { authMiddleware, rateLimiterMiddleware } from '@/middlewares';

const router = Router();
const subjectsController = new SubjectsController();

/**
 * @swagger
 * components:
 *   schemas:
 *     Subject:
 *       type: object
 *       required:
 *         - title
 *         - description
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the subject
 *         title:
 *           type: string
 *           description: The title of the subject
 *         description:
 *           type: string
 *           description: The description of the subject
 *         language:
 *           type: string
 *           description: The language of the subject content
 *         curriculum:
 *           type: object
 *           description: The generated curriculum structure
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The date the subject was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The date the subject was last updated
 */

/**
 * @swagger
 * /api/subjects:
 *   post:
 *     summary: Create a new subject
 *     tags: [Subjects]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *             properties:
 *               title:
 *                 type: string
 *                 example: "React Development"
 *               description:
 *                 type: string
 *                 example: "Learn modern React development with hooks and context"
 *               language:
 *                 type: string
 *                 example: "English"
 *     responses:
 *       201:
 *         description: Subject created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Subject'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post('/', authMiddleware, rateLimiterMiddleware, subjectsController.createSubject);

/**
 * @swagger
 * /api/subjects:
 *   get:
 *     summary: Get all subjects for the authenticated user
 *     tags: [Subjects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for title or description
 *       - in: query
 *         name: language
 *         schema:
 *           type: string
 *         description: Filter by language
 *     responses:
 *       200:
 *         description: Subjects retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Subject'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 */
router.get('/', authMiddleware, rateLimiterMiddleware, subjectsController.getSubjects);

/**
 * @swagger
 * /api/subjects/{id}:
 *   get:
 *     summary: Get a subject by ID
 *     tags: [Subjects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Subject ID
 *     responses:
 *       200:
 *         description: Subject retrieved successfully
 *       404:
 *         description: Subject not found
 *       401:
 *         description: Unauthorized
 */
router.get('/:id', authMiddleware, rateLimiterMiddleware, subjectsController.getSubjectById);

/**
 * @swagger
 * /api/subjects/{id}:
 *   put:
 *     summary: Update a subject
 *     tags: [Subjects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Subject ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               language:
 *                 type: string
 *               curriculum:
 *                 type: object
 *     responses:
 *       200:
 *         description: Subject updated successfully
 *       404:
 *         description: Subject not found
 *       401:
 *         description: Unauthorized
 */
router.put('/:id', authMiddleware, rateLimiterMiddleware, subjectsController.updateSubject);

/**
 * @swagger
 * /api/subjects/{id}:
 *   delete:
 *     summary: Delete a subject
 *     tags: [Subjects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Subject ID
 *     responses:
 *       200:
 *         description: Subject deleted successfully
 *       404:
 *         description: Subject not found
 *       401:
 *         description: Unauthorized
 */
router.delete('/:id', authMiddleware, rateLimiterMiddleware, subjectsController.deleteSubject);

/**
 * @swagger
 * /api/subjects/{id}/generate-curriculum:
 *   post:
 *     summary: Generate curriculum for a subject using AI
 *     tags: [Subjects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Subject ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - subject
 *             properties:
 *               subject:
 *                 type: string
 *                 example: "React Development"
 *               language:
 *                 type: string
 *                 example: "English"
 *     responses:
 *       200:
 *         description: Curriculum generated successfully
 *       404:
 *         description: Subject not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: AI generation failed
 */
router.post('/:id/generate-curriculum', authMiddleware, rateLimiterMiddleware, subjectsController.generateCurriculum);

/**
 * @swagger
 * /api/subjects/{id}/progress:
 *   post:
 *     summary: Update learning progress for a subject
 *     tags: [Subjects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Subject ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - topicId
 *               - completed
 *             properties:
 *               topicId:
 *                 type: string
 *                 example: "topic-123"
 *               completed:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Progress updated successfully
 *       404:
 *         description: Subject not found
 *       401:
 *         description: Unauthorized
 */
router.post('/:id/progress', authMiddleware, rateLimiterMiddleware, subjectsController.updateProgress);

/**
 * @swagger
 * /api/subjects/{id}/progress:
 *   get:
 *     summary: Get learning progress for a subject
 *     tags: [Subjects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Subject ID
 *     responses:
 *       200:
 *         description: Progress retrieved successfully
 *       404:
 *         description: Subject not found
 *       401:
 *         description: Unauthorized
 */
router.get('/:id/progress', authMiddleware, rateLimiterMiddleware, subjectsController.getProgress);

/**
 * @swagger
 * /api/subjects/{id}/stats:
 *   get:
 *     summary: Get learning statistics for a subject
 *     tags: [Subjects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Subject ID
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *       404:
 *         description: Subject not found
 *       401:
 *         description: Unauthorized
 */
router.get('/:id/stats', authMiddleware, rateLimiterMiddleware, subjectsController.getSubjectStats);

export default router;
