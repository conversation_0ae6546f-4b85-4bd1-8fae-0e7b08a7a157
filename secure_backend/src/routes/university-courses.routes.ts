import { Router } from 'express';
import { UniversityCoursesController } from '@/controllers/university-courses.controller';
import { authMiddleware, rateLimiterMiddleware } from '@/middlewares';

const router = Router();
const universityCoursesController = new UniversityCoursesController();

/**
 * @swagger
 * components:
 *   schemas:
 *     UniversityCourse:
 *       type: object
 *       required:
 *         - title
 *         - description
 *         - duration
 *         - difficulty
 *         - learningOutcomes
 *         - userSketch
 *       properties:
 *         id:
 *           type: string
 *           description: The auto-generated id of the course
 *         title:
 *           type: string
 *           description: The title of the course
 *         description:
 *           type: string
 *           description: The description of the course
 *         duration:
 *           type: string
 *           description: The duration of the course (e.g., "12 weeks")
 *         difficulty:
 *           type: string
 *           enum: [beginner, intermediate, advanced]
 *           description: The difficulty level of the course
 *         prerequisites:
 *           type: array
 *           items:
 *             type: string
 *           description: List of prerequisites
 *         learningOutcomes:
 *           type: array
 *           items:
 *             type: string
 *           description: List of learning outcomes
 *         userSketch:
 *           type: string
 *           description: Original user input that generated this course
 *         language:
 *           type: string
 *           description: The language of the course content
 *         modules:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/UniversityCourseModule'
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *     UniversityCourseModule:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         title:
 *           type: string
 *         description:
 *           type: string
 *         duration:
 *           type: string
 *         moduleOrder:
 *           type: integer
 *         prerequisites:
 *           type: array
 *           items:
 *             type: string
 *         learningObjectives:
 *           type: array
 *           items:
 *             type: string
 *         curriculumStructure:
 *           type: object
 */

/**
 * @swagger
 * /api/university-courses/generate:
 *   post:
 *     summary: Generate a university-style course from user sketch using AI
 *     tags: [University Courses]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userSketch
 *             properties:
 *               userSketch:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 10000
 *                 example: "I want to learn full-stack web development with React, Node.js, and databases. I need to understand both frontend and backend development, API design, and deployment."
 *               language:
 *                 type: string
 *                 example: "English"
 *     responses:
 *       200:
 *         description: University course generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     course:
 *                       type: object
 *                       properties:
 *                         courseInfo:
 *                           type: object
 *                         modules:
 *                           type: array
 *                           items:
 *                             type: object
 *                         reasoning:
 *                           type: string
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: AI generation failed
 */
router.post('/generate', authMiddleware, rateLimiterMiddleware, universityCoursesController.generateUniversityCourse);

/**
 * @swagger
 * /api/university-courses:
 *   post:
 *     summary: Create a new university course
 *     tags: [University Courses]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *               - duration
 *               - difficulty
 *               - learningOutcomes
 *               - userSketch
 *               - modules
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Full-Stack Web Development Mastery"
 *               description:
 *                 type: string
 *                 example: "A comprehensive course covering modern web development"
 *               duration:
 *                 type: string
 *                 example: "16 weeks"
 *               difficulty:
 *                 type: string
 *                 enum: [beginner, intermediate, advanced]
 *                 example: "intermediate"
 *               prerequisites:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["Basic programming knowledge", "HTML/CSS fundamentals"]
 *               learningOutcomes:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["Build full-stack web applications", "Deploy to production"]
 *               userSketch:
 *                 type: string
 *                 example: "Original user learning goals and requirements"
 *               modules:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - title
 *                     - description
 *                     - duration
 *                     - order
 *                     - learningObjectives
 *                     - curriculum
 *                   properties:
 *                     title:
 *                       type: string
 *                     description:
 *                       type: string
 *                     duration:
 *                       type: string
 *                     order:
 *                       type: integer
 *                     prerequisites:
 *                       type: array
 *                       items:
 *                         type: string
 *                     learningObjectives:
 *                       type: array
 *                       items:
 *                         type: string
 *                     curriculum:
 *                       type: object
 *               language:
 *                 type: string
 *                 example: "English"
 *     responses:
 *       201:
 *         description: University course created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post('/', authMiddleware, rateLimiterMiddleware, universityCoursesController.createUniversityCourse);

/**
 * @swagger
 * /api/university-courses:
 *   get:
 *     summary: Get all university courses for the authenticated user
 *     tags: [University Courses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for title or description
 *       - in: query
 *         name: difficulty
 *         schema:
 *           type: string
 *           enum: [beginner, intermediate, advanced]
 *         description: Filter by difficulty level
 *       - in: query
 *         name: language
 *         schema:
 *           type: string
 *         description: Filter by language
 *     responses:
 *       200:
 *         description: University courses retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', authMiddleware, rateLimiterMiddleware, universityCoursesController.getUniversityCourses);

/**
 * @swagger
 * /api/university-courses/{id}:
 *   get:
 *     summary: Get a university course by ID
 *     tags: [University Courses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Course ID
 *     responses:
 *       200:
 *         description: University course retrieved successfully
 *       404:
 *         description: Course not found
 *       401:
 *         description: Unauthorized
 */
router.get('/:id', authMiddleware, rateLimiterMiddleware, universityCoursesController.getUniversityCourseById);

/**
 * @swagger
 * /api/university-courses/{id}:
 *   put:
 *     summary: Update a university course
 *     tags: [University Courses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Course ID
 *     responses:
 *       200:
 *         description: University course updated successfully
 *       404:
 *         description: Course not found
 *       401:
 *         description: Unauthorized
 */
router.put('/:id', authMiddleware, rateLimiterMiddleware, universityCoursesController.updateUniversityCourse);

/**
 * @swagger
 * /api/university-courses/{id}:
 *   delete:
 *     summary: Delete a university course
 *     tags: [University Courses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Course ID
 *     responses:
 *       200:
 *         description: University course deleted successfully
 *       404:
 *         description: Course not found
 *       401:
 *         description: Unauthorized
 */
router.delete('/:id', authMiddleware, rateLimiterMiddleware, universityCoursesController.deleteUniversityCourse);

/**
 * @swagger
 * /api/university-courses/{id}/progress/{moduleId}:
 *   post:
 *     summary: Update learning progress for a university course module
 *     tags: [University Courses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Course ID
 *       - in: path
 *         name: moduleId
 *         required: true
 *         schema:
 *           type: string
 *         description: Module ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - topicId
 *               - completed
 *             properties:
 *               topicId:
 *                 type: string
 *                 example: "topic-123"
 *               completed:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Progress updated successfully
 *       404:
 *         description: Course not found
 *       401:
 *         description: Unauthorized
 */
router.post('/:id/progress/:moduleId', authMiddleware, rateLimiterMiddleware, universityCoursesController.updateProgress);

/**
 * @swagger
 * /api/university-courses/{id}/stats:
 *   get:
 *     summary: Get learning statistics for a university course
 *     tags: [University Courses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Course ID
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *       404:
 *         description: Course not found
 *       401:
 *         description: Unauthorized
 */
router.get('/:id/stats', authMiddleware, rateLimiterMiddleware, universityCoursesController.getCourseStats);

export default router;
