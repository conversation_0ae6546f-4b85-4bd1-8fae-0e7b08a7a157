import { Router } from 'express';
import { <PERSON><PERSON><PERSON>ontroller } from '../controllers/mfa.controller';
import { SetupMFADto, VerifyMFADto, DisableMFADto, UseBackupCodeDto } from '../dtos/auth.dto';
import { authMiddleware, detectSuspiciousActivity, rateLimiterMiddleware, validationMiddleware } from '@/middlewares';

const router = Router();

/**
 * @swagger
 * /mfa/status:
 *   get:
 *     summary: Get MFA status for the authenticated user
 *     tags: [MFA]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: MFA status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     isEnabled:
 *                       type: boolean
 *                     backupCodesRemaining:
 *                       type: number
 *                     enabledAt:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized
 */
router.get('/status', authMiddleware, MFAController.getStatus);

/**
 * @swagger
 * /mfa/setup/initialize:
 *   post:
 *     summary: Initialize MFA setup
 *     tags: [MFA]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: MFA setup initialized successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     qrCodeUrl:
 *                       type: string
 *                     backupCodes:
 *                       type: array
 *                       items:
 *                         type: string
 *                     setupToken:
 *                       type: string
 *       400:
 *         description: MFA already enabled or other error
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/setup/initialize',
  authMiddleware,
  detectSuspiciousActivity,
  rateLimiterMiddleware,
  MFAController.initializeSetup,
);

/**
 * @swagger
 * /mfa/setup/complete:
 *   post:
 *     summary: Complete MFA setup by verifying TOTP code
 *     tags: [MFA]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               totpCode:
 *                 type: string
 *                 minLength: 6
 *                 maxLength: 6
 *               setupToken:
 *                 type: string
 *             required:
 *               - totpCode
 *               - setupToken
 *     responses:
 *       200:
 *         description: MFA setup completed successfully
 *       400:
 *         description: Invalid TOTP code or setup token
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/setup/complete',
  authMiddleware,
  detectSuspiciousActivity,
  rateLimiterMiddleware,
  validationMiddleware(SetupMFADto),
  MFAController.completeSetup,
);

/**
 * @swagger
 * /mfa/verify/totp:
 *   post:
 *     summary: Verify TOTP code for authentication
 *     tags: [MFA]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               totpCode:
 *                 type: string
 *                 minLength: 6
 *                 maxLength: 6
 *             required:
 *               - totpCode
 *     responses:
 *       200:
 *         description: TOTP code verified successfully
 *       400:
 *         description: Invalid TOTP code
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/verify/totp',
  authMiddleware,
  detectSuspiciousActivity,
  rateLimiterMiddleware,
  validationMiddleware(VerifyMFADto),
  MFAController.verifyTOTP,
);

/**
 * @swagger
 * /mfa/verify/backup-code:
 *   post:
 *     summary: Verify backup code for authentication
 *     tags: [MFA]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               backupCode:
 *                 type: string
 *                 minLength: 8
 *             required:
 *               - backupCode
 *     responses:
 *       200:
 *         description: Backup code verified successfully
 *       400:
 *         description: Invalid or used backup code
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/verify/backup-code',
  authMiddleware,
  detectSuspiciousActivity,
  rateLimiterMiddleware,
  validationMiddleware(UseBackupCodeDto),
  MFAController.verifyBackupCode,
);

/**
 * @swagger
 * /mfa/disable:
 *   post:
 *     summary: Disable MFA for the user
 *     tags: [MFA]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               totpCode:
 *                 type: string
 *                 minLength: 6
 *                 maxLength: 6
 *             required:
 *               - totpCode
 *     responses:
 *       200:
 *         description: MFA disabled successfully
 *       400:
 *         description: Invalid TOTP code
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/disable',
  authMiddleware,
  detectSuspiciousActivity,
  rateLimiterMiddleware,
  validationMiddleware(DisableMFADto),
  MFAController.disableMFA,
);

/**
 * @swagger
 * /mfa/backup-codes/regenerate:
 *   post:
 *     summary: Regenerate backup codes
 *     tags: [MFA]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               totpCode:
 *                 type: string
 *                 minLength: 6
 *                 maxLength: 6
 *             required:
 *               - totpCode
 *     responses:
 *       200:
 *         description: Backup codes regenerated successfully
 *       400:
 *         description: Invalid TOTP code
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/backup-codes/regenerate',
  authMiddleware,
  detectSuspiciousActivity,
  rateLimiterMiddleware,
  validationMiddleware(VerifyMFADto),
  MFAController.regenerateBackupCodes,
);

export default router;
