import { Router } from 'express';
import { AuthController } from '../controllers/auth.controller';
import { UserController } from '../controllers/user.controller';
import { CreateUserDto, UpdateUserDto } from '../dtos/user.dto';
import { LoginDto } from '../dtos/auth.dto';
import validationMiddleware from '../middlewares/validation.middleware';
import authMiddleware from '../middlewares/auth.middleware';
import { permissionMiddleware } from '../middlewares/authorization.middleware';
import { Resource, Action } from '../types/rbac';

const router = Router();
const userController = new UserController();

/**
 * Public routes (no authentication required)
 */

/**
 * @swagger
 * /api/v1/users:
 *   post:
 *     summary: Register a new user
 *     description: Create a new user account (public registration)
 *     tags:
 *       - Users
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterDto'
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationError'
 *       409:
 *         description: User already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/', validationMiddleware(CreateUserDto), userController.createUser);

/**
 * @swagger
 * /api/v1/users/login:
 *   post:
 *     summary: User login
 *     description: Authenticate a user and receive JWT token
 *     tags:
 *       - Users
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginDto'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationError'
 *       401:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/login', validationMiddleware(LoginDto), AuthController.logIn);

/**
 * Protected routes (authentication required)
 */

/**
 * @swagger
 * /api/v1/users/profile:
 *   get:
 *     summary: Get current user profile
 *     description: Retrieve the authenticated user's profile information
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserResponse'
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/profile', authMiddleware, userController.getUserById);

/**
 * @swagger
 * /api/v1/users/search:
 *   get:
 *     summary: Search users
 *     description: Search for users (requires authentication)
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Users found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UsersListResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/search', authMiddleware, userController.getUsers);

/**
 * @swagger
 * /api/v1/users/{id}:
 *   get:
 *     summary: Get user by ID
 *     description: Retrieve user information by ID (self or admin access)
 *     tags:
 *       - Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserResponse'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Forbidden - Cannot access other user's data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id', authMiddleware, userController.getUserById);

// Update user (self or admin access)
router.put(
  '/:id',
  authMiddleware,
  validationMiddleware(UpdateUserDto, true), // skipMissingProperties = true for partial updates
  userController.updateUser,
);

// Delete user (self or admin access)
router.delete('/:id', authMiddleware, userController.deleteUser);

/**
 * Admin-only routes (require admin role and specific permissions)
 */

// Get all users with filters and pagination
router.get('/', authMiddleware, permissionMiddleware(Resource.USER, Action.READ), userController.getUsers);

// Get users by role
router.get('/role/:role', authMiddleware, permissionMiddleware(Resource.USER, Action.READ), userController.getUsers);

// Get user statistics
router.get('/admin/stats', authMiddleware, permissionMiddleware(Resource.USER, Action.READ), userController.getUsers);

// Update user role (admin only)
router.patch(
  '/:id/role',
  authMiddleware,
  permissionMiddleware(Resource.USER, Action.UPDATE),
  userController.updateUser,
);

// Get user by email (admin only for privacy)
router.get('/email/:email', authMiddleware, permissionMiddleware(Resource.USER, Action.READ), userController.getUsers);

export default router;
