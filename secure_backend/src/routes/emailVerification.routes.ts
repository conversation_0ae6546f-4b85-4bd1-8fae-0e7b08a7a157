import { Router } from 'express';
import { EmailVerificationController } from '../controllers/emailVerification.controller';
import { authMiddleware, validationMiddleware } from '../middlewares';
import {
  emailVerificationRateLimit,
  securityHeaders,
  sanitizeInput,
  detectSuspiciousActivity,
  deviceFingerprint,
} from '../middlewares/security.middleware';
import { VerifyEmailDto, ResendVerificationDto, ChangeEmailDto } from '../dtos';

const router = Router();

// Apply security headers to all routes
router.use(securityHeaders);

// Apply input sanitization to all routes
router.use(sanitizeInput);

// Apply device fingerprinting to all routes
router.use(deviceFingerprint);

/**
 * PUBLIC ROUTES (No authentication required)
 */

/**
 * @swagger
 * /email-verification/verify:
 *   post:
 *     summary: Verify email address using verification token
 *     description: Verifies a user's email address using a token sent via email. Can be used for both initial email verification and email change confirmation.
 *     tags: [Email Verification]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/VerifyEmailDto'
 *     parameters:
 *       - in: query
 *         name: token
 *         schema:
 *           type: string
 *         description: Verification token (alternative to body parameter)
 *     responses:
 *       200:
 *         description: Email verified successfully
 *       400:
 *         description: Invalid or expired token
 *       429:
 *         description: Too many requests
 */
router.post(
  '/verify',
  detectSuspiciousActivity,
  emailVerificationRateLimit,
  validationMiddleware(VerifyEmailDto),
  EmailVerificationController.verify,
);

/**
 * @swagger
 * /email-verification/resend:
 *   post:
 *     summary: Resend email verification
 *     description: Resends email verification. Can be used by authenticated users or by providing an email address for unauthenticated requests.
 *     tags: [Email Verification]
 *     security:
 *       - cookieAuth: []
 *       - {}
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ResendVerificationDto'
 *     responses:
 *       200:
 *         description: Verification email sent successfully
 *       400:
 *         description: Email required for unauthenticated requests
 *       404:
 *         description: User not found
 *       429:
 *         description: Rate limited - too many requests
 */
router.post(
  '/resend',
  detectSuspiciousActivity,
  emailVerificationRateLimit,
  validationMiddleware(ResendVerificationDto),
  (req, res, next) => {
    // Optional authentication - proceed even if no token
    try {
      authMiddleware(req, res, err => {
        if (err) {
          // If auth fails, continue without authentication
          (req as any).user = undefined;
        }
        next();
      });
    } catch {
      // If auth middleware throws, continue without authentication
      (req as any).user = undefined;
      next();
    }
  },
  EmailVerificationController.resend,
);

/**
 * PROTECTED ROUTES (Authentication required)
 */

/**
 * @swagger
 * /email-verification/change-email:
 *   post:
 *     summary: Initiate email address change
 *     description: Initiates an email address change process. Sets pendingEmail and sends verification email to the new address. The email change is finalized when the verification token is used.
 *     tags: [Email Verification]
 *     security:
 *       - cookieAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ChangeEmailDto'
 *     responses:
 *       200:
 *         description: Email change verification sent successfully
 *       400:
 *         description: Invalid request or new email same as current
 *       401:
 *         description: Authentication required
 *       409:
 *         description: Email already in use by another account
 *       429:
 *         description: Rate limited - too many requests
 */
router.post(
  '/change-email',
  authMiddleware,
  detectSuspiciousActivity,
  emailVerificationRateLimit,
  validationMiddleware(ChangeEmailDto),
  EmailVerificationController.changeEmail,
);

/**
 * @swagger
 * /email-verification/status:
 *   get:
 *     summary: Get email verification status
 *     description: Returns the current email verification status for the authenticated user, including pending email changes and rate limiting information.
 *     tags: [Email Verification]
 *     security:
 *       - cookieAuth: []
 *     responses:
 *       200:
 *         description: Email verification status retrieved successfully
 *       401:
 *         description: Authentication required
 */
router.get('/status', authMiddleware, EmailVerificationController.getStatus);

export default router;
