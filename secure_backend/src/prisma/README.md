# Prisma Setup and Data Access Layer

This directory contains the Prisma setup and Data Access Layer (DAL) implementation for the secure backend application.

## 📁 Files Overview

### Core Files
- `schema.prisma` - Prisma schema definition with PostgreSQL datasource and User model
- `../loaders/prisma.ts` - Singleton PrismaClient loader with graceful shutdown
- `../loaders/dal.ts` - Data Access Layer helpers for common database operations
- `../repositories/user.repository.ts` - Example User repository implementation

### Example Files
- `../examples/prisma-usage.ts` - Complete usage examples demonstrating all features

## 🔧 Implementation Details

### 1. Database Schema (`schema.prisma`)

```prisma
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id                  String    @id @default(cuid())
  email               String    @unique
  passwordHash        String
  emailVerified       DateTime?
  username            String?   @unique
  provider            String?
  role                Role      @default(USER)
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt
  logs                AuditLog[]

  @@map("users")
}

model AuditLog {
  id        String      @id @default(cuid())
  userId    String
  user      User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  action    AuditAction
  ipAddress String?
  userAgent String?
  createdAt DateTime    @default(now())

  @@map("audit_logs")
}

enum Role {
  USER
  ADMIN
  SUPER_ADMIN
  MODERATOR
}

enum AuditAction {
  LOGIN_SUCCESS
  LOGIN_FAILURE
  LOGOUT
  PASSWORD_RESET_REQUEST
  PASSWORD_RESET_SUCCESS
  EMAIL_CHANGE_REQUEST
  EMAIL_CHANGE_SUCCESS
  EMAIL_VERIFIED
  OAUTH_LOGIN_SUCCESS
  OAUTH_LOGIN_FAILURE
}
```

### 2. Prisma Loader (`loaders/prisma.ts`)

Features:
- ✅ Singleton pattern for PrismaClient instance
- ✅ Environment-based logging configuration
- ✅ Graceful shutdown handling
- ✅ Database connection management
- ✅ Health check functionality
- ✅ Process signal handlers (SIGINT, SIGTERM)
- ✅ Error handling for uncaught exceptions

```typescript
import { prisma, connectDatabase, disconnectDatabase } from './loaders';

// Use the singleton instance
const users = await prisma.user.findMany();

// Manual connection management
await connectDatabase();
await disconnectDatabase();
```

### 3. Data Access Layer (`loaders/dal.ts`)

Helper functions for common database operations:
- ✅ Transaction management with timeout configuration
- ✅ Raw SQL query execution
- ✅ Batch operations
- ✅ Generic pagination
- ✅ Soft delete operations
- ✅ Upsert operations
- ✅ Bulk create operations
- ✅ Database health checks

```typescript
import { transaction, paginate, batch } from './loaders';

// Use transaction
await transaction(async (tx) => {
  // Database operations within transaction
});

// Use pagination
const result = await paginate(prisma.user, { where: { role: 'USER' } }, 1, 10);

// Batch operations
await batch([
  prisma.user.create({ data: user1Data }),
  prisma.user.create({ data: user2Data })
]);
```

### 4. User Repository (`repositories/user.repository.ts`)

Complete CRUD repository with:
- ✅ Type-safe interfaces for data operations
- ✅ Comprehensive error handling
- ✅ Prisma error code mapping
- ✅ Logging integration
- ✅ Filter and pagination support
- ✅ Bulk operations
- ✅ Upsert functionality

```typescript
import { UserRepository } from './repositories/user.repository';

// Create user
const user = await UserRepository.create({
  email: '<EMAIL>',
  passwordHash: 'hashed_password',
  role: Role.USER
});

// Find with pagination and filters
const result = await UserRepository.findMany(
  { role: Role.USER },
  1, // page
  10 // limit
);
```

## 🚀 Usage Examples

### Basic Connection
```typescript
import { connectDatabase, databaseHealthCheck } from './loaders';

await connectDatabase();
const isHealthy = await databaseHealthCheck();
```

### CRUD Operations
```typescript
import { UserRepository } from './repositories/user.repository';

// Create
const user = await UserRepository.create({
  email: '<EMAIL>',
  passwordHash: 'hash123'
});

// Read
const foundUser = await UserRepository.findByEmail('<EMAIL>');
const users = await UserRepository.findMany({ role: Role.USER });

// Update
await UserRepository.update(user.id, { role: Role.ADMIN });

// Delete
await UserRepository.delete(user.id);
```

### Transactions
```typescript
import { transaction } from './loaders';

const result = await transaction(async (tx) => {
  const user1 = await tx.user.create({ data: userData1 });
  const user2 = await tx.user.create({ data: userData2 });
  return { user1, user2 };
});
```

### Bulk Operations
```typescript
import { UserRepository } from './repositories/user.repository';

// Bulk create
const result = await UserRepository.createMany([
  { email: '<EMAIL>', passwordHash: 'hash1' },
  { email: '<EMAIL>', passwordHash: 'hash2' }
]);
```

## 🛡️ Error Handling

The implementation includes comprehensive error handling:
- Prisma error codes are mapped to meaningful messages
- Database connection errors are logged and re-thrown
- Transaction errors trigger automatic rollback
- Graceful shutdown prevents data corruption

## 🔄 Graceful Shutdown

The Prisma loader automatically handles:
- SIGINT and SIGTERM signals
- Uncaught exceptions
- Unhandled promise rejections
- Proper database disconnection

## 📊 Logging

Integrated logging for:
- Database queries (development only)
- Connection status
- Error conditions
- Transaction lifecycle
- Operation performance metrics

## 🧪 Testing

See `examples/prisma-usage.ts` for complete working examples of all features.

To run examples:
```bash
npx ts-node src/examples/prisma-usage.ts
```

## 🔧 Commands

```bash
# Generate Prisma client
npx prisma generate

# Create and apply migrations
npx prisma migrate dev --name init

# View database in Prisma Studio
npx prisma studio

# Seed database (if seeder is created)
npx prisma db seed
```

## 📝 Environment Variables

Required environment variables:
```env
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"
NODE_ENV=development # or production
```

## 🔒 Security Features

- Password fields use `passwordHash` (not plain text)
- Unique constraints on email fields
- Role-based access control ready
- SQL injection protection via Prisma
- Transaction isolation levels supported

## 📈 Performance Features

- Connection pooling via PrismaClient
- Query optimization hints
- Batch operations for bulk data
- Efficient pagination
- Raw SQL support for complex queries

This implementation provides a robust, type-safe, and production-ready data access layer for the secure backend application.
