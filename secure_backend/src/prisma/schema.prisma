generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  username      String?   @unique
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  passwordHash  String?
  role          Role      @default(USER)
  accounts      Account[]
  sessions      Session[]
  logs          AuditLog[]
  passwordResetTokens PasswordResetToken[]
  mfaSetup      MFASetup?
  mfaBackupCodes MFABackupCode[]

  // Enhanced session management relations
  userSessions        UserSession[]
  trustedDevices      TrustedDevice[]
  sessionActivities   SessionActivity[]

  // Login attempt tracking for brute-force prevention
  failedLoginAttempts Int       @default(0)
  lockoutExpires      DateTime?

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String  // "oauth", "email", etc
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token         String?
  session_state    String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Enhanced session management with device tracking
model UserSession {
  id                String    @id @default(cuid())
  userId            String
  sessionToken      String    @unique
  deviceId          String    // Unique device identifier
  deviceName        String?   // User-friendly device name
  deviceType        String?   // mobile, desktop, tablet, etc.
  userAgent         String
  ipAddress         String
  location          String?   // City, Country (if available)
  isActive          Boolean   @default(true)
  isTrusted         Boolean   @default(false)
  lastActivity      DateTime  @default(now())
  createdAt         DateTime  @default(now())
  expiresAt         DateTime
  terminatedAt      DateTime?
  terminatedBy      String?   // 'user', 'admin', 'system', 'security'
  terminationReason String?   // 'logout', 'timeout', 'suspicious', 'admin_action'

  // Security tracking
  loginAttempts     Int       @default(0)
  suspiciousActivity Boolean  @default(false)
  riskScore         Float     @default(0.0)

  // Relationships
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  activities        SessionActivity[]

  @@index([userId])
  @@index([deviceId])
  @@index([isActive])
  @@index([lastActivity])
  @@index([suspiciousActivity])
  @@map("user_sessions")
}

// Device trust management
model TrustedDevice {
  id              String   @id @default(cuid())
  userId          String
  deviceId        String
  deviceName      String
  deviceType      String?
  fingerprint     String   // Device fingerprint hash
  userAgent       String
  firstSeen       DateTime @default(now())
  lastSeen        DateTime @default(now())
  trustLevel      String   @default("pending") // pending, trusted, suspicious, blocked
  trustedAt       DateTime?
  trustedBy       String?  // 'user', 'admin', 'auto'
  isActive        Boolean  @default(true)

  // Relationships
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, deviceId])
  @@index([userId])
  @@index([deviceId])
  @@index([trustLevel])
  @@map("trusted_devices")
}

// Session activity log
model SessionActivity {
  id          String   @id @default(cuid())
  sessionId   String
  userId      String
  action      String   // login, logout, activity, suspicious, terminated
  details     Json?    // Additional activity details
  ipAddress   String
  userAgent   String
  timestamp   DateTime @default(now())
  riskScore   Float    @default(0.0)

  // Relationships
  session     UserSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([sessionId])
  @@index([userId])
  @@index([action])
  @@index([timestamp])
  @@map("session_activities")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model PasswordResetToken {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token       String   @unique
  expires     DateTime
  isUsed      Boolean  @default(false)
  requestedAt DateTime @default(now())
  usedAt      DateTime?
  ipAddress   String?
  userAgent   String?

  @@map("password_reset_tokens")
}

model MFASetup {
  id           String   @id @default(cuid())
  userId       String   @unique
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  secret       String   // TOTP secret (encrypted)
  isEnabled    Boolean  @default(false)
  backupCodes  String[] // Encrypted backup codes
  qrCodeUrl    String?  // QR code data URL for setup
  setupToken   String?  // Temporary token for setup verification
  setupExpires DateTime?
  enabledAt    DateTime?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("mfa_setups")
}

model MFABackupCode {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  code      String   // Hashed backup code
  isUsed    Boolean  @default(false)
  usedAt    DateTime?
  createdAt DateTime @default(now())

  @@unique([userId, code])
  @@map("mfa_backup_codes")
}

// Historical Logging for Auditing
model AuditLog {
  id        String      @id @default(cuid())
  userId    String
  user      User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  action    AuditAction
  ipAddress String?
  userAgent String?
  createdAt DateTime    @default(now())

  @@map("audit_logs")
}

enum Role {
  USER
  ADMIN
}

enum AuditAction {
  LOGIN_SUCCESS
  LOGIN_FAILURE
  LOGOUT
  PASSWORD_RESET_REQUEST
  PASSWORD_RESET_SUCCESS
  EMAIL_CHANGE_REQUEST
  EMAIL_CHANGE_SUCCESS
  EMAIL_VERIFIED
  OAUTH_LOGIN_SUCCESS
  OAUTH_LOGIN_FAILURE
}
