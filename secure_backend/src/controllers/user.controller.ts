import { Request, Response } from 'express';
import { Container } from 'typedi';
import { Role } from '@prisma/client';
import { BaseController } from './base.controller';
import { UserService } from '../services/user.service';
import { CreateUserDto, UpdateUserDto } from '../dtos/user.dto';
import { <PERSON>rrorHandler, ErrorCodes } from '../utils/errorHandler';
import { HttpException } from '../exceptions/HttpException';
import { UserFilters } from '../repositories/user.repository';
import { User as UserInterface } from '../interfaces/user.interface';

export class UserController extends BaseController {
  private userService: UserService;

  constructor() {
    super();
    this.userService = Container.get(UserService);
  }

  public getUsers = this.catchAsync(async (req: Request, res: Response) => {
    const page = parseInt(req.query['page'] as string) || 1;
    const limit = parseInt(req.query['limit'] as string) || 10;
    const filters: UserFilters = {};
    if (req.query['email']) filters.email = req.query['email'] as string;
    if (req.query['role']) filters.role = req.query['role'] as Role;
    if (req.query['username']) filters.username = req.query['username'] as string;

    const result = await this.userService.findAllUser(filters, page, limit);
    this.sendResponse(res, 200, result);
  });

  public getUserById = this.catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    if (!id) {
      throw ErrorHandler.badRequest('User ID is required', ErrorCodes.MISSING_REQUIRED_FIELD);
    }
    const user = await this.userService.findUserById(id);
    if (!user) {
      throw ErrorHandler.notFound('User not found', ErrorCodes.RESOURCE_NOT_FOUND);
    }
    this.sendResponse(res, 200, user);
  });

  public createUser = this.catchAsync(async (req: Request, res: Response) => {
    const user = await this.userService.createUser(req.body as CreateUserDto);
    this.sendResponse(res, 201, user, 'User created successfully');
  });

  public updateUser = this.catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    if (!id) {
      throw new HttpException(400, 'User ID is required');
    }
    const requestingUser = req.user;

    if ((requestingUser as UserInterface)?.id !== id && (requestingUser as UserInterface)?.role !== Role.ADMIN) {
      throw new HttpException(403, 'Forbidden');
    }

    const user = await this.userService.updateUser(id, req.body as UpdateUserDto);
    this.sendResponse(res, 200, user, 'User updated successfully');
  });

  public deleteUser = this.catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    if (!id) {
      throw ErrorHandler.badRequest('User ID is required', ErrorCodes.MISSING_REQUIRED_FIELD);
    }
    const requestingUser = req.user as UserInterface;

    // Check if user can delete this account
    if (requestingUser?.id !== id && requestingUser?.role !== Role.ADMIN) {
      throw ErrorHandler.forbidden('You can only delete your own account', ErrorCodes.INSUFFICIENT_PERMISSIONS);
    }

    // Check if user exists before attempting deletion
    const existingUser = await this.userService.findUserById(id);
    if (!existingUser) {
      throw ErrorHandler.notFound('User not found', ErrorCodes.RESOURCE_NOT_FOUND);
    }

    await this.userService.deleteUser(id);
    this.sendResponse(res, 200, { id }, 'User deleted successfully');
  });
}
