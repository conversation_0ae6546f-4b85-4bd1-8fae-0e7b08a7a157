import { Request, Response, NextFunction } from 'express';
import { Container } from 'typedi';
import { UniversityCoursesService } from '@/services/university-courses.service';
import { 
  GenerateUniversityCourseDto,
  CreateUniversityCourseDto,
  UpdateUniversityCourseDto,
  GetUniversityCoursesQueryDto
} from '@/dtos/learning.dto';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { HttpException } from '@/exceptions/HttpException';

export class UniversityCoursesController {
  private universityCoursesService = Container.get(UniversityCoursesService);

  public generateUniversityCourse = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const dto = plainToClass(GenerateUniversityCourseDto, req.body);
      const errors = await validate(dto);
      
      if (errors.length > 0) {
        throw new HttpException(400, 'Validation failed');
      }

      const result = await this.universityCoursesService.generateUniversityCourse(userId, dto);
      
      res.status(200).json({
        success: true,
        data: result,
        message: 'University course generated successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public createUniversityCourse = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const dto = plainToClass(CreateUniversityCourseDto, req.body);
      const errors = await validate(dto);
      
      if (errors.length > 0) {
        throw new HttpException(400, 'Validation failed');
      }

      const course = await this.universityCoursesService.createUniversityCourse(userId, dto);
      
      res.status(201).json({
        success: true,
        data: course,
        message: 'University course created successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public getUniversityCourses = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const queryDto = plainToClass(GetUniversityCoursesQueryDto, req.query);
      const errors = await validate(queryDto);
      
      if (errors.length > 0) {
        throw new HttpException(400, 'Invalid query parameters');
      }

      const result = await this.universityCoursesService.getUniversityCourses(
        userId,
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.difficulty,
        queryDto.language
      );
      
      res.status(200).json({
        success: true,
        data: result.courses,
        pagination: {
          page: queryDto.page || 1,
          limit: queryDto.limit || 10,
          total: result.total,
          pages: Math.ceil(result.total / (queryDto.limit || 10))
        },
        message: 'University courses retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public getUniversityCourseById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Course ID is required');
      }

      const course = await this.universityCoursesService.getUniversityCourseById(userId, id);
      
      res.status(200).json({
        success: true,
        data: course,
        message: 'University course retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public updateUniversityCourse = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Course ID is required');
      }

      const dto = plainToClass(UpdateUniversityCourseDto, req.body);
      const errors = await validate(dto);
      
      if (errors.length > 0) {
        throw new HttpException(400, 'Validation failed');
      }

      const course = await this.universityCoursesService.updateUniversityCourse(userId, id, dto);
      
      res.status(200).json({
        success: true,
        data: course,
        message: 'University course updated successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public deleteUniversityCourse = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Course ID is required');
      }

      await this.universityCoursesService.deleteUniversityCourse(userId, id);
      
      res.status(200).json({
        success: true,
        message: 'University course deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public updateProgress = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id, moduleId } = req.params;
      const { topicId, completed } = req.body;

      if (!id || !moduleId || !topicId || typeof completed !== 'boolean') {
        throw new HttpException(400, 'Course ID, module ID, topic ID, and completed status are required');
      }

      const progress = await this.universityCoursesService.updateProgress(
        userId, 
        id, 
        moduleId, 
        topicId, 
        completed
      );
      
      res.status(200).json({
        success: true,
        data: progress,
        message: 'Progress updated successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public getCourseStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Course ID is required');
      }

      const stats = await this.universityCoursesService.getCourseStats(userId, id);
      
      res.status(200).json({
        success: true,
        data: stats,
        message: 'Course statistics retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  };
}
