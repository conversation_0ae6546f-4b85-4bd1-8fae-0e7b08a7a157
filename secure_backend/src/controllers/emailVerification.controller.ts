import { Request, Response, NextFunction } from 'express';
import { EmailVerificationService, EmailVerificationContext } from '../services/emailVerification.service';
import { AuthService } from '../services/auth.service';
import { HttpException } from '../exceptions/HttpException';
import { BaseController } from './base.controller';
import { User as UserInterface } from '../interfaces/user.interface';

/**
 * Controller handling email verification operations
 * Provides endpoints for verifying email addresses, resending verification emails,
 * and initiating email address changes
 */
export class EmailVerificationController extends BaseController {
  private static emailVerificationService = new EmailVerificationService();

  /**
   * Extract context information from request for audit logging
   */
  private static extractContext(req: Request): EmailVerificationContext {
    const context: EmailVerificationContext = {};
    if (req.ip) context.ip = req.ip;
    const userAgent = req.get('User-Agent');
    if (userAgent) context.userAgent = userAgent;
    const deviceFingerprint = (req as unknown as Record<string, unknown>)['deviceFingerprint'] as string | undefined;
    if (deviceFingerprint) context.deviceFingerprint = deviceFingerprint;
    return context;
  }

  /**
   * @swagger
   * /email-verification/verify:
   *   post:
   *     summary: Verify email address using verification token
   *     description: Verifies a user's email address using a token sent via email. Can be used for both initial email verification and email change confirmation.
   *     tags: [Email Verification]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/VerifyEmailDto'
   *     parameters:
   *       - in: query
   *         name: token
   *         schema:
   *           type: string
   *         description: Verification token (alternative to body parameter)
   *     responses:
   *       200:
   *         description: Email verified successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: Email verified successfully
   *                 data:
   *                   type: object
   *                   properties:
   *                     user:
   *                       $ref: '#/components/schemas/User'
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       400:
   *         description: Invalid or expired token
   *       500:
   *         description: Internal server error
   */
  static async verify(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Get token from either body or query parameters
      const { token } = req.body.token ? req.body : req.query;

      if (!token) {
        throw new HttpException(400, 'Verification token is required');
      }

      const context = EmailVerificationController.extractContext(req);
      const result = await EmailVerificationController.emailVerificationService.verifyEmail(token, context);

      res.status(200).json({
        success: true,
        message: result.message,
        data: {
          user: result.user,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * @swagger
   * /email-verification/resend:
   *   post:
   *     summary: Resend email verification
   *     description: Resends email verification. Can be used by authenticated users or by providing an email address for unauthenticated requests.
   *     tags: [Email Verification]
   *     security:
   *       - cookieAuth: []
   *       - {}
   *     requestBody:
   *       required: false
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/ResendVerificationDto'
   *     responses:
   *       200:
   *         description: Verification email sent successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: Verification email sent successfully
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       400:
   *         description: Email required for unauthenticated requests
   *       404:
   *         description: User not found
   *       429:
   *         description: Rate limited - too many requests
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: false
   *                 message:
   *                   type: string
   *                   example: Please wait 5 minutes before requesting another verification email
   *                 data:
   *                   type: object
   *                   properties:
   *                     canResendAfter:
   *                       type: string
   *                       format: date-time
   *                       description: When the next resend attempt is allowed
   *       500:
   *         description: Internal server error
   */
  static async resend(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email } = req.body;
      const user = req.user; // May be undefined for unauthenticated requests

      let targetUserId: string;

      if (user) {
        // Authenticated user - resend verification for their account
        targetUserId = (user as UserInterface).id;
      } else if (email) {
        // Unauthenticated user - find user by email
        const foundUser = await AuthService.findUserByEmail(email);
        if (!foundUser) {
          throw new HttpException(404, 'User not found');
        }
        targetUserId = foundUser.id;
      } else {
        throw new HttpException(400, 'Email is required for unauthenticated requests');
      }

      const context = EmailVerificationController.extractContext(req);
      const result = await EmailVerificationController.emailVerificationService.sendEmailVerification(
        targetUserId,
        context,
      );

      if (result.canResendAfter) {
        res.status(429).json({
          success: false,
          message: result.message,
          data: {
            canResendAfter: result.canResendAfter,
          },
          timestamp: new Date().toISOString(),
        });
      } else {
        res.status(200).json({
          success: true,
          message: result.message,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * @swagger
   * /email-verification/change-email:
   *   post:
   *     summary: Initiate email address change
   *     description: Initiates an email address change process. Sets pendingEmail and sends verification email to the new address. The email change is finalized when the verification token is used.
   *     tags: [Email Verification]
   *     security:
   *       - cookieAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/ChangeEmailDto'
   *     responses:
   *       200:
   *         description: Email change verification sent successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: Verification email sent to your new email address
   *                 data:
   *                   type: object
   *                   properties:
   *                     pendingEmail:
   *                       type: string
   *                       format: email
   *                       description: The new email address pending verification
   *                       example: <EMAIL>
   *                     currentEmail:
   *                       type: string
   *                       format: email
   *                       description: The current email address
   *                       example: <EMAIL>
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       400:
   *         description: Invalid request or new email same as current
   *       401:
   *         description: Authentication required
   *       409:
   *         description: Email already in use by another account
   *       429:
   *         description: Rate limited - too many requests
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: false
   *                 message:
   *                   type: string
   *                   example: Please wait 5 minutes before requesting another verification email
   *                 data:
   *                   type: object
   *                   properties:
   *                     canResendAfter:
   *                       type: string
   *                       format: date-time
   *                       description: When the next request is allowed
   *       500:
   *         description: Internal server error
   */
  static async changeEmail(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { newEmail } = req.body;
      const user = req.user!; // Guaranteed to exist due to authMiddleware

      if (!newEmail) {
        throw new HttpException(400, 'New email address is required');
      }

      if (newEmail === (user as UserInterface).email) {
        throw new HttpException(400, 'New email address must be different from current email');
      }

      const context = EmailVerificationController.extractContext(req);
      const result = await EmailVerificationController.emailVerificationService.sendEmailChangeVerification(
        (user as UserInterface).id,
        newEmail,
        context,
      );

      if (result.canResendAfter) {
        res.status(429).json({
          success: false,
          message: result.message,
          data: {
            canResendAfter: result.canResendAfter,
          },
          timestamp: new Date().toISOString(),
        });
      } else {
        res.status(200).json({
          success: true,
          message: result.message,
          data: {
            pendingEmail: newEmail,
            currentEmail: (user as UserInterface).email,
          },
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * @swagger
   * /email-verification/status:
   *   get:
   *     summary: Get email verification status
   *     description: Returns the current email verification status for the authenticated user, including pending email changes and rate limiting information.
   *     tags: [Email Verification]
   *     security:
   *       - cookieAuth: []
   *     responses:
   *       200:
   *         description: Email verification status retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: Verification status retrieved successfully
   *                 data:
   *                   type: object
   *                   properties:
   *                     isVerified:
   *                       type: boolean
   *                       description: Whether the current email is verified
   *                       example: true
   *                     pendingEmail:
   *                       type: string
   *                       format: email
   *                       description: Email address pending verification (if any)
   *                       example: <EMAIL>
   *                     canResendAfter:
   *                       type: string
   *                       format: date-time
   *                       description: When the next resend attempt is allowed (if rate limited)
   *                     tokenExpires:
   *                       type: string
   *                       format: date-time
   *                       description: When the current verification token expires
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *       401:
   *         description: Authentication required
   *       500:
   *         description: Internal server error
   */
  static async getStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!; // Guaranteed to exist due to authMiddleware

      const status = await EmailVerificationController.emailVerificationService.getVerificationStatus(
        (user as UserInterface).id,
      );

      res.status(200).json({
        success: true,
        message: 'Verification status retrieved successfully',
        data: status,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
}
