import { Request, Response, NextFunction } from 'express';
import { Container } from 'typedi';
import { SubjectsService } from '@/services/subjects.service';
import { 
  CreateSubjectDto, 
  GenerateCurriculumDto, 
  UpdateProgressDto, 
  UpdateSubjectDto,
  GetSubjectsQueryDto 
} from '@/dtos/learning.dto';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { HttpException } from '@/exceptions/HttpException';

export class SubjectsController {
  private subjectsService = Container.get(SubjectsService);

  public createSubject = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const dto = plainToClass(CreateSubjectDto, req.body);
      const errors = await validate(dto);
      
      if (errors.length > 0) {
        throw new HttpException(400, 'Validation failed');
      }

      const subject = await this.subjectsService.createSubject(userId, dto);
      
      res.status(201).json({
        success: true,
        data: subject,
        message: 'Subject created successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public getSubjects = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const queryDto = plainToClass(GetSubjectsQueryDto, req.query);
      const errors = await validate(queryDto);
      
      if (errors.length > 0) {
        throw new HttpException(400, 'Invalid query parameters');
      }

      const result = await this.subjectsService.getSubjects(
        userId,
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.language
      );
      
      res.status(200).json({
        success: true,
        data: result.subjects,
        pagination: {
          page: queryDto.page || 1,
          limit: queryDto.limit || 10,
          total: result.total,
          pages: Math.ceil(result.total / (queryDto.limit || 10))
        },
        message: 'Subjects retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public getSubjectById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Subject ID is required');
      }

      const subject = await this.subjectsService.getSubjectById(userId, id);
      
      res.status(200).json({
        success: true,
        data: subject,
        message: 'Subject retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public updateSubject = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Subject ID is required');
      }

      const dto = plainToClass(UpdateSubjectDto, req.body);
      const errors = await validate(dto);
      
      if (errors.length > 0) {
        throw new HttpException(400, 'Validation failed');
      }

      const subject = await this.subjectsService.updateSubject(userId, id, dto);
      
      res.status(200).json({
        success: true,
        data: subject,
        message: 'Subject updated successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public deleteSubject = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Subject ID is required');
      }

      await this.subjectsService.deleteSubject(userId, id);
      
      res.status(200).json({
        success: true,
        message: 'Subject deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public generateCurriculum = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Subject ID is required');
      }

      const dto = plainToClass(GenerateCurriculumDto, req.body);
      const errors = await validate(dto);
      
      if (errors.length > 0) {
        throw new HttpException(400, 'Validation failed');
      }

      const result = await this.subjectsService.generateCurriculum(
        userId, 
        id, 
        dto.subject, 
        dto.language || 'English'
      );
      
      res.status(200).json({
        success: true,
        data: result,
        message: 'Curriculum generated successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public updateProgress = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Subject ID is required');
      }

      const dto = plainToClass(UpdateProgressDto, req.body);
      const errors = await validate(dto);
      
      if (errors.length > 0) {
        throw new HttpException(400, 'Validation failed');
      }

      const progress = await this.subjectsService.updateProgress(userId, id, dto);
      
      res.status(200).json({
        success: true,
        data: progress,
        message: 'Progress updated successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public getProgress = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Subject ID is required');
      }

      const progress = await this.subjectsService.getProgress(userId, id);
      
      res.status(200).json({
        success: true,
        data: progress,
        message: 'Progress retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  };

  public getSubjectStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const { id } = req.params;
      if (!id) {
        throw new HttpException(400, 'Subject ID is required');
      }

      const stats = await this.subjectsService.getSubjectStats(userId, id);
      
      res.status(200).json({
        success: true,
        data: stats,
        message: 'Subject statistics retrieved successfully'
      });
    } catch (error) {
      next(error);
    }
  };
}
