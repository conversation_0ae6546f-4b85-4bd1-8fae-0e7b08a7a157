/**
 * Base template interfaces that applications extending this template should implement
 */

// Base resource data interface that all resource data should extend
export interface BaseResourceData {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  userId?: string;
  createdBy?: string;
  ownerId?: string;
}

// Application-specific resource data interface
// Applications should extend this interface for their specific resources
export interface AppResourceData extends BaseResourceData {
  [key: string]: unknown;
}

// User data interface that can be extended by applications
export interface BaseUserData {
  id: string;
  email: string;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

// Application-specific user data interface
export interface AppUserData extends BaseUserData {
  [key: string]: unknown;
}

// Service response interfaces
export interface ServiceResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Repository filter interface
export interface RepositoryFilters {
  [key: string]: unknown;
}

// Pagination result with strongly typed data
export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Permission condition value types
export type PermissionConditionValue = string | number | boolean | string[] | number[];

// RBAC resource data interface (generic for different resource types)
export interface RBACResourceData<TResource = AppResourceData> extends BaseResourceData {
  resourceType?: string;
  data?: TResource;
}

// Auth service context (can be extended for custom authentication flows)
export interface AuthContext<TUser = AppUserData> {
  user: TUser;
  token: string;
  permissions: string[];
  resources: string[];
}

// Validation context interface
export interface ValidationContext {
  body?: Record<string, unknown>;
  params?: Record<string, unknown>;
  query?: Record<string, unknown>;
  headers?: Record<string, unknown>;
}

// Middleware context (can be extended for custom middleware data)
export interface MiddlewareContext<TUser = AppUserData> {
  user?: TUser;
  permissions?: string[];
  metadata?: Record<string, unknown>;
}

// Logging context interface
export interface LoggingContext {
  userId?: string;
  action?: string;
  resource?: string;
  metadata?: Record<string, unknown>;
  timestamp?: Date;
}

// Configuration interface that applications can extend
export interface AppConfiguration {
  database: {
    url: string;
    ssl?: boolean;
    poolSize?: number;
  };
  auth: {
    jwtSecret: string;
    jwtExpiresIn: string;
    bcryptSaltRounds: number;
  };
  server: {
    port: number;
    origin: string;
    credentials: boolean;
  };
  rateLimit: {
    windowMs: number;
    max: number;
    message: string;
  };
  logging: {
    level: string;
    format: string;
    directory: string;
  };
  // Applications can extend this with their own configuration
  [key: string]: unknown;
}

// Template event interfaces (for potential event-driven features)
export interface BaseEvent {
  type: string;
  timestamp: Date;
  userId?: string;
  metadata?: Record<string, unknown>;
}

export interface AuthEvent extends BaseEvent {
  type:
    | 'AUTH_LOGIN'
    | 'AUTH_LOGOUT'
    | 'AUTH_REGISTER'
    | 'AUTH_PASSWORD_CHANGE'
    | 'AUTH_OAUTH_LOGIN'
    | 'EMAIL_VERIFICATION_FAILED'
    | 'EMAIL_VERIFICATION_SUCCESS';
  email: string;
}

export interface PermissionEvent extends BaseEvent {
  type: 'PERMISSION_CHECK' | 'PERMISSION_DENIED' | 'PERMISSION_GRANTED';
  resource: string;
  action: string;
  result: boolean;
}

// Error interfaces for consistent error handling
export interface AppError {
  code: string;
  message: string;
  statusCode: number;
  details?: Record<string, unknown>;
  stack?: string;
}

export interface ValidationError extends AppError {
  field: string;
  value: unknown;
  constraints: string[];
}

// Generic service interface that all services should implement
export interface BaseService<TEntity, TCreateInput, TUpdateInput, TFilters = RepositoryFilters> {
  create(input: TCreateInput): Promise<TEntity>;
  findById(id: string): Promise<TEntity | null>;
  findMany(filters?: TFilters, page?: number, limit?: number): Promise<PaginationResult<TEntity>>;
  update(id: string, input: TUpdateInput): Promise<TEntity>;
  delete(id: string): Promise<void>;
}

// Generic repository interface
export interface BaseRepository<TEntity, TCreateInput, TUpdateInput, TFilters = RepositoryFilters> {
  create(input: TCreateInput): Promise<TEntity>;
  findById(id: string): Promise<TEntity | null>;
  findMany(filters?: TFilters, page?: number, limit?: number): Promise<PaginationResult<TEntity>>;
  update(id: string, input: TUpdateInput): Promise<TEntity>;
  delete(id: string): Promise<void>;
  count(filters?: TFilters): Promise<number>;
}

// Type helpers for better type inference
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Generic API response wrapper
export interface ApiResponse<T = unknown> {
  success: boolean;
  message?: string;
  data?: T;
  error?: AppError;
  timestamp: Date;
  requestId?: string;
}

// Template metadata interface (for template versioning and info)
export interface TemplateMetadata {
  version: string;
  name: string;
  description: string;
  features: string[];
  requiredExtensions: string[];
}

// All types are already exported individually above
