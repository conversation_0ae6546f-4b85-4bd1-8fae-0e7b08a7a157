// Permission-based RBAC system
// Users can extend these permissions based on their application needs

import { PermissionConditionValue } from './template';

export enum Resource {
  USER = 'user',
  PROFILE = 'profile',
  PRODUCT = 'product',
  ORDER = 'order',
  PAYMENT = 'payment',
  ANALYTICS = 'analytics',
  SYSTEM = 'system',
  // Add more resources as needed
}

export enum Action {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  MANAGE = 'manage', // Full access (includes all CRUD operations)
  // Add more granular actions as needed
  APPROVE = 'approve',
  REJECT = 'reject',
  EXPORT = 'export',
  IMPORT = 'import',
}

export interface Permission {
  resource: Resource;
  actions: Action[];
  conditions?: PermissionCondition[];
}

export interface PermissionCondition {
  field: string;
  operator: 'eq' | 'ne' | 'in' | 'nin' | 'gt' | 'gte' | 'lt' | 'lte' | 'owns';
  value: PermissionConditionValue;
}

export interface RolePermissions {
  role: string;
  permissions: Permission[];
  description?: string;
}

// Pre-defined role templates that users can customize
export const DEFAULT_ROLE_PERMISSIONS: RolePermissions[] = [
  {
    role: 'SUPER_ADMIN',
    description: 'Full system access',
    permissions: [
      {
        resource: Resource.SYSTEM,
        actions: [Action.MANAGE],
      },
      {
        resource: Resource.USER,
        actions: [Action.MANAGE],
      },
      {
        resource: Resource.PROFILE,
        actions: [Action.MANAGE],
      },
      {
        resource: Resource.PRODUCT,
        actions: [Action.MANAGE],
      },
      {
        resource: Resource.ORDER,
        actions: [Action.MANAGE],
      },
      {
        resource: Resource.PAYMENT,
        actions: [Action.MANAGE],
      },
      {
        resource: Resource.ANALYTICS,
        actions: [Action.MANAGE],
      },
    ],
  },
  {
    role: 'ADMIN',
    description: 'Administrative access with some limitations',
    permissions: [
      {
        resource: Resource.USER,
        actions: [Action.CREATE, Action.READ, Action.UPDATE],
      },
      {
        resource: Resource.PROFILE,
        actions: [Action.READ, Action.UPDATE],
      },
      {
        resource: Resource.PRODUCT,
        actions: [Action.MANAGE],
      },
      {
        resource: Resource.ORDER,
        actions: [Action.READ, Action.UPDATE, Action.APPROVE, Action.REJECT],
      },
      {
        resource: Resource.ANALYTICS,
        actions: [Action.READ, Action.EXPORT],
      },
    ],
  },
  {
    role: 'MODERATOR',
    description: 'Content moderation and user management',
    permissions: [
      {
        resource: Resource.USER,
        actions: [Action.READ, Action.UPDATE],
        conditions: [
          {
            field: 'role',
            operator: 'ne',
            value: 'ADMIN',
          },
        ],
      },
      {
        resource: Resource.PRODUCT,
        actions: [Action.READ, Action.UPDATE, Action.APPROVE, Action.REJECT],
      },
      {
        resource: Resource.ORDER,
        actions: [Action.READ],
      },
    ],
  },
  {
    role: 'USER',
    description: 'Standard user access',
    permissions: [
      {
        resource: Resource.PROFILE,
        actions: [Action.READ, Action.UPDATE],
        conditions: [
          {
            field: 'userId',
            operator: 'owns',
            value: 'self', // Special value indicating the user owns this resource
          },
        ],
      },
      {
        resource: Resource.PRODUCT,
        actions: [Action.READ],
      },
      {
        resource: Resource.ORDER,
        actions: [Action.CREATE, Action.READ, Action.UPDATE],
        conditions: [
          {
            field: 'userId',
            operator: 'owns',
            value: 'self',
          },
        ],
      },
    ],
  },
];

// Permission check result
export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string | undefined;
}
