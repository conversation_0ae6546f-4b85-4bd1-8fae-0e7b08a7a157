import { AppError } from './template';

export interface ApiResponse<T = unknown> {
  success: boolean;
  message?: string;
  data?: T;
  error?: AppError;
  timestamp?: Date;
  requestId?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

import { PrismaClient } from '@prisma/client';

export type DatabaseTransactionClient = Parameters<Parameters<PrismaClient['$transaction']>[0]>[0];
