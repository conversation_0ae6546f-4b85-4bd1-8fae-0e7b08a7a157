
# Authorization Tests

This document outlines the `curl` commands for testing the authorization features of the application.

## Role-Based Access Control (RBAC)

### 1. Accessing a Protected Route with Sufficient Permissions

*   **Description:** A user with the `ADMIN` role should be able to access a route protected by `authorizationMiddleware([Role.ADMIN])`.
*   **Command:**
    ```bash
    curl -X GET \
      http://localhost:3000/api/v1/auth/users \
      -H 'Authorization: Bearer <ADMIN_ACCESS_TOKEN>'
    ```
*   **Expected Outcome:** A `200 OK` response with a list of users.

### 2. Accessing a Protected Route without Sufficient Permissions

*   **Description:** A user with the `USER` role should not be able to access a route protected by `authorizationMiddleware([Role.ADMIN])`.
*   **Command:**
    ```bash
    curl -X GET \
      http://localhost:3000/api/v1/auth/users \
      -H 'Authorization: Bearer <USER_ACCESS_TOKEN>'
    ```
*   **Expected Outcome:** A `403 Forbidden` response with an error message.

## Permission-Based Access Control

### 1. Accessing a Protected Route with a Specific Permission

*   **Description:** A user with the `USER:READ` permission should be able to access a route protected by `permissionMiddleware(Resource.USER, Action.READ)`.
*   **Command:**
    ```bash
    curl -X GET \
      http://localhost:3000/api/v1/auth/users-rbac \
      -H 'Authorization: Bearer <ACCESS_TOKEN_WITH_USER_READ_PERMISSION>'
    ```
*   **Expected Outcome:** A `200 OK` response with a list of users.

### 2. Accessing a Protected Route without a Specific Permission

*   **Description:** A user without the `USER:READ` permission should not be able to access a route protected by `permissionMiddleware(Resource.USER, Action.READ)`.
*   **Command:**
    ```bash
    curl -X GET \
      http://localhost:3000/api/v1/auth/users-rbac \
      -H 'Authorization: Bearer <ACCESS_TOKEN_WITHOUT_USER_READ_PERMISSION>'
    ```
*   **Expected Outcome:** A `403 Forbidden` response with an error message.

